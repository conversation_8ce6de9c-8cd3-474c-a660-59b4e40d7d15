@model IEnumerable<ColorOasisSystemWeb.Models.Purchasing.Supplier>

@{
    ViewData["Title"] = "Suppliers";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="nxl-content">
    <!-- [ page-header ] start -->
    <div class="page-header">
        <div class="page-header-left d-flex align-items-center">
            <div class="page-header-title">
                <h5 class="m-b-10">Suppliers</h5>
            </div>
            <ul class="breadcrumb">
                <li class="breadcrumb-item"><a href="@Url.Action("Index", "Home")">Home</a></li>
                <li class="breadcrumb-item">ERP</li>
                <li class="breadcrumb-item">Purchasing</li>
                <li class="breadcrumb-item">Suppliers</li>
            </ul>
        </div>
        <div class="page-header-right ms-auto">
            <div class="page-header-right-items">
                <div class="d-flex d-md-none">
                    <a href="javascript:void(0)" class="page-header-right-close-toggle">
                        <i class="feather-arrow-left me-2"></i>
                        <span>Back</span>
                    </a>
                </div>
                <div class="d-flex align-items-center gap-2 page-header-right-items-wrapper">
                    <a href="@Url.Action("Create")" class="btn btn-primary">
                        <i class="feather-plus me-2"></i>
                        <span>Add New Supplier</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
    <!-- [ page-header ] end -->

    <!-- [ Main Content ] start -->
    <div class="main-content">
        <div class="row">
            <div class="col-lg-12">
                <div class="card stretch stretch-full">
                    <div class="card-header">
                        <h5 class="card-title">Suppliers</h5>
                        <div class="card-header-action">
                            <div class="card-header-btn">
                                <div data-bs-toggle="tooltip" title="Refresh">
                                    <a href="javascript:void(0);" class="avatar-text avatar-md" onclick="location.reload();">
                                        <i class="feather-refresh-cw"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body custom-card-action p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0" id="suppliersTable">
                                <thead>
                                    <tr>
                                        <th>Code</th>
                                        <th>Name</th>
                                        <th>Contact</th>
                                        <th>Type</th>
                                        <th>Rating</th>
                                        <th>Status</th>
                                        <th class="text-end">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var supplier in Model)
                                    {
                                        <tr>
                                            <td>
                                                <div class="fw-bold text-dark">@supplier.SupplierCode</div>
                                            </td>
                                            <td>
                                                <div class="fw-bold text-dark">@supplier.CompanyName</div>
                                                <div class="fs-12 text-muted">@supplier.ContactPerson</div>
                                            </td>
                                            <td>
                                                <div class="fs-12">
                                                    <i class="feather-phone me-1"></i>@supplier.Phone<br>
                                                    <i class="feather-mail me-1"></i>@supplier.Email
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-soft-info text-info">@supplier.SupplierType</span>
                                            </td>
                                            <td>
                                                @{
                                                    var ratingClass = supplier.Rating >= 4 ? "text-success" : 
                                                                    supplier.Rating >= 3 ? "text-warning" : "text-danger";
                                                }
                                                <div class="@ratingClass">
                                                    @for (int i = 1; i <= 5; i++)
                                                    {
                                                        if (i <= supplier.Rating)
                                                        {
                                                            <i class="feather-star" style="fill: currentColor;"></i>
                                                        }
                                                        else
                                                        {
                                                            <i class="feather-star"></i>
                                                        }
                                                    }
                                                    <span class="ms-1">(@supplier.Rating.ToString("F1"))</span>
                                                </div>
                                            </td>
                                            <td>
                                                @switch (supplier.Status)
                                                {
                                                    case ColorOasisSystemWeb.Models.Purchasing.SupplierStatus.Active:
                                                        <span class="badge bg-soft-success text-success">Active</span>
                                                        break;
                                                    case ColorOasisSystemWeb.Models.Purchasing.SupplierStatus.Inactive:
                                                        <span class="badge bg-soft-secondary text-secondary">Inactive</span>
                                                        break;
                                                    case ColorOasisSystemWeb.Models.Purchasing.SupplierStatus.Blacklisted:
                                                        <span class="badge bg-soft-danger text-danger">Blacklisted</span>
                                                        break;
                                                    case ColorOasisSystemWeb.Models.Purchasing.SupplierStatus.Pending:
                                                        <span class="badge bg-soft-warning text-warning">Pending</span>
                                                        break;
                                                }
                                            </td>
                                            <td>
                                                <div class="hstack gap-2 justify-content-end">
                                                    <a href="@Url.Action("Details", new { id = supplier.Id })" class="avatar-text avatar-md" data-bs-toggle="tooltip" title="View Details">
                                                        <i class="feather-eye"></i>
                                                    </a>
                                                    <a href="@Url.Action("Edit", new { id = supplier.Id })" class="avatar-text avatar-md" data-bs-toggle="tooltip" title="Edit">
                                                        <i class="feather-edit-3"></i>
                                                    </a>
                                                    <a href="@Url.Action("Delete", new { id = supplier.Id })" class="avatar-text avatar-md" data-bs-toggle="tooltip" title="Delete">
                                                        <i class="feather-trash-2"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- [ Main Content ] end -->
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            $('#suppliersTable').DataTable({
                responsive: true,
                pageLength: 25,
                order: [[1, 'asc']],
                columnDefs: [
                    { orderable: false, targets: [6] }
                ]
            });
        });
    </script>
}
