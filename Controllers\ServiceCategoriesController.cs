using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using ColorOasisSystemWeb.Data;
using ColorOasisSystemWeb.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using ColorOasisSystemWeb.AdvancedNotification.Services;
using ColorOasisSystemWeb.Enums;
using ColorOasisSystemWeb.ViewModels.Service;
using ColorOasisSystemWeb.Models.Mappers;

namespace ColorOasisSystemWeb.Controllers
{
    [Authorize(Roles = "User, Admin")]
    public class ServiceCategoriesController : Controller
    {
        private readonly AppDbContext _context;
        private readonly UserManager<User> _userManager;
        private readonly NotificationService _notificationService;

        public ServiceCategoriesController(
            AppDbContext context,
            UserManager<User> userManager,
            NotificationService notificationService)
        {
            _context = context;
            _userManager = userManager;
            _notificationService = notificationService;
        }

        // GET: ServiceCategories
        public async Task<IActionResult> Index()
        {
            var serviceCategories = await _context.ServiceCategories.ToListAsync();
            var viewModels = ServiceCategoryMapper.ToViewModelList(serviceCategories);
            return View(viewModels);
        }

        // GET: ServiceCategories/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var serviceCategory = await _context.ServiceCategories
                .FirstOrDefaultAsync(m => m.Id == id);
            if (serviceCategory == null)
            {
                return NotFound();
            }

            var viewModel = ServiceCategoryMapper.ToViewModel(serviceCategory);
            return View(viewModel);
        }

        // GET: ServiceCategories/Create
        public IActionResult Create()
        {
            string datePart = DateTime.Now.ToString("ddMMyyyy");

            // Get the latest client code that matches today's date
            var lastClient =  _context.ServiceCategories
                .Where(c => c.Code.Contains(datePart))
                .OrderByDescending(c => c.Code)
                .FirstOrDefaultAsync().Result;

            // Initialize the sequence number
            int sequenceNumber = 1;

            if (lastClient != null)
            {
                // Extract the numeric part and increment it
                string lastSequence = lastClient.Code.Substring(16);
                sequenceNumber = int.Parse(lastSequence) + 1;
            }

            // Generate the new client code
            string newClientCode = $"SRVCCAT-{datePart}{sequenceNumber.ToString("D4")}";
            ViewBag.Code = newClientCode;
            var viewModel = new ServiceCategoryViewModel();
            return View(viewModel);
        }

        // POST: ServiceCategories/Create
        // To protect from overposting attacks, enable the specific properties you want to bind to.
        // For more details, see http://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("Id,Code,Name,NameEn")] ServiceCategoryViewModel viewModel)
        {
            ViewBag.Code = viewModel.Code;

            if (ModelState.IsValid)
            {
                var serviceCategory = ServiceCategoryMapper.FromViewModel(viewModel);
                _context.Add(serviceCategory);
                await _context.SaveChangesAsync();
                
                // Send notification for new service category
                var currentUser = await _userManager.GetUserAsync(User);
                if (currentUser != null)
                {
                    await _notificationService.CreateAndSendNotification(
                        ModelType.ServiceCategory,
                        ProcessType.Add,
                        serviceCategory.Id,
                        currentUser);
                }
                
                return RedirectToAction(nameof(Index));
            }
            return View(viewModel);
        }

        // GET: ServiceCategories/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var serviceCategory = await _context.ServiceCategories.FindAsync(id);
            if (serviceCategory == null)
            {
                return NotFound();
            }
            var viewModel = ServiceCategoryMapper.ToViewModel(serviceCategory);
            return View(viewModel);
        }

        // POST: ServiceCategories/Edit/5
        // To protect from overposting attacks, enable the specific properties you want to bind to.
        // For more details, see http://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("Id,Code,Name,NameEn")] ServiceCategoryViewModel viewModel)
        {
            if (id != viewModel.Id)
            {
                return NotFound();
            }
            ViewBag.Code = viewModel.Code;

            if (ModelState.IsValid)
            {
                try
                {
                    var serviceCategory = ServiceCategoryMapper.FromViewModel(viewModel);
                    _context.Update(serviceCategory);
                    await _context.SaveChangesAsync();
                    
                    // Send notification for updated service category
                    var currentUser = await _userManager.GetUserAsync(User);
                    if (currentUser != null)
                    {
                        await _notificationService.CreateAndSendNotification(
                            ModelType.ServiceCategory,
                            ProcessType.Update,
                            serviceCategory.Id,
                            currentUser);
                    }
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!ServiceCategoryExists(viewModel.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
            }
            return View(viewModel);
        }

        // GET: ServiceCategories/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var serviceCategory = await _context.ServiceCategories
                .FirstOrDefaultAsync(m => m.Id == id);
            if (serviceCategory == null)
            {
                return NotFound();
            }

            var viewModel = ServiceCategoryMapper.ToViewModel(serviceCategory);
            return View(viewModel);
        }

        // POST: ServiceCategories/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var serviceCategory = await _context.ServiceCategories.FindAsync(id);
            if (serviceCategory != null)
            {
                _context.ServiceCategories.Remove(serviceCategory);
            }

            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
        }

        private bool ServiceCategoryExists(int id)
        {
            return _context.ServiceCategories.Any(e => e.Id == id);
        }
    }
}
