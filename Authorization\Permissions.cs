using System;
using System.Collections.Generic;
using System.Linq;

namespace ColorOasisSystemWeb.Authorization
{
    public static class Permissions
    {
        // User permissions
        public const string Users_View = "Permissions.Users.View";
        public const string Users_Create = "Permissions.Users.Create";
        public const string Users_Edit = "Permissions.Users.Edit";
        public const string Users_Delete = "Permissions.Users.Delete";
        
        // Role permissions
        public const string Roles_View = "Permissions.Roles.View";
        public const string Roles_Create = "Permissions.Roles.Create";
        public const string Roles_Edit = "Permissions.Roles.Edit";
        public const string Roles_Delete = "Permissions.Roles.Delete";
        
        // Notification permissions
        public const string Notifications_View = "Permissions.Notifications.View";
        public const string Notifications_Create = "Permissions.Notifications.Create";
        public const string Notifications_Edit = "Permissions.Notifications.Edit";
        public const string Notifications_Delete = "Permissions.Notifications.Delete";
        
        // Client permissions
        public const string Clients_View = "Permissions.Clients.View";
        public const string Clients_Create = "Permissions.Clients.Create";
        public const string Clients_Edit = "Permissions.Clients.Edit";
        public const string Clients_Delete = "Permissions.Clients.Delete";
        
        // Company permissions
        public const string Companies_View = "Permissions.Companies.View";
        public const string Companies_Create = "Permissions.Companies.Create";
        public const string Companies_Edit = "Permissions.Companies.Edit";
        public const string Companies_Delete = "Permissions.Companies.Delete";
        
        // CompanyInfo permissions
        public const string CompanyInfo_View = "Permissions.CompanyInfo.View";
        public const string CompanyInfo_Create = "Permissions.CompanyInfo.Create";
        public const string CompanyInfo_Edit = "Permissions.CompanyInfo.Edit";
        public const string CompanyInfo_Delete = "Permissions.CompanyInfo.Delete";
        
        // Inspection permissions
        public const string Inspections_View = "Permissions.Inspections.View";
        public const string Inspections_Create = "Permissions.Inspections.Create";
        public const string Inspections_Edit = "Permissions.Inspections.Edit";
        public const string Inspections_Delete = "Permissions.Inspections.Delete";
        
        // Quotation permissions
        public const string Quotations_View = "Permissions.Quotations.View";
        public const string Quotations_Create = "Permissions.Quotations.Create";
        public const string Quotations_Edit = "Permissions.Quotations.Edit";
        public const string Quotations_Delete = "Permissions.Quotations.Delete";
        
        // Service permissions
        public const string Services_View = "Permissions.Services.View";
        public const string Services_Create = "Permissions.Services.Create";
        public const string Services_Edit = "Permissions.Services.Edit";
        public const string Services_Delete = "Permissions.Services.Delete";
        
        // ServiceCategory permissions
        public const string ServiceCategories_View = "Permissions.ServiceCategories.View";
        public const string ServiceCategories_Create = "Permissions.ServiceCategories.Create";
        public const string ServiceCategories_Edit = "Permissions.ServiceCategories.Edit";
        public const string ServiceCategories_Delete = "Permissions.ServiceCategories.Delete";
        
        // ServiceType permissions
        public const string ServiceTypes_View = "Permissions.ServiceTypes.View";
        public const string ServiceTypes_Create = "Permissions.ServiceTypes.Create";
        public const string ServiceTypes_Edit = "Permissions.ServiceTypes.Edit";
        public const string ServiceTypes_Delete = "Permissions.ServiceTypes.Delete";
        
        // ConnectionGroup permissions
        public const string ConnectionGroups_View = "Permissions.ConnectionGroups.View";
        public const string ConnectionGroups_Create = "Permissions.ConnectionGroups.Create";
        public const string ConnectionGroups_Edit = "Permissions.ConnectionGroups.Edit";
        public const string ConnectionGroups_Delete = "Permissions.ConnectionGroups.Delete";
        
        // InspectionDetails permissions
        public const string InspectionDetails_View = "Permissions.InspectionDetails.View";
        public const string InspectionDetails_Create = "Permissions.InspectionDetails.Create";
        public const string InspectionDetails_Edit = "Permissions.InspectionDetails.Edit";
        public const string InspectionDetails_Delete = "Permissions.InspectionDetails.Delete";
        
        // QuotationDetails permissions
        public const string QuotationDetails_View = "Permissions.QuotationDetails.View";
        public const string QuotationDetails_Create = "Permissions.QuotationDetails.Create";
        public const string QuotationDetails_Edit = "Permissions.QuotationDetails.Edit";
        public const string QuotationDetails_Delete = "Permissions.QuotationDetails.Delete";
        
        // UserConnection permissions
        public const string UserConnections_View = "Permissions.UserConnections.View";
        public const string UserConnections_Create = "Permissions.UserConnections.Create";
        public const string UserConnections_Edit = "Permissions.UserConnections.Edit";
        public const string UserConnections_Delete = "Permissions.UserConnections.Delete";
        
        // SignalR permissions
        public const string SignalR_View = "Permissions.SignalR.View";
        public const string SignalR_Manage = "Permissions.SignalR.Manage";
        
        // Chat permissions
        public const string Chat_View = "Permissions.Chat.View";
        public const string Chat_Send = "Permissions.Chat.Send";
        public const string Chat_Manage = "Permissions.Chat.Manage";

        // ERP Module Permissions

        // Inventory Management permissions
        public static class Inventory
        {
            public const string View = "Permissions.Inventory.View";
            public const string Create = "Permissions.Inventory.Create";
            public const string Edit = "Permissions.Inventory.Edit";
            public const string Delete = "Permissions.Inventory.Delete";
            public const string StockAdjustment = "Permissions.Inventory.StockAdjustment";
            public const string Transfer = "Permissions.Inventory.Transfer";
        }

        // Warehouse Management permissions
        public static class Warehouse
        {
            public const string View = "Permissions.Warehouse.View";
            public const string Create = "Permissions.Warehouse.Create";
            public const string Edit = "Permissions.Warehouse.Edit";
            public const string Delete = "Permissions.Warehouse.Delete";
            public const string Manage = "Permissions.Warehouse.Manage";
        }

        // Purchasing permissions
        public static class Purchasing
        {
            public const string View = "Permissions.Purchasing.View";
            public const string Create = "Permissions.Purchasing.Create";
            public const string Edit = "Permissions.Purchasing.Edit";
            public const string Delete = "Permissions.Purchasing.Delete";
            public const string Approve = "Permissions.Purchasing.Approve";
            public const string Receive = "Permissions.Purchasing.Receive";
        }

        // Supplier Management permissions
        public static class Suppliers
        {
            public const string View = "Permissions.Suppliers.View";
            public const string Create = "Permissions.Suppliers.Create";
            public const string Edit = "Permissions.Suppliers.Edit";
            public const string Delete = "Permissions.Suppliers.Delete";
            public const string Evaluate = "Permissions.Suppliers.Evaluate";
        }

        // Sales Management permissions
        public static class Sales
        {
            public const string View = "Permissions.Sales.View";
            public const string Create = "Permissions.Sales.Create";
            public const string Edit = "Permissions.Sales.Edit";
            public const string Delete = "Permissions.Sales.Delete";
            public const string Approve = "Permissions.Sales.Approve";
            public const string Ship = "Permissions.Sales.Ship";
        }

        // Lead Management permissions
        public static class Leads
        {
            public const string View = "Permissions.Leads.View";
            public const string Create = "Permissions.Leads.Create";
            public const string Edit = "Permissions.Leads.Edit";
            public const string Delete = "Permissions.Leads.Delete";
            public const string Convert = "Permissions.Leads.Convert";
        }

        // Opportunity Management permissions
        public static class Opportunities
        {
            public const string View = "Permissions.Opportunities.View";
            public const string Create = "Permissions.Opportunities.Create";
            public const string Edit = "Permissions.Opportunities.Edit";
            public const string Delete = "Permissions.Opportunities.Delete";
            public const string Close = "Permissions.Opportunities.Close";
        }

        // Accounting permissions
        public static class Accounting
        {
            public const string View = "Permissions.Accounting.View";
            public const string Create = "Permissions.Accounting.Create";
            public const string Edit = "Permissions.Accounting.Edit";
            public const string Delete = "Permissions.Accounting.Delete";
            public const string ProcessPayments = "Permissions.Accounting.ProcessPayments";
        }

        // Invoice Management permissions
        public static class Invoices
        {
            public const string View = "Permissions.Invoices.View";
            public const string Create = "Permissions.Invoices.Create";
            public const string Edit = "Permissions.Invoices.Edit";
            public const string Delete = "Permissions.Invoices.Delete";
            public const string Send = "Permissions.Invoices.Send";
            public const string Void = "Permissions.Invoices.Void";
        }

        // Payment Management permissions
        public static class Payments
        {
            public const string View = "Permissions.Payments.View";
            public const string Create = "Permissions.Payments.Create";
            public const string Edit = "Permissions.Payments.Edit";
            public const string Delete = "Permissions.Payments.Delete";
            public const string Process = "Permissions.Payments.Process";
            public const string Refund = "Permissions.Payments.Refund";
        }

        // Reporting permissions
        public static class Reports
        {
            public const string View = "Permissions.Reports.View";
            public const string Generate = "Permissions.Reports.Generate";
            public const string Export = "Permissions.Reports.Export";
            public const string Schedule = "Permissions.Reports.Schedule";
        }
        
        // Get all permissions
        public static List<string> GetAllPermissions()
        {
            return typeof(Permissions)
                .GetFields(System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Static | System.Reflection.BindingFlags.FlattenHierarchy)
                .Where(f => f.IsLiteral && !f.IsInitOnly && f.FieldType == typeof(string))
                .Select(f => (string)f.GetValue(null))
                .ToList();
        }
        
        // Get permissions by role
        public static List<string> GetPermissionsByRole(string roleName)
        {
            // Default permissions based on role
            switch (roleName)
            {
                case "Admin":
                    return GetAllPermissions();
                case "Manager":
                    return new List<string>
                    {
                        // User management
                        Users_View,
                        
                        // Role management
                        Roles_View,
                        
                        // Notification management
                        Notifications_View,
                        Notifications_Create,
                        Notifications_Edit,
                        
                        // Client management
                        Clients_View,
                        Clients_Create,
                        Clients_Edit,
                        
                        // Company management
                        Companies_View,
                        Companies_Create,
                        Companies_Edit,
                        
                        // CompanyInfo management
                        CompanyInfo_View,
                        CompanyInfo_Edit,
                        
                        // Inspection management
                        Inspections_View,
                        Inspections_Create,
                        Inspections_Edit,
                        InspectionDetails_View,
                        InspectionDetails_Create,
                        InspectionDetails_Edit,
                        
                        // Quotation management
                        Quotations_View,
                        Quotations_Create,
                        Quotations_Edit,
                        QuotationDetails_View,
                        QuotationDetails_Create,
                        QuotationDetails_Edit,
                        
                        // Service management
                        Services_View,
                        Services_Create,
                        Services_Edit,
                        ServiceCategories_View,
                        ServiceTypes_View,
                        
                        // Connection management
                        ConnectionGroups_View,
                        UserConnections_View,
                        
                        // SignalR management
                        SignalR_View
                    };
                case "User":
                    return new List<string>
                    {
                        // Notification permissions
                        Notifications_View,
                        
                        // View-only permissions
                        Clients_View,
                        Companies_View,
                        CompanyInfo_View,
                        Inspections_View,
                        InspectionDetails_View,
                        Quotations_View,
                        QuotationDetails_View,
                        Services_View,
                        ServiceCategories_View,
                        ServiceTypes_View,
                        ConnectionGroups_View,
                        UserConnections_View
                    };
                default:
                    // For custom roles, provide basic view permissions
                    // This ensures new roles have at least basic access
                    return new List<string>
                    {
                        // Basic view permissions for custom roles
                        Users_View,
                        Notifications_View,
                        Clients_View,
                        Companies_View,
                        CompanyInfo_View,
                        Inspections_View,
                        InspectionDetails_View,
                        Quotations_View,
                        QuotationDetails_View,
                        Services_View,
                        ServiceCategories_View,
                        ServiceTypes_View,
                        ConnectionGroups_View,
                        UserConnections_View
                    };
            }
        }
        
        // Get all permissions grouped by category
        public static Dictionary<string, List<PermissionInfo>> GetPermissionsByCategory()
        {
            var allPermissions = GetAllPermissions();
            var permissionGroups = new Dictionary<string, List<PermissionInfo>>();
            
            foreach (var permission in allPermissions)
            {
                var parts = permission.Split('.');
                if (parts.Length >= 3)
                {
                    var category = parts[1]; // e.g., "Users", "Roles", etc.
                    var action = parts[2]; // e.g., "View", "Create", etc.
                    
                    if (!permissionGroups.ContainsKey(category))
                    {
                        permissionGroups[category] = new List<PermissionInfo>();
                    }
                    
                    permissionGroups[category].Add(new PermissionInfo
                    {
                        Name = $"{category} {action}",
                        Value = permission,
                        Category = category,
                        Action = action
                    });
                }
            }
            
            return permissionGroups;
        }
    }
    
    // Class to hold permission information
    public class PermissionInfo
    {
        public string Name { get; set; }
        public string Value { get; set; }
        public string Category { get; set; }
        public string Action { get; set; }
    }
}