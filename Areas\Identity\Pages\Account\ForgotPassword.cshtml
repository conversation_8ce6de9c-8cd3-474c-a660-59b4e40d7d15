@page
@model ForgotPasswordModel
@{
    ViewData["Title"] = @SharedLocalizer["Identity.Account.ForgotPassword"];
    Layout = null; // This page won't use the layout
}

<h1>@ViewData["Title"]</h1>
<h2>@SharedLocalizer["Identity.Account.EnterEmail"]</h2>
<hr />
<div class="row">
    <div class="col-md-4">
        <form method="post">
            <div asp-validation-summary="ModelOnly" class="text-danger" role="alert"></div>
            <div class="form-floating mb-3">
                <input asp-for="Input.Email" class="form-control" autocomplete="username" aria-required="true" placeholder="<EMAIL>" />
                <label asp-for="Input.Email" class="form-label"></label>
                <span asp-validation-for="Input.Email" class="text-danger"></span>
            </div>
            <button type="submit" class="w-100 btn btn-lg btn-primary">@SharedLocalizer["Identity.Account.ResetPassword"]</button>
        </form>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
