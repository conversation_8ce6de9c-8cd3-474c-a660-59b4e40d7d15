using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using System.Security.Claims;
using System.Threading.Tasks;
using ColorOasisSystemWeb.Models;

namespace ColorOasisSystemWeb.Authorization
{
    public class PermissionRequirement : IAuthorizationRequirement
    {
        public string Permission { get; private set; }

        public PermissionRequirement(string permission)
        {
            Permission = permission;
        }
    }

    public class PermissionAuthorizationHandler : AuthorizationHandler<PermissionRequirement>
    {
        private readonly UserManager<User> _userManager;
        private readonly RoleManager<IdentityRole> _roleManager;

        public PermissionAuthorizationHandler(UserManager<User> userManager, RoleManager<IdentityRole> roleManager)
        {
            _userManager = userManager;
            _roleManager = roleManager;
        }

        protected override async Task HandleRequirementAsync(AuthorizationHandlerContext context, PermissionRequirement requirement)
        {
            if (context.User == null)
            {
                return;
            }

            // Get user ID from claims
            var userId = context.User.FindFirstValue(ClaimTypes.NameIdentifier);
            if (string.IsNullOrEmpty(userId))
            {
                return;
            }

            // Get user from database
            var user = await _userManager.FindByIdAsync(userId);
            if (user == null)
            {
                return;
            }

            // Get user roles
            var userRoles = await _userManager.GetRolesAsync(user);
            
            // Check if user has Admin role (Admin has all permissions)
            if (userRoles.Contains("Admin"))
            {
                context.Succeed(requirement);
                return;
            }

            // Check if user has the required permission through their roles
            foreach (var roleName in userRoles)
            {
                var permissions = Permissions.GetPermissionsByRole(roleName);
                if (permissions.Contains(requirement.Permission))
                {
                    context.Succeed(requirement);
                    return;
                }
            }

            // Check if user has the required permission directly assigned
            var userClaims = await _userManager.GetClaimsAsync(user);
            if (userClaims.Any(c => c.Type == "Permission" && c.Value == requirement.Permission))
            {
                context.Succeed(requirement);
            }
        }
    }
}