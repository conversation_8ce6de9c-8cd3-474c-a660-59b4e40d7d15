using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using ColorOasisSystemWeb.Data;
using ColorOasisSystemWeb.Models.Sales;
using ColorOasisSystemWeb.Authorization;
using Microsoft.AspNetCore.Identity;
using ColorOasisSystemWeb.Models;
using ColorOasisSystemWeb.AdvancedNotification.Services;
using ColorOasisSystemWeb.Enums;
using Microsoft.AspNetCore.Mvc.Rendering;

namespace ColorOasisSystemWeb.Controllers
{
    [Authorize]
    public class SalesOrdersController : Controller
    {
        private readonly AppDbContext _context;
        private readonly UserManager<User> _userManager;
        private readonly NotificationService _notificationService;
        private readonly ILogger<SalesOrdersController> _logger;

        public SalesOrdersController(
            AppDbContext context,
            UserManager<User> userManager,
            NotificationService notificationService,
            ILogger<SalesOrdersController> logger)
        {
            _context = context;
            _userManager = userManager;
            _notificationService = notificationService;
            _logger = logger;
        }

        // GET: SalesOrders
        [Authorize(Permissions.Sales.View)]
        public async Task<IActionResult> Index()
        {
            try
            {
                var salesOrders = await _context.SalesOrders
                    .Include(s => s.Client)
                    .Include(s => s.Company)
                    .Include(s => s.Items)
                    .OrderByDescending(s => s.OrderDate)
                    .ToListAsync();

                return View(salesOrders);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving sales orders");
                TempData["Error"] = "An error occurred while retrieving sales orders.";
                return View(new List<SalesOrder>());
            }
        }

        // GET: SalesOrders/Details/5
        [Authorize(Permissions.Sales.View)]
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var salesOrder = await _context.SalesOrders
                .Include(s => s.Client)
                .Include(s => s.Company)
                .Include(s => s.Opportunity)
                .Include(s => s.Quotation)
                .Include(s => s.Items)
                .ThenInclude(i => i.Service)
                .FirstOrDefaultAsync(m => m.Id == id);

            if (salesOrder == null)
            {
                return NotFound();
            }

            return View(salesOrder);
        }

        // GET: SalesOrders/Create
        [Authorize(Permissions.Sales.Create)]
        public IActionResult Create()
        {
            ViewData["ClientId"] = new SelectList(_context.Clients.Where(c => !c.IsDeleted), "Id", "Name");
            ViewData["CompanyId"] = new SelectList(_context.Companies.Where(c => !c.IsDeleted), "Id", "Name");
            ViewData["OpportunityId"] = new SelectList(_context.Opportunities.Where(o => o.Stage != OpportunityStage.Lost && o.Stage != OpportunityStage.Won), "Id", "Title");
            ViewData["QuotationId"] = new SelectList(_context.Quotations.Where(q => !q.IsDeleted), "Id", "Code");
            return View();
        }

        // POST: SalesOrders/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        [Authorize(Permissions.Sales.Create)]
        public async Task<IActionResult> Create([Bind("ClientId,CompanyId,OpportunityId,QuotationId,CustomerPO,ExpectedDeliveryDate,DeliveryAddress,PaymentTerms,Notes,TaxRate,DiscountAmount,ShippingCost")] SalesOrder salesOrder)
        {
            if (ModelState.IsValid)
            {
                try
                {
                    // Generate order number
                    salesOrder.OrderNumber = await GenerateOrderNumber();
                    salesOrder.OrderDate = DateTime.UtcNow;
                    salesOrder.Status = SalesOrderStatus.Draft;

                    var currentUser = await _userManager.GetUserAsync(User);
                    salesOrder.CreatedBy = currentUser?.UserName ?? "System";
                    salesOrder.CreatedAt = DateTime.UtcNow;
                    salesOrder.UpdatedAt = DateTime.UtcNow;

                    _context.Add(salesOrder);
                    await _context.SaveChangesAsync();

                    // Send notification
                    if (currentUser != null)
                    {
                        await _notificationService.CreateAndSendNotification(
                            ModelType.SalesOrder,
                            ProcessType.Add,
                            salesOrder.Id,
                            currentUser);
                    }

                    TempData["Success"] = "Sales order created successfully.";
                    return RedirectToAction(nameof(Details), new { id = salesOrder.Id });
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error creating sales order");
                    TempData["Error"] = "An error occurred while creating the sales order.";
                }
            }

            ViewData["ClientId"] = new SelectList(_context.Clients.Where(c => !c.IsDeleted), "Id", "Name", salesOrder.ClientId);
            ViewData["CompanyId"] = new SelectList(_context.Companies.Where(c => !c.IsDeleted), "Id", "Name", salesOrder.CompanyId);
            ViewData["OpportunityId"] = new SelectList(_context.Opportunities.Where(o => o.Stage != OpportunityStage.Lost && o.Stage != OpportunityStage.Won), "Id", "Title", salesOrder.OpportunityId);
            ViewData["QuotationId"] = new SelectList(_context.Quotations.Where(q => !q.IsDeleted), "Id", "Code", salesOrder.QuotationId);
            return View(salesOrder);
        }

        // GET: SalesOrders/Edit/5
        [Authorize(Permissions.Sales.Edit)]
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var salesOrder = await _context.SalesOrders.FindAsync(id);
            if (salesOrder == null)
            {
                return NotFound();
            }

            // Only allow editing of draft orders
            if (salesOrder.Status != SalesOrderStatus.Draft)
            {
                TempData["Error"] = "Only draft sales orders can be edited.";
                return RedirectToAction(nameof(Details), new { id });
            }

            ViewData["ClientId"] = new SelectList(_context.Clients.Where(c => !c.IsDeleted), "Id", "Name", salesOrder.ClientId);
            ViewData["CompanyId"] = new SelectList(_context.Companies.Where(c => !c.IsDeleted), "Id", "Name", salesOrder.CompanyId);
            ViewData["OpportunityId"] = new SelectList(_context.Opportunities.Where(o => o.Stage != OpportunityStage.Lost && o.Stage != OpportunityStage.Won), "Id", "Title", salesOrder.OpportunityId);
            ViewData["QuotationId"] = new SelectList(_context.Quotations.Where(q => !q.IsDeleted), "Id", "Code", salesOrder.QuotationId);
            return View(salesOrder);
        }

        // POST: SalesOrders/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        [Authorize(Permissions.Sales.Edit)]
        public async Task<IActionResult> Edit(int id, [Bind("Id,OrderNumber,ClientId,CompanyId,OpportunityId,QuotationId,CustomerPO,ExpectedDeliveryDate,DeliveryAddress,PaymentTerms,Notes,TaxRate,DiscountAmount,ShippingCost,Status,OrderDate,CreatedBy,CreatedAt")] SalesOrder salesOrder)
        {
            if (id != salesOrder.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    salesOrder.UpdatedAt = DateTime.UtcNow;
                    var currentUser = await _userManager.GetUserAsync(User);
                    salesOrder.UpdatedBy = currentUser?.UserName ?? "System";

                    _context.Update(salesOrder);
                    await _context.SaveChangesAsync();

                    // Send notification
                    if (currentUser != null)
                    {
                        await _notificationService.CreateAndSendNotification(
                            ModelType.SalesOrder,
                            ProcessType.Edit,
                            salesOrder.Id,
                            currentUser);
                    }

                    TempData["Success"] = "Sales order updated successfully.";
                    return RedirectToAction(nameof(Details), new { id });
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!SalesOrderExists(salesOrder.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error updating sales order");
                    TempData["Error"] = "An error occurred while updating the sales order.";
                }
            }

            ViewData["ClientId"] = new SelectList(_context.Clients.Where(c => !c.IsDeleted), "Id", "Name", salesOrder.ClientId);
            ViewData["CompanyId"] = new SelectList(_context.Companies.Where(c => !c.IsDeleted), "Id", "Name", salesOrder.CompanyId);
            ViewData["OpportunityId"] = new SelectList(_context.Opportunities.Where(o => o.Stage != OpportunityStage.Lost && o.Stage != OpportunityStage.Won), "Id", "Title", salesOrder.OpportunityId);
            ViewData["QuotationId"] = new SelectList(_context.Quotations.Where(q => !q.IsDeleted), "Id", "Code", salesOrder.QuotationId);
            return View(salesOrder);
        }

        // POST: SalesOrders/Confirm/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        [Authorize(Permissions.Sales.Edit)]
        public async Task<IActionResult> Confirm(int id)
        {
            try
            {
                var salesOrder = await _context.SalesOrders
                    .Include(s => s.Items)
                    .FirstOrDefaultAsync(s => s.Id == id);

                if (salesOrder == null)
                {
                    return NotFound();
                }

                if (salesOrder.Status != SalesOrderStatus.Draft)
                {
                    TempData["Error"] = "Only draft sales orders can be confirmed.";
                    return RedirectToAction(nameof(Details), new { id });
                }

                if (!salesOrder.Items.Any())
                {
                    TempData["Error"] = "Cannot confirm sales order without items.";
                    return RedirectToAction(nameof(Details), new { id });
                }

                salesOrder.Status = SalesOrderStatus.Confirmed;
                salesOrder.ConfirmedDate = DateTime.UtcNow;

                var currentUser = await _userManager.GetUserAsync(User);
                salesOrder.ConfirmedBy = currentUser?.UserName ?? "System";
                salesOrder.UpdatedAt = DateTime.UtcNow;
                salesOrder.UpdatedBy = currentUser?.UserName ?? "System";

                // Calculate totals
                salesOrder.Subtotal = salesOrder.Items.Sum(i => i.QuantityOrdered * i.UnitPrice);
                salesOrder.TaxAmount = salesOrder.Subtotal * (salesOrder.TaxRate / 100);
                salesOrder.TotalAmount = salesOrder.Subtotal + salesOrder.TaxAmount + salesOrder.ShippingCost - salesOrder.DiscountAmount;

                await _context.SaveChangesAsync();

                TempData["Success"] = "Sales order confirmed successfully.";
                return RedirectToAction(nameof(Details), new { id });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error confirming sales order");
                TempData["Error"] = "An error occurred while confirming the sales order.";
                return RedirectToAction(nameof(Details), new { id });
            }
        }

        // POST: SalesOrders/Cancel/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        [Authorize(Permissions.Sales.Edit)]
        public async Task<IActionResult> Cancel(int id)
        {
            try
            {
                var salesOrder = await _context.SalesOrders.FindAsync(id);

                if (salesOrder == null)
                {
                    return NotFound();
                }

                if (salesOrder.Status == SalesOrderStatus.Completed || salesOrder.Status == SalesOrderStatus.Cancelled)
                {
                    TempData["Error"] = "Cannot cancel completed or already cancelled sales orders.";
                    return RedirectToAction(nameof(Details), new { id });
                }

                salesOrder.Status = SalesOrderStatus.Cancelled;
                salesOrder.CancelledDate = DateTime.UtcNow;

                var currentUser = await _userManager.GetUserAsync(User);
                salesOrder.CancelledBy = currentUser?.UserName ?? "System";
                salesOrder.UpdatedAt = DateTime.UtcNow;
                salesOrder.UpdatedBy = currentUser?.UserName ?? "System";

                await _context.SaveChangesAsync();

                TempData["Success"] = "Sales order cancelled successfully.";
                return RedirectToAction(nameof(Details), new { id });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cancelling sales order");
                TempData["Error"] = "An error occurred while cancelling the sales order.";
                return RedirectToAction(nameof(Details), new { id });
            }
        }

        // Helper method to generate order number
        private async Task<string> GenerateOrderNumber()
        {
            var today = DateTime.Today;
            var prefix = $"SO{today:yyyyMM}";

            var lastOrder = await _context.SalesOrders
                .Where(s => s.OrderNumber.StartsWith(prefix))
                .OrderByDescending(s => s.OrderNumber)
                .FirstOrDefaultAsync();

            int nextNumber = 1;
            if (lastOrder != null)
            {
                var numberPart = lastOrder.OrderNumber.Substring(prefix.Length);
                if (int.TryParse(numberPart, out int lastNumber))
                {
                    nextNumber = lastNumber + 1;
                }
            }

            return $"{prefix}{nextNumber:D4}";
        }

        private bool SalesOrderExists(int id)
        {
            return _context.SalesOrders.Any(e => e.Id == id);
        }
    }
}
