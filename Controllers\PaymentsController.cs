using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using ColorOasisSystemWeb.Authorization;

namespace ColorOasisSystemWeb.Controllers
{
    [Authorize]
    public class PaymentsController : Controller
    {
        [Authorize(Permissions.Payments.View)]
        public IActionResult Index()
        {
            ViewBag.Title = "Payments";
            ViewBag.Message = "Manage customer payments, payment processing, and payment tracking.";
            return View("~/Views/Shared/ComingSoon.cshtml");
        }

        [Authorize(Permissions.Payments.Create)]
        public IActionResult Create()
        {
            ViewBag.Title = "Record Payment";
            ViewBag.Message = "Record new payment functionality will be implemented here.";
            return View("~/Views/Shared/ComingSoon.cshtml");
        }
    }
}
