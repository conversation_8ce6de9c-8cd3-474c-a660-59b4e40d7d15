using ColorOasisSystemWeb.Models;
using ColorOasisSystemWeb.Models.Chat;
using ColorOasisSystemWeb.Models.Notification;
using ColorOasisSystemWeb.Models.Inventory;
using ColorOasisSystemWeb.Models.Purchasing;
using ColorOasisSystemWeb.Models.Sales;
using ColorOasisSystemWeb.Models.Accounting;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using System.Reflection.Emit;

namespace ColorOasisSystemWeb.Data
{
    public class AppDbContext : IdentityDbContext<User>
    {
        public AppDbContext(DbContextOptions<AppDbContext> options)
            : base(options)
        {
            Database.Migrate();
        }
        
        public DbSet<UserSocialMedia> UserSocialMedia { get; set; }
        protected override void OnModelCreating(ModelBuilder builder)
        {
            base.OnModelCreating(builder);
            //Tochange the Default Schema 
            //builder.HasDefaultSchema("Name");
            builder.Entity<User>().ToTable("Users", "Security");//to ignore property.Ignore(e=>e.PhoneNumber)
            builder.Entity<IdentityRole>().ToTable("Role", "Security");
            builder.Entity<IdentityUserRole<string>>().ToTable("UsersRole", "Security");
            builder.Entity<IdentityRoleClaim<string>>().ToTable("RolesClaim", "Security");
            builder.Entity<IdentityUserLogin<string>>().ToTable("UsersLogin", "Security");
            builder.Entity<IdentityUserClaim<string>>().ToTable("UsersClaim", "Security");
            builder.Entity<IdentityUserToken<string>>().ToTable("UsersToken", "Security");
            builder.Entity<Quotation>()
            .HasOne(q => q.User)
            .WithMany(u => u.Quotations)
            .HasForeignKey(q => q.UserId)
            .OnDelete(DeleteBehavior.NoAction);
        }
        public DbSet<CompanyInfo> CompanyInfo { get; set; }
        public DbSet<Notification> Notifications { get; set; }
        public DbSet<ConnectionGroupUser> ConnectionGroupUsers { get; set; }
        public DbSet<ConnectionGroup> ConnectionGroups { get; set; }
        public DbSet<UserNotification> UserNotifications { get; set; }
        public DbSet<NotificationPreference> NotificationPreferences { get; set; }
        public DbSet<Quotation> Quotations { get; set; }
        public DbSet<QuotationDetails> QuotationDetails { get; set; }
        public DbSet<Client> Clients { get; set; }
        public DbSet<Service> Services { get; set; }
        public DbSet<ServiceCategory> ServiceCategories { get; set; }
        public DbSet<ServiceType> ServiceTypes { get; set; }
        public DbSet<Company> Companies { get; set; }
        public DbSet<Inspection> Inspections { get; set; }
        public DbSet<InspectionDetails> InspectionDetails { get; set; }
        
        // Chat related entities
        public DbSet<ChatMessage> ChatMessages { get; set; }
        public DbSet<ChatConversation> ChatConversations { get; set; }
        public DbSet<ChatParticipant> ChatParticipants { get; set; }
        public DbSet<ChatAttachment> ChatAttachments { get; set; }
        
        // Profile related entities
        public DbSet<UserProfileDetails> UserProfileDetails { get; set; }
        public DbSet<UserProfileVisibility> UserProfileVisibility { get; set; }

        // Inventory Management entities
        public DbSet<InventoryItem> InventoryItems { get; set; }
        public DbSet<Warehouse> Warehouses { get; set; }
        public DbSet<BinLocation> BinLocations { get; set; }
        public DbSet<InventoryLocation> InventoryLocations { get; set; }
        public DbSet<InventoryTransaction> InventoryTransactions { get; set; }
        public DbSet<StockAdjustment> StockAdjustments { get; set; }

        // Purchasing entities
        public DbSet<Supplier> Suppliers { get; set; }
        public DbSet<SupplierContract> SupplierContracts { get; set; }
        public DbSet<SupplierContact> SupplierContacts { get; set; }
        public DbSet<SupplierEvaluation> SupplierEvaluations { get; set; }
        public DbSet<PurchaseRequisition> PurchaseRequisitions { get; set; }
        public DbSet<RequisitionItem> RequisitionItems { get; set; }
        public DbSet<RequisitionApproval> RequisitionApprovals { get; set; }
        public DbSet<PurchaseOrder> PurchaseOrders { get; set; }
        public DbSet<PurchaseOrderItem> PurchaseOrderItems { get; set; }
        public DbSet<PurchaseOrderApproval> PurchaseOrderApprovals { get; set; }
        public DbSet<GoodsReceipt> GoodsReceipts { get; set; }
        public DbSet<ReceiptItem> ReceiptItems { get; set; }

        // Sales Management entities
        public DbSet<Lead> Leads { get; set; }
        public DbSet<LeadActivity> LeadActivities { get; set; }
        public DbSet<LeadNote> LeadNotes { get; set; }
        public DbSet<Opportunity> Opportunities { get; set; }
        public DbSet<OpportunityActivity> OpportunityActivities { get; set; }
        public DbSet<OpportunityNote> OpportunityNotes { get; set; }
        public DbSet<SalesOrder> SalesOrders { get; set; }
        public DbSet<SalesOrderItem> SalesOrderItems { get; set; }
        public DbSet<SalesOrderApproval> SalesOrderApprovals { get; set; }
        public DbSet<Shipment> Shipments { get; set; }
        public DbSet<ShipmentItem> ShipmentItems { get; set; }

        // Accounting entities
        public DbSet<Invoice> Invoices { get; set; }
        public DbSet<InvoiceItem> InvoiceItems { get; set; }
        public DbSet<Payment> Payments { get; set; }
        public DbSet<InvoiceHistory> InvoiceHistory { get; set; }
    }
}
