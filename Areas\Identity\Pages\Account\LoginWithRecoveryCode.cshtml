@page
@model LoginWithRecoveryCodeModel
@{
    ViewData["Title"] = @SharedLocalizer["Identity.Manage.RecoveryCodeVerification"];
    Layout = null; // This page won't use the layout
}

<h1>@ViewData["Title"]</h1>
<hr />
<p>
    @SharedLocalizer["Identity.Manage.RecoveryCodeLoginInfo"]
</p>
<div class="row">
    <div class="col-md-4">
        <form method="post">
            <div asp-validation-summary="ModelOnly" class="text-danger" role="alert"></div>
            <div class="form-floating mb-3">
                <input asp-for="Input.RecoveryCode" class="form-control" autocomplete="off" placeholder="RecoveryCode" />
                <label asp-for="Input.RecoveryCode" class="form-label"></label>
                <span asp-validation-for="Input.RecoveryCode" class="text-danger"></span>
            </div>
            <button type="submit" class="w-100 btn btn-lg btn-primary">@SharedLocalizer["Identity.Account.Login"]</button>
        </form>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}