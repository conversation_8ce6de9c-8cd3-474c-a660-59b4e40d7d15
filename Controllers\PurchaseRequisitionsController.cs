using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using ColorOasisSystemWeb.Authorization;

namespace ColorOasisSystemWeb.Controllers
{
    [Authorize]
    public class PurchaseRequisitionsController : Controller
    {
        [Authorize(Permissions.Purchasing.View)]
        public IActionResult Index()
        {
            ViewBag.Title = "Purchase Requisitions";
            ViewBag.Message = "Manage purchase requests, approvals, and procurement workflows.";
            return View("~/Views/Shared/ComingSoon.cshtml");
        }

        [Authorize(Permissions.Purchasing.Create)]
        public IActionResult Create()
        {
            ViewBag.Title = "Create Requisition";
            ViewBag.Message = "Create new purchase requisition functionality will be implemented here.";
            return View("~/Views/Shared/ComingSoon.cshtml");
        }
    }
}
