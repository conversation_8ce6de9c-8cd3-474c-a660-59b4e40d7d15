@{
    ViewData["Title"] = "Accounting";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="nxl-content">
    <!-- [ page-header ] start -->
    <div class="page-header">
        <div class="page-header-left d-flex align-items-center">
            <div class="page-header-title">
                <h5 class="m-b-10">Accounting</h5>
            </div>
            <ul class="breadcrumb">
                <li class="breadcrumb-item"><a href="@Url.Action("Index", "Home")">Home</a></li>
                <li class="breadcrumb-item">ERP</li>
                <li class="breadcrumb-item">Accounting</li>
            </ul>
        </div>
        <div class="page-header-right ms-auto">
            <div class="page-header-right-items">
                <div class="d-flex align-items-center gap-2 page-header-right-items-wrapper">
                    <a href="@Url.Action("Index", "Invoices")" class="btn btn-primary">
                        <i class="feather-file-text me-2"></i>
                        <span>Invoices</span>
                    </a>
                    <a href="@Url.Action("Index", "Payments")" class="btn btn-outline-primary">
                        <i class="feather-dollar-sign me-2"></i>
                        <span>Payments</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
    <!-- [ page-header ] end -->

    <!-- [ Main Content ] start -->
    <div class="main-content">
        <div class="row">
            <!-- Quick Stats -->
            <div class="col-lg-3 col-md-6">
                <div class="card stretch stretch-full">
                    <div class="card-body">
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <h6 class="text-muted mb-1">Total Invoices</h6>
                                <h4 class="mb-0">-</h4>
                            </div>
                            <div class="avatar-text avatar-lg bg-soft-primary text-primary">
                                <i class="feather-file-text"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="card stretch stretch-full">
                    <div class="card-body">
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <h6 class="text-muted mb-1">Outstanding Amount</h6>
                                <h4 class="mb-0">-</h4>
                            </div>
                            <div class="avatar-text avatar-lg bg-soft-warning text-warning">
                                <i class="feather-clock"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="card stretch stretch-full">
                    <div class="card-body">
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <h6 class="text-muted mb-1">Payments Received</h6>
                                <h4 class="mb-0">-</h4>
                            </div>
                            <div class="avatar-text avatar-lg bg-soft-success text-success">
                                <i class="feather-dollar-sign"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="card stretch stretch-full">
                    <div class="card-body">
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <h6 class="text-muted mb-1">Overdue Invoices</h6>
                                <h4 class="mb-0">-</h4>
                            </div>
                            <div class="avatar-text avatar-lg bg-soft-danger text-danger">
                                <i class="feather-alert-triangle"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-12">
                <div class="card stretch stretch-full">
                    <div class="card-body">
                        <div class="text-center py-5">
                            <div class="mb-4">
                                <i class="feather-dollar-sign" style="font-size: 4rem; color: #6c757d;"></i>
                            </div>
                            <h4 class="mb-3">Accounting Module</h4>
                            <p class="text-muted mb-4">
                                Welcome to the Accounting module! You can access Invoices and Payments from the buttons above.
                            </p>
                            <div class="alert alert-info">
                                <i class="feather-info me-2"></i>
                                <strong>Available:</strong> Invoice management and payment processing are fully functional.
                                <br>
                                <strong>Coming Soon:</strong> Chart of accounts, general ledger, financial statements, and advanced reporting.
                            </div>
                            <div class="d-flex justify-content-center gap-3 mt-4">
                                <a href="@Url.Action("Index", "Invoices")" class="btn btn-primary">
                                    <i class="feather-file-text me-2"></i>
                                    Manage Invoices
                                </a>
                                <a href="@Url.Action("Index", "Payments")" class="btn btn-outline-primary">
                                    <i class="feather-dollar-sign me-2"></i>
                                    View Payments
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- [ Main Content ] end -->
</div>
