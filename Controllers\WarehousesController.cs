using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using ColorOasisSystemWeb.Authorization;

namespace ColorOasisSystemWeb.Controllers
{
    [Authorize]
    public class WarehousesController : Controller
    {
        [Authorize(Permissions.Warehouse.View)]
        public IActionResult Index()
        {
            ViewBag.Title = "Warehouses";
            ViewBag.Message = "Manage warehouse locations, bin locations, and storage facilities.";
            return View("~/Views/Shared/ComingSoon.cshtml");
        }

        [Authorize(Permissions.Warehouse.Create)]
        public IActionResult Create()
        {
            ViewBag.Title = "Add Warehouse";
            ViewBag.Message = "Add new warehouse functionality will be implemented here.";
            return View("~/Views/Shared/ComingSoon.cshtml");
        }
    }
}
