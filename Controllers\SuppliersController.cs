using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using ColorOasisSystemWeb.Data;
using ColorOasisSystemWeb.Models.Purchasing;
using ColorOasisSystemWeb.Authorization;
using Microsoft.AspNetCore.Identity;
using ColorOasisSystemWeb.Models;
using ColorOasisSystemWeb.AdvancedNotification.Services;
using ColorOasisSystemWeb.Enums;

namespace ColorOasisSystemWeb.Controllers
{
    [Authorize]
    public class SuppliersController : Controller
    {
        private readonly AppDbContext _context;
        private readonly UserManager<User> _userManager;
        private readonly NotificationService _notificationService;
        private readonly ILogger<SuppliersController> _logger;

        public SuppliersController(
            AppDbContext context,
            UserManager<User> userManager,
            NotificationService notificationService,
            ILogger<SuppliersController> logger)
        {
            _context = context;
            _userManager = userManager;
            _notificationService = notificationService;
            _logger = logger;
        }

        // GET: Suppliers
        [Authorize(Permissions.Suppliers.View)]
        public async Task<IActionResult> Index()
        {
            try
            {
                var suppliers = await _context.Suppliers
                    .Include(s => s.Contacts)
                    .Include(s => s.Contracts)
                    .ToListAsync();

                return View(suppliers);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving suppliers");
                TempData["Error"] = "An error occurred while retrieving suppliers.";
                return View(new List<Supplier>());
            }
        }

        // GET: Suppliers/Details/5
        [Authorize(Permissions.Suppliers.View)]
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var supplier = await _context.Suppliers
                .Include(s => s.Contacts)
                .Include(s => s.Contracts)
                .Include(s => s.Evaluations)
                .Include(s => s.PurchaseOrders)
                .FirstOrDefaultAsync(m => m.Id == id);

            if (supplier == null)
            {
                return NotFound();
            }

            return View(supplier);
        }

        // GET: Suppliers/Create
        [Authorize(Permissions.Suppliers.Create)]
        public IActionResult Create()
        {
            return View();
        }

        // POST: Suppliers/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        [Authorize(Permissions.Suppliers.Create)]
        public async Task<IActionResult> Create([Bind("Code,Name,NameEn,ContactPerson,Email,Phone,Mobile,Fax,Website,Address,City,State,Country,PostalCode,TaxNumber,RegistrationNumber,Type,Status,PaymentTerms,CreditLimit,Currency,BankName,BankAccount,IBAN,SWIFT,Notes,QualityRating,DeliveryRating,ServiceRating,LeadTimeDays,MinimumOrderAmount,IsActive")] Supplier supplier)
        {
            if (ModelState.IsValid)
            {
                try
                {
                    // Generate supplier code if not provided
                    if (string.IsNullOrEmpty(supplier.Code))
                    {
                        supplier.Code = await GenerateSupplierCode();
                    }

                    supplier.CreatedAt = DateTime.UtcNow;
                    supplier.UpdatedAt = DateTime.UtcNow;

                    var currentUser = await _userManager.GetUserAsync(User);
                    supplier.CreatedBy = currentUser?.UserName ?? "System";

                    _context.Add(supplier);
                    await _context.SaveChangesAsync();

                    // Send notification
                    if (currentUser != null)
                    {
                        await _notificationService.CreateAndSendNotification(
                            ModelType.Supplier,
                            ProcessType.Add,
                            supplier.Id,
                            currentUser);
                    }

                    TempData["Success"] = "Supplier created successfully.";
                    return RedirectToAction(nameof(Index));
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error creating supplier");
                    TempData["Error"] = "An error occurred while creating the supplier.";
                }
            }
            return View(supplier);
        }

        // GET: Suppliers/Edit/5
        [Authorize(Permissions.Suppliers.Edit)]
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var supplier = await _context.Suppliers.FindAsync(id);
            if (supplier == null)
            {
                return NotFound();
            }
            return View(supplier);
        }

        // POST: Suppliers/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        [Authorize(Permissions.Suppliers.Edit)]
        public async Task<IActionResult> Edit(int id, [Bind("Id,Code,Name,NameEn,ContactPerson,Email,Phone,Mobile,Fax,Website,Address,City,State,Country,PostalCode,TaxNumber,RegistrationNumber,Type,Status,PaymentTerms,CreditLimit,Currency,BankName,BankAccount,IBAN,SWIFT,Notes,QualityRating,DeliveryRating,ServiceRating,LeadTimeDays,MinimumOrderAmount,IsActive,CreatedAt,CreatedBy")] Supplier supplier)
        {
            if (id != supplier.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    supplier.UpdatedAt = DateTime.UtcNow;
                    var currentUser = await _userManager.GetUserAsync(User);
                    supplier.UpdatedBy = currentUser?.UserName ?? "System";

                    _context.Update(supplier);
                    await _context.SaveChangesAsync();

                    // Send notification
                    if (currentUser != null)
                    {
                        await _notificationService.CreateAndSendNotification(
                            ModelType.Supplier,
                            ProcessType.Edit,
                            supplier.Id,
                            currentUser);
                    }

                    TempData["Success"] = "Supplier updated successfully.";
                    return RedirectToAction(nameof(Index));
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!SupplierExists(supplier.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error updating supplier");
                    TempData["Error"] = "An error occurred while updating the supplier.";
                }
            }
            return View(supplier);
        }

        // GET: Suppliers/Delete/5
        [Authorize(Permissions.Suppliers.Delete)]
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var supplier = await _context.Suppliers
                .Include(s => s.Contacts)
                .Include(s => s.Contracts)
                .Include(s => s.PurchaseOrders)
                .FirstOrDefaultAsync(m => m.Id == id);
            if (supplier == null)
            {
                return NotFound();
            }

            return View(supplier);
        }

        // POST: Suppliers/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        [Authorize(Permissions.Suppliers.Delete)]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            try
            {
                var supplier = await _context.Suppliers
                    .Include(s => s.PurchaseOrders)
                    .FirstOrDefaultAsync(s => s.Id == id);

                if (supplier != null)
                {
                    // Check if supplier has active purchase orders
                    if (supplier.PurchaseOrders?.Any(po => po.Status != POStatus.Cancelled && po.Status != POStatus.Completed) == true)
                    {
                        TempData["Error"] = "Cannot delete supplier with active purchase orders. Please complete or cancel all orders first.";
                        return RedirectToAction(nameof(Delete), new { id });
                    }

                    _context.Suppliers.Remove(supplier);
                    await _context.SaveChangesAsync();

                    // Send notification
                    var currentUser = await _userManager.GetUserAsync(User);
                    if (currentUser != null)
                    {
                        await _notificationService.CreateAndSendNotification(
                            ModelType.Supplier,
                            ProcessType.Delete,
                            id,
                            currentUser);
                    }

                    TempData["Success"] = "Supplier deleted successfully.";
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting supplier");
                TempData["Error"] = "An error occurred while deleting the supplier.";
            }

            return RedirectToAction(nameof(Index));
        }

        // Helper method to generate supplier code
        private async Task<string> GenerateSupplierCode()
        {
            var prefix = "SUP";

            var lastSupplier = await _context.Suppliers
                .Where(s => s.Code.StartsWith(prefix))
                .OrderByDescending(s => s.Code)
                .FirstOrDefaultAsync();

            int nextNumber = 1;
            if (lastSupplier != null && lastSupplier.Code.Length > prefix.Length)
            {
                var numberPart = lastSupplier.Code.Substring(prefix.Length);
                if (int.TryParse(numberPart, out int lastNumber))
                {
                    nextNumber = lastNumber + 1;
                }
            }

            return $"{prefix}{nextNumber:D4}";
        }

        private bool SupplierExists(int id)
        {
            return _context.Suppliers.Any(e => e.Id == id);
        }
    }
}
