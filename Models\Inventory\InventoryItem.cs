using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using ColorOasisSystemWeb.Enums;

namespace ColorOasisSystemWeb.Models.Inventory
{
    public class InventoryItem : Service // Extend existing Service entity
    {
        [Display(Name = "Item Type")]
        public ItemType Type { get; set; } = ItemType.Physical;
        
        [Display(Name = "SKU")]
        [MaxLength(50)]
        public string SKU { get; set; }
        
        [Display(Name = "Reorder Point")]
        [NumericOnly]
        public decimal ReorderPoint { get; set; }
        
        [Display(Name = "Maximum Stock Level")]
        [NumericOnly]
        public decimal MaxStockLevel { get; set; }
        
        [Display(Name = "Preferred Supplier")]
        [MaxLength(100)]
        public string PreferredSupplier { get; set; }
        
        [Display(Name = "Serial Tracked")]
        public bool IsSerialTracked { get; set; } = false;
        
        [Display(Name = "Lot Tracked")]
        public bool IsLotTracked { get; set; } = false;
        
        [Display(Name = "Unit of Measure")]
        [MaxLength(20)]
        public string UnitOfMeasure { get; set; } = "PCS";
        
        [Display(Name = "Lead Time (Days)")]
        public int LeadTimeDays { get; set; } = 0;
        
        [Display(Name = "Safety Stock")]
        [NumericOnly]
        public decimal SafetyStock { get; set; } = 0;
        
        [Display(Name = "ABC Classification")]
        public ABCClassification ABCClass { get; set; } = ABCClassification.C;
        
        [Display(Name = "Last Cost")]
        [NumericOnly]
        public decimal LastCost { get; set; } = 0;
        
        [Display(Name = "Average Cost")]
        [NumericOnly]
        public decimal AverageCost { get; set; } = 0;
        
        [Display(Name = "Standard Cost")]
        [NumericOnly]
        public decimal StandardCost { get; set; } = 0;
        
        // Navigation Properties
        public virtual ICollection<InventoryLocation> Locations { get; set; } = new List<InventoryLocation>();
        public virtual ICollection<InventoryTransaction> Transactions { get; set; } = new List<InventoryTransaction>();
        public virtual ICollection<StockAdjustment> StockAdjustments { get; set; } = new List<StockAdjustment>();
    }

    public class InventoryLocation
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [ForeignKey("InventoryItem")]
        public int ItemId { get; set; }
        public virtual InventoryItem InventoryItem { get; set; }

        [Required]
        [ForeignKey("Warehouse")]
        public int WarehouseId { get; set; }
        public virtual Warehouse Warehouse { get; set; }

        [Display(Name = "Bin Location")]
        [MaxLength(50)]
        public string BinLocation { get; set; }

        [Display(Name = "Quantity On Hand")]
        [NumericOnly]
        public decimal QuantityOnHand { get; set; } = 0;

        [Display(Name = "Quantity Reserved")]
        [NumericOnly]
        public decimal QuantityReserved { get; set; } = 0;

        [Display(Name = "Quantity Available")]
        [NumericOnly]
        public decimal QuantityAvailable => QuantityOnHand - QuantityReserved;

        [Display(Name = "Last Cost")]
        [NumericOnly]
        public decimal LastCost { get; set; } = 0;

        [Display(Name = "Last Updated")]
        public DateTime LastUpdated { get; set; } = DateTime.UtcNow;

        [Display(Name = "Updated By")]
        public string UpdatedBy { get; set; } = "";

        [Display(Name = "Created Date")]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        [Display(Name = "Created By")]
        public string? CreatedBy { get; set; }

        [Display(Name = "Updated Date")]
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        [Display(Name = "Last Updated")]
        public DateTime LastUpdated { get; set; } = DateTime.UtcNow;

        [Display(Name = "Updated By")]
        public string? UpdatedBy { get; set; }
    }

    public class InventoryTransaction
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        [ForeignKey("InventoryItem")]
        public int ItemId { get; set; }
        public virtual InventoryItem? InventoryItem { get; set; }

        [Required]
        [ForeignKey("Warehouse")]
        public int WarehouseId { get; set; }
        public virtual Warehouse? Warehouse { get; set; }
        
        [Display(Name = "Transaction Type")]
        public TransactionType Type { get; set; }
        
        [Display(Name = "Quantity")]
        [NumericOnly]
        public decimal Quantity { get; set; }
        
        [Display(Name = "Transaction Date")]
        public DateTime TransactionDate { get; set; } = DateTime.UtcNow;
        
        [Display(Name = "Reference")]
        [MaxLength(100)]
        public string? Reference { get; set; }

        [Display(Name = "Unit Cost")]
        [NumericOnly]
        public decimal UnitCost { get; set; } = 0;

        [Display(Name = "Total Cost")]
        [NumericOnly]
        public decimal TotalCost => Quantity * UnitCost;

        [Display(Name = "Notes")]
        [MaxLength(500)]
        public string? Notes { get; set; }

        [Display(Name = "Created By")]
        public string? CreatedBy { get; set; }

        [Display(Name = "Batch/Lot Number")]
        [MaxLength(50)]
        public string? BatchLotNumber { get; set; }

        [Display(Name = "Serial Number")]
        [MaxLength(50)]
        public string? SerialNumber { get; set; }
    }

    public class Warehouse
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        [Display(Name = "Warehouse Code")]
        [MaxLength(20)]
        public string Code { get; set; }
        
        [Required]
        [Display(Name = "Warehouse Name")]
        [MaxLength(100)]
        public string Name { get; set; }
        
        [Display(Name = "Address")]
        [MaxLength(200)]
        public string Address { get; set; }
        
        [Display(Name = "Manager")]
        [MaxLength(100)]
        public string Manager { get; set; }
        
        [Display(Name = "Phone")]
        [MaxLength(20)]
        public string Phone { get; set; }
        
        [Display(Name = "Email")]
        [EmailAddress]
        [MaxLength(100)]
        public string Email { get; set; }
        
        [Display(Name = "Is Active")]
        public bool IsActive { get; set; } = true;
        
        [Display(Name = "Is Default")]
        public bool IsDefault { get; set; } = false;

        [Display(Name = "Created Date")]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        [Display(Name = "Updated Date")]
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // Navigation Properties
        public virtual ICollection<BinLocation> BinLocations { get; set; } = new List<BinLocation>();
        public virtual ICollection<InventoryLocation> InventoryLocations { get; set; } = new List<InventoryLocation>();
        public virtual ICollection<InventoryTransaction> Transactions { get; set; } = new List<InventoryTransaction>();
    }

    public class BinLocation
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        [ForeignKey("Warehouse")]
        public int WarehouseId { get; set; }
        public virtual Warehouse? Warehouse { get; set; }

        [Required]
        [Display(Name = "Bin Code")]
        [MaxLength(20)]
        public string Code { get; set; } = "";

        [Display(Name = "Description")]
        [MaxLength(100)]
        public string? Description { get; set; }

        [Display(Name = "Zone")]
        [MaxLength(50)]
        public string? Zone { get; set; }

        [Display(Name = "Aisle")]
        [MaxLength(10)]
        public string? Aisle { get; set; }

        [Display(Name = "Rack")]
        [MaxLength(10)]
        public string? Rack { get; set; }

        [Display(Name = "Shelf")]
        [MaxLength(10)]
        public string? Shelf { get; set; }
        
        [Display(Name = "Is Active")]
        public bool IsActive { get; set; } = true;
        
        [Display(Name = "Capacity")]
        [NumericOnly]
        public decimal Capacity { get; set; } = 0;
        
        [Display(Name = "Current Utilization")]
        [NumericOnly]
        public decimal CurrentUtilization { get; set; } = 0;
    }

    public class StockAdjustment
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        [Display(Name = "Adjustment Number")]
        [MaxLength(50)]
        public string AdjustmentNumber { get; set; }
        
        [Required]
        [ForeignKey("InventoryItem")]
        public int ItemId { get; set; }
        public virtual InventoryItem? InventoryItem { get; set; }

        [Required]
        [ForeignKey("Warehouse")]
        public int WarehouseId { get; set; }
        public virtual Warehouse? Warehouse { get; set; }
        
        [Display(Name = "Adjustment Date")]
        public DateTime AdjustmentDate { get; set; } = DateTime.UtcNow;
        
        [Display(Name = "Reason")]
        public AdjustmentReason Reason { get; set; }
        
        [Display(Name = "Quantity Before")]
        [NumericOnly]
        public decimal QuantityBefore { get; set; }
        
        [Display(Name = "Quantity After")]
        [NumericOnly]
        public decimal QuantityAfter { get; set; }
        
        [Display(Name = "Adjustment Quantity")]
        [NumericOnly]
        public decimal AdjustmentQuantity => QuantityAfter - QuantityBefore;
        
        [Display(Name = "Unit Cost")]
        [NumericOnly]
        public decimal UnitCost { get; set; } = 0;
        
        [Display(Name = "Total Value Impact")]
        [NumericOnly]
        public decimal TotalValueImpact => AdjustmentQuantity * UnitCost;
        
        [Display(Name = "Notes")]
        [MaxLength(500)]
        public string? Notes { get; set; }

        [Display(Name = "Approved By")]
        public string? ApprovedBy { get; set; }

        [Display(Name = "Created By")]
        public string? CreatedBy { get; set; }
        
        [Display(Name = "Is Approved")]
        public bool IsApproved { get; set; } = false;
        
        [Display(Name = "Approval Date")]
        public DateTime? ApprovalDate { get; set; }
    }
}
