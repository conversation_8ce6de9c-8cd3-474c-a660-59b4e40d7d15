using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using ColorOasisSystemWeb.Authorization;

namespace ColorOasisSystemWeb.Controllers
{
    [Authorize]
    public class InventoryTransactionsController : Controller
    {
        [Authorize(Permissions.Inventory.Transfer)]
        public IActionResult Index()
        {
            ViewBag.Title = "Inventory Transactions";
            ViewBag.Message = "View inventory transaction history, transfers, and movement records.";
            return View("~/Views/Shared/ComingSoon.cshtml");
        }
    }
}
