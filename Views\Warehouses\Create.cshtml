@model ColorOasisSystemWeb.Models.Inventory.Warehouse

@{
    ViewData["Title"] = "Create Warehouse";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="card-title">@ViewData["Title"]</h4>
                    <a asp-action="Index" class="btn btn-secondary">
                        <i data-feather="arrow-left" class="feather-icon me-2"></i>
                        Back to List
                    </a>
                </div>
                <div class="card-body">
                    <form asp-action="Create" method="post">
                        <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                        
                        <div class="row">
                            <!-- Basic Information -->
                            <div class="col-md-6">
                                <h5 class="mb-3">Basic Information</h5>
                                
                                <div class="mb-3">
                                    <label asp-for="Code" class="form-label"></label>
                                    <input asp-for="Code" class="form-control" />
                                    <span asp-validation-for="Code" class="text-danger"></span>
                                </div>

                                <div class="mb-3">
                                    <label asp-for="Name" class="form-label"></label>
                                    <input asp-for="Name" class="form-control" />
                                    <span asp-validation-for="Name" class="text-danger"></span>
                                </div>

                                <div class="mb-3">
                                    <label asp-for="Address" class="form-label"></label>
                                    <textarea asp-for="Address" class="form-control" rows="3"></textarea>
                                    <span asp-validation-for="Address" class="text-danger"></span>
                                </div>

                                <div class="mb-3">
                                    <label asp-for="Manager" class="form-label"></label>
                                    <input asp-for="Manager" class="form-control" />
                                    <span asp-validation-for="Manager" class="text-danger"></span>
                                </div>
                            </div>

                            <!-- Contact Information -->
                            <div class="col-md-6">
                                <h5 class="mb-3">Contact Information</h5>
                                
                                <div class="mb-3">
                                    <label asp-for="Phone" class="form-label"></label>
                                    <input asp-for="Phone" class="form-control" type="tel" />
                                    <span asp-validation-for="Phone" class="text-danger"></span>
                                </div>

                                <div class="mb-3">
                                    <label asp-for="Email" class="form-label"></label>
                                    <input asp-for="Email" class="form-control" type="email" />
                                    <span asp-validation-for="Email" class="text-danger"></span>
                                </div>

                                <div class="mb-3">
                                    <h6>Settings</h6>
                                    
                                    <div class="form-check mb-2">
                                        <input asp-for="IsActive" class="form-check-input" type="checkbox" checked />
                                        <label asp-for="IsActive" class="form-check-label"></label>
                                        <small class="form-text text-muted d-block">Active warehouses can be used for inventory operations</small>
                                    </div>

                                    <div class="form-check">
                                        <input asp-for="IsDefault" class="form-check-input" type="checkbox" />
                                        <label asp-for="IsDefault" class="form-check-label"></label>
                                        <small class="form-text text-muted d-block">Default warehouse is used for new inventory items</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="alert alert-info" role="alert">
                                    <h6 class="alert-heading">
                                        <i data-feather="info" class="feather-icon me-2"></i>
                                        Warehouse Setup
                                    </h6>
                                    <p class="mb-0">
                                        After creating the warehouse, you can add bin locations to organize your inventory storage.
                                        Bin locations help track exactly where items are stored within the warehouse.
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-end gap-2">
                                    <a asp-action="Index" class="btn btn-secondary">Cancel</a>
                                    <button type="submit" class="btn btn-primary">
                                        <i data-feather="save" class="feather-icon me-2"></i>
                                        Create Warehouse
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    <script>
        // Initialize Feather icons
        if (typeof feather !== 'undefined') {
            feather.replace();
        }

        // Handle default warehouse checkbox
        $('#IsDefault').on('change', function() {
            if ($(this).is(':checked')) {
                // Show warning about changing default
                if (confirm('Setting this as the default warehouse will remove the default status from any existing default warehouse. Continue?')) {
                    // User confirmed, keep checked
                } else {
                    // User cancelled, uncheck
                    $(this).prop('checked', false);
                }
            }
        });
    </script>
}
