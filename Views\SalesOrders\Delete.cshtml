@model ColorOasisSystemWeb.Models.Sales.SalesOrder

@{
    ViewData["Title"] = "Delete Sales Order";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="nxl-content">
    <!-- [ page-header ] start -->
    <div class="page-header">
        <div class="page-header-left d-flex align-items-center">
            <div class="page-header-title">
                <h5 class="m-b-10">Delete Sales Order</h5>
            </div>
            <ul class="breadcrumb">
                <li class="breadcrumb-item"><a href="@Url.Action("Index", "Home")">Home</a></li>
                <li class="breadcrumb-item">ERP</li>
                <li class="breadcrumb-item">Sales</li>
                <li class="breadcrumb-item"><a href="@Url.Action("Index")">Sales Orders</a></li>
                <li class="breadcrumb-item">Delete</li>
            </ul>
        </div>
        <div class="page-header-right ms-auto">
            <div class="page-header-right-items">
                <div class="d-flex align-items-center gap-2 page-header-right-items-wrapper">
                    <a href="@Url.Action("Index")" class="btn btn-outline-primary">
                        <i class="feather-arrow-left me-2"></i>
                        <span>Back to List</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
    <!-- [ page-header ] end -->

    <!-- [ Main Content ] start -->
    <div class="main-content">
        <div class="row">
            <div class="col-12">
                <div class="card stretch stretch-full">
                    <div class="card-header">
                        <h5 class="card-title text-danger">
                            <i class="feather-alert-triangle me-2"></i>
                            Confirm Deletion
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-danger">
                            <h6 class="alert-heading">Warning!</h6>
                            <p class="mb-0">
                                Are you sure you want to delete this sales order? This action cannot be undone.
                                All related data including order items, shipments, and invoices will be affected.
                            </p>
                        </div>

                        <!-- Order Information -->
                        <div class="row">
                            <div class="col-lg-6">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Order Number</label>
                                    <div class="fw-bold">@Model.OrderNumber</div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Order Date</label>
                                    <div>@Model.OrderDate.ToString("MMM dd, yyyy")</div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Customer</label>
                                    <div class="fw-bold">@Model.Client?.Name</div>
                                    @if (!string.IsNullOrEmpty(Model.Client?.Email))
                                    {
                                        <div class="text-muted">@Model.Client.Email</div>
                                    }
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Required Date</label>
                                    <div>@Model.RequiredDate.ToString("MMM dd, yyyy")</div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Status</label>
                                    <div>
                                        @switch (Model.Status)
                                        {
                                            case ColorOasisSystemWeb.Enums.SalesOrderStatus.Draft:
                                                <span class="badge bg-soft-secondary text-secondary">Draft</span>
                                                break;
                                            case ColorOasisSystemWeb.Enums.SalesOrderStatus.Confirmed:
                                                <span class="badge bg-soft-success text-success">Confirmed</span>
                                                break;
                                            case ColorOasisSystemWeb.Enums.SalesOrderStatus.InProduction:
                                                <span class="badge bg-soft-info text-info">In Production</span>
                                                break;
                                            case ColorOasisSystemWeb.Enums.SalesOrderStatus.ReadyToShip:
                                                <span class="badge bg-soft-primary text-primary">Ready to Ship</span>
                                                break;
                                            case ColorOasisSystemWeb.Enums.SalesOrderStatus.Shipped:
                                                <span class="badge bg-soft-success text-success">Shipped</span>
                                                break;
                                            case ColorOasisSystemWeb.Enums.SalesOrderStatus.Delivered:
                                                <span class="badge bg-soft-success text-success">Delivered</span>
                                                break;
                                            case ColorOasisSystemWeb.Enums.SalesOrderStatus.Cancelled:
                                                <span class="badge bg-soft-danger text-danger">Cancelled</span>
                                                break;
                                            case ColorOasisSystemWeb.Enums.SalesOrderStatus.OnHold:
                                                <span class="badge bg-soft-secondary text-secondary">On Hold</span>
                                                break;
                                        }
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Total Amount</label>
                                    <div class="fw-bold fs-5 text-primary">@Model.TotalAmount.ToString("C")</div>
                                </div>
                            </div>
                        </div>

                        <!-- Order Items Summary -->
                        @if (Model.Items?.Any() == true)
                        {
                            <div class="row">
                                <div class="col-12">
                                    <h6 class="mb-3">Order Items (@Model.Items.Count items)</h6>
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>Item</th>
                                                    <th>Quantity</th>
                                                    <th>Unit Price</th>
                                                    <th>Total</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach (var item in Model.Items.Take(5))
                                                {
                                                    <tr>
                                                        <td>
                                                            <div class="fw-bold">@item.ItemDescription</div>
                                                            @if (!string.IsNullOrEmpty(item.Specifications))
                                                            {
                                                                <small class="text-muted">@item.Specifications</small>
                                                            }
                                                        </td>
                                                        <td>@item.QuantityOrdered @item.UnitOfMeasure</td>
                                                        <td>@item.UnitPrice.ToString("C")</td>
                                                        <td class="fw-bold">@item.LineTotal.ToString("C")</td>
                                                    </tr>
                                                }
                                                @if (Model.Items.Count > 5)
                                                {
                                                    <tr>
                                                        <td colspan="4" class="text-center text-muted">
                                                            ... and @(Model.Items.Count - 5) more items
                                                        </td>
                                                    </tr>
                                                }
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        }

                        <!-- Order Summary -->
                        <div class="row">
                            <div class="col-md-6 offset-md-6">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6 class="card-title">Order Summary</h6>
                                        <div class="d-flex justify-content-between mb-2">
                                            <span>Subtotal:</span>
                                            <span>@Model.Subtotal.ToString("C")</span>
                                        </div>
                                        @if (Model.DiscountAmount > 0)
                                        {
                                            <div class="d-flex justify-content-between mb-2 text-success">
                                                <span>Discount:</span>
                                                <span>-@Model.DiscountAmount.ToString("C")</span>
                                            </div>
                                        }
                                        <div class="d-flex justify-content-between mb-2">
                                            <span>Tax (@Model.TaxRate%):</span>
                                            <span>@Model.TaxAmount.ToString("C")</span>
                                        </div>
                                        @if (Model.ShippingCost > 0)
                                        {
                                            <div class="d-flex justify-content-between mb-2">
                                                <span>Shipping:</span>
                                                <span>@Model.ShippingCost.ToString("C")</span>
                                            </div>
                                        }
                                        <hr>
                                        <div class="d-flex justify-content-between fw-bold">
                                            <span>Total:</span>
                                            <span class="text-primary">@Model.TotalAmount.ToString("C")</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Related Data Warning -->
                        <div class="alert alert-warning">
                            <h6 class="alert-heading">
                                <i class="feather-info me-2"></i>
                                Related Data Impact
                            </h6>
                            <p class="mb-2">Deleting this sales order may affect:</p>
                            <ul class="mb-0">
                                <li>Order items and line details</li>
                                <li>Shipments and delivery records</li>
                                <li>Invoices and payment records</li>
                                <li>Production schedules and planning</li>
                                <li>Customer order history</li>
                            </ul>
                        </div>

                        <!-- Status-specific warnings -->
                        @if (Model.Status != ColorOasisSystemWeb.Enums.SalesOrderStatus.Draft && Model.Status != ColorOasisSystemWeb.Enums.SalesOrderStatus.Cancelled)
                        {
                            <div class="alert alert-danger">
                                <h6 class="alert-heading">
                                    <i class="feather-alert-circle me-2"></i>
                                    Active Order Warning
                                </h6>
                                <p class="mb-0">
                                    This order is currently <strong>@Model.Status</strong>. Deleting an active order may disrupt business operations.
                                    Consider cancelling the order instead of deleting it to maintain audit trail.
                                </p>
                            </div>
                        }

                        <!-- Confirmation Form -->
                        <form asp-action="Delete" method="post" class="mt-4">
                            <input type="hidden" asp-for="Id" />
                            <div class="d-flex justify-content-end gap-2">
                                <a href="@Url.Action("Index")" class="btn btn-outline-secondary">
                                    <i class="feather-x me-2"></i>Cancel
                                </a>
                                <a href="@Url.Action("Details", new { id = Model.Id })" class="btn btn-outline-info">
                                    <i class="feather-eye me-2"></i>View Details
                                </a>
                                @if (Model.Status == ColorOasisSystemWeb.Enums.SalesOrderStatus.Draft || Model.Status == ColorOasisSystemWeb.Enums.SalesOrderStatus.Cancelled)
                                {
                                    <button type="submit" class="btn btn-danger" onclick="return confirm('Are you absolutely sure you want to delete this sales order? This action cannot be undone.')">
                                        <i class="feather-trash-2 me-2"></i>Delete Order
                                    </button>
                                }
                                else
                                {
                                    <button type="submit" class="btn btn-danger" onclick="return confirm('WARNING: This is an active order. Deleting it may disrupt business operations. Are you absolutely sure you want to proceed? This action cannot be undone.')">
                                        <i class="feather-trash-2 me-2"></i>Force Delete Order
                                    </button>
                                }
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- [ Main Content ] end -->
</div>
