using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using ColorOasisSystemWeb.Data;
using ColorOasisSystemWeb.Models.Inventory;
using ColorOasisSystemWeb.Authorization;
using X.PagedList.Extensions;

namespace ColorOasisSystemWeb.Controllers
{
    [Authorize]
    public class InventoryController : Controller
    {
        private readonly AppDbContext _context;
        private readonly ILogger<InventoryController> _logger;

        public InventoryController(AppDbContext context, ILogger<InventoryController> logger)
        {
            _context = context;
            _logger = logger;
        }

        // GET: Inventory
        [Authorize(Policy = Permissions.Inventory.View)]
        public async Task<IActionResult> Index(string searchString, int? page, string sortOrder)
        {
            ViewData["CurrentFilter"] = searchString;
            ViewData["NameSortParm"] = String.IsNullOrEmpty(sortOrder) ? "name_desc" : "";
            ViewData["SKUSortParm"] = sortOrder == "SKU" ? "sku_desc" : "SKU";
            ViewData["PriceSortParm"] = sortOrder == "Price" ? "price_desc" : "Price";

            var inventoryItems = from i in _context.InventoryItems
                                .Include(i => i.ServiceType)
                                .Include(i => i.ServiceCategory)
                                .Include(i => i.Locations)
                                select i;

            if (!String.IsNullOrEmpty(searchString))
            {
                inventoryItems = inventoryItems.Where(i => i.Name.Contains(searchString)
                                                    || i.NameEn.Contains(searchString)
                                                    || i.SKU.Contains(searchString)
                                                    || i.Code.Contains(searchString));
            }

            switch (sortOrder)
            {
                case "name_desc":
                    inventoryItems = inventoryItems.OrderByDescending(i => i.Name);
                    break;
                case "SKU":
                    inventoryItems = inventoryItems.OrderBy(i => i.SKU);
                    break;
                case "sku_desc":
                    inventoryItems = inventoryItems.OrderByDescending(i => i.SKU);
                    break;
                case "Price":
                    inventoryItems = inventoryItems.OrderBy(i => i.Price);
                    break;
                case "price_desc":
                    inventoryItems = inventoryItems.OrderByDescending(i => i.Price);
                    break;
                default:
                    inventoryItems = inventoryItems.OrderBy(i => i.Name);
                    break;
            }

            int pageSize = 10;
            int pageNumber = (page ?? 1);

            var pagedList = await inventoryItems.ToListAsync();
            return View(pagedList.ToPagedList(pageNumber, pageSize));
        }

        // GET: Inventory/Details/5
        [Authorize(Policy = Permissions.Inventory.View)]
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var inventoryItem = await _context.InventoryItems
                .Include(i => i.ServiceType)
                .Include(i => i.ServiceCategory)
                .Include(i => i.Locations)
                    .ThenInclude(l => l.Warehouse)
                .Include(i => i.Transactions.OrderByDescending(t => t.TransactionDate).Take(10))
                .Include(i => i.StockAdjustments.OrderByDescending(a => a.AdjustmentDate).Take(5))
                .FirstOrDefaultAsync(m => m.Id == id);

            if (inventoryItem == null)
            {
                return NotFound();
            }

            return View(inventoryItem);
        }

        // GET: Inventory/Create
        [Authorize(Policy = Permissions.Inventory.Create)]
        public IActionResult Create()
        {
            ViewData["ServiceTypeId"] = new Microsoft.AspNetCore.Mvc.Rendering.SelectList(_context.ServiceTypes, "Id", "Name");
            ViewData["ServiceCategoryId"] = new Microsoft.AspNetCore.Mvc.Rendering.SelectList(_context.ServiceCategories, "Id", "Name");
            return View();
        }

        // POST: Inventory/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        [Authorize(Policy = Permissions.Inventory.Create)]
        public async Task<IActionResult> Create([Bind("Code,Name,NameEn,Photo,MinimumPrice,MaximumPrice,Price,Barcode,Discount,ServiceTypeId,ServiceCategoryId,Type,SKU,ReorderPoint,MaxStockLevel,PreferredSupplier,IsSerialTracked,IsLotTracked,UnitOfMeasure,LeadTimeDays,SafetyStock,ABCClass,LastCost,AverageCost,StandardCost")] InventoryItem inventoryItem)
        {
            if (ModelState.IsValid)
            {
                // Generate SKU if not provided
                if (string.IsNullOrEmpty(inventoryItem.SKU))
                {
                    inventoryItem.SKU = await GenerateSKU(inventoryItem.ServiceCategoryId);
                }

                // Generate Code if not provided
                if (string.IsNullOrEmpty(inventoryItem.Code))
                {
                    inventoryItem.Code = await GenerateCode();
                }

                _context.Add(inventoryItem);
                await _context.SaveChangesAsync();

                _logger.LogInformation($"Inventory item {inventoryItem.Name} created by {User.Identity.Name}");
                
                TempData["SuccessMessage"] = "Inventory item created successfully.";
                return RedirectToAction(nameof(Index));
            }

            ViewData["ServiceTypeId"] = new Microsoft.AspNetCore.Mvc.Rendering.SelectList(_context.ServiceTypes, "Id", "Name", inventoryItem.ServiceTypeId);
            ViewData["ServiceCategoryId"] = new Microsoft.AspNetCore.Mvc.Rendering.SelectList(_context.ServiceCategories, "Id", "Name", inventoryItem.ServiceCategoryId);
            return View(inventoryItem);
        }

        // GET: Inventory/Edit/5
        [Authorize(Policy = Permissions.Inventory.Edit)]
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var inventoryItem = await _context.InventoryItems.FindAsync(id);
            if (inventoryItem == null)
            {
                return NotFound();
            }

            ViewData["ServiceTypeId"] = new Microsoft.AspNetCore.Mvc.Rendering.SelectList(_context.ServiceTypes, "Id", "Name", inventoryItem.ServiceTypeId);
            ViewData["ServiceCategoryId"] = new Microsoft.AspNetCore.Mvc.Rendering.SelectList(_context.ServiceCategories, "Id", "Name", inventoryItem.ServiceCategoryId);
            return View(inventoryItem);
        }

        // POST: Inventory/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        [Authorize(Policy = Permissions.Inventory.Edit)]
        public async Task<IActionResult> Edit(int id, [Bind("Id,Code,Name,NameEn,Photo,MinimumPrice,MaximumPrice,Price,Barcode,Discount,ServiceTypeId,ServiceCategoryId,IsDeleted,Type,SKU,ReorderPoint,MaxStockLevel,PreferredSupplier,IsSerialTracked,IsLotTracked,UnitOfMeasure,LeadTimeDays,SafetyStock,ABCClass,LastCost,AverageCost,StandardCost")] InventoryItem inventoryItem)
        {
            if (id != inventoryItem.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    _context.Update(inventoryItem);
                    await _context.SaveChangesAsync();

                    _logger.LogInformation($"Inventory item {inventoryItem.Name} updated by {User.Identity.Name}");
                    
                    TempData["SuccessMessage"] = "Inventory item updated successfully.";
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!InventoryItemExists(inventoryItem.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
            }

            ViewData["ServiceTypeId"] = new Microsoft.AspNetCore.Mvc.Rendering.SelectList(_context.ServiceTypes, "Id", "Name", inventoryItem.ServiceTypeId);
            ViewData["ServiceCategoryId"] = new Microsoft.AspNetCore.Mvc.Rendering.SelectList(_context.ServiceCategories, "Id", "Name", inventoryItem.ServiceCategoryId);
            return View(inventoryItem);
        }

        // GET: Inventory/Delete/5
        [Authorize(Policy = Permissions.Inventory.Delete)]
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var inventoryItem = await _context.InventoryItems
                .Include(i => i.ServiceType)
                .Include(i => i.ServiceCategory)
                .FirstOrDefaultAsync(m => m.Id == id);

            if (inventoryItem == null)
            {
                return NotFound();
            }

            return View(inventoryItem);
        }

        // POST: Inventory/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        [Authorize(Policy = Permissions.Inventory.Delete)]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var inventoryItem = await _context.InventoryItems.FindAsync(id);
            if (inventoryItem != null)
            {
                // Soft delete
                inventoryItem.IsDeleted = true;
                _context.Update(inventoryItem);
                await _context.SaveChangesAsync();

                _logger.LogInformation($"Inventory item {inventoryItem.Name} deleted by {User.Identity.Name}");
                
                TempData["SuccessMessage"] = "Inventory item deleted successfully.";
            }

            return RedirectToAction(nameof(Index));
        }

        // GET: Inventory/StockLevels
        [Authorize(Policy = Permissions.Inventory.View)]
        public async Task<IActionResult> StockLevels(int? page)
        {
            var stockLevels = await _context.InventoryLocations
                .Include(il => il.InventoryItem)
                .Include(il => il.Warehouse)
                .Where(il => !il.InventoryItem.IsDeleted)
                .GroupBy(il => il.InventoryItem)
                .Select(g => new
                {
                    Item = g.Key,
                    TotalQuantity = g.Sum(il => il.QuantityOnHand),
                    TotalReserved = g.Sum(il => il.QuantityReserved),
                    TotalAvailable = g.Sum(il => il.QuantityAvailable),
                    Locations = g.ToList()
                })
                .ToListAsync();

            int pageSize = 15;
            int pageNumber = (page ?? 1);

            return View(stockLevels.ToPagedList(pageNumber, pageSize));
        }

        // Helper methods
        private bool InventoryItemExists(int id)
        {
            return _context.InventoryItems.Any(e => e.Id == id);
        }

        private async Task<string> GenerateSKU(int categoryId)
        {
            var category = await _context.ServiceCategories.FindAsync(categoryId);
            var categoryCode = category?.Code?.Substring(0, Math.Min(3, category.Code.Length)).ToUpper() ?? "ITM";
            
            var lastItem = await _context.InventoryItems
                .Where(i => i.SKU.StartsWith(categoryCode))
                .OrderByDescending(i => i.SKU)
                .FirstOrDefaultAsync();

            int nextNumber = 1;
            if (lastItem != null && lastItem.SKU.Length > categoryCode.Length)
            {
                var numberPart = lastItem.SKU.Substring(categoryCode.Length);
                if (int.TryParse(numberPart, out int lastNumber))
                {
                    nextNumber = lastNumber + 1;
                }
            }

            return $"{categoryCode}{nextNumber:D4}";
        }

        private async Task<string> GenerateCode()
        {
            var lastItem = await _context.InventoryItems
                .OrderByDescending(i => i.Code)
                .FirstOrDefaultAsync();

            int nextNumber = 1;
            if (lastItem != null && lastItem.Code.StartsWith("INV"))
            {
                var numberPart = lastItem.Code.Substring(3);
                if (int.TryParse(numberPart, out int lastNumber))
                {
                    nextNumber = lastNumber + 1;
                }
            }

            return $"INV{nextNumber:D6}";
        }
    }
}
