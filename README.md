# ColorOasisSystemWeb

A comprehensive business management system designed for service-based companies, featuring real-time communication, multi-language support, and advanced permission management.

## 🚀 Features

### Core Business Management
- **Client & Company Management** - Complete CRM functionality for individual and corporate clients
- **Service Catalog** - Comprehensive service management with categories, types, and pricing
- **Inspection System** - Detailed property inspections with 90+ room types support
- **Quotation Management** - Automated quote generation with detailed line items and pricing
- **Real-time Chat** - Multi-user conversations with file attachments and typing indicators
- **Advanced Notifications** - Strategy-pattern based notification system with user preferences

### Technical Features
- **Multi-language Support** - English and Arabic localization with JSON-based system
- **Role-based Security** - Granular permission system with Admin, Manager, and User roles
- **Real-time Communication** - SignalR-powered chat and notifications
- **Responsive Design** - Modern UI with Bootstrap and custom components
- **Image Processing** - Built-in image handling and optimization

## 🛠️ Technology Stack

- **Backend**: ASP.NET Core 8.0 (C#)
- **Database**: Microsoft SQL Server with Entity Framework Core 8.0.10
- **Authentication**: ASP.NET Core Identity with custom permissions
- **Real-time**: SignalR for WebSocket communication
- **Frontend**: <PERSON><PERSON>, Bootstrap, jQuery
- **Image Processing**: SixLabors.ImageSharp
- **Localization**: Custom JSON-based system

## 📋 Prerequisites

- [.NET 8.0 SDK](https://dotnet.microsoft.com/download/dotnet/8.0)
- [SQL Server](https://www.microsoft.com/en-us/sql-server/sql-server-downloads) (LocalDB or full instance)
- [Node.js](https://nodejs.org/) (for SignalR client packages)
- [Visual Studio 2022](https://visualstudio.microsoft.com/) or [VS Code](https://code.visualstudio.com/)

## 🚀 Getting Started

### 1. Clone the Repository
```bash
git clone <repository-url>
cd ColorOasisSystemWeb
```

### 2. Install Dependencies
```bash
# Restore .NET packages
dotnet restore

# Install Node.js packages
npm install
```

### 3. Configure Database
Update the connection string in `appsettings.json`:
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=.;Database=ColorOasisWebDB;Trusted_Connection=True;MultipleActiveResultSets=true;TrustServerCertificate=true;"
  }
}
```

### 4. Run Database Migrations
```bash
dotnet ef database update
```

### 5. Run the Application
```bash
dotnet run
```

The application will be available at:
- HTTP: `http://localhost:5032`
- HTTPS: `https://localhost:7014`

## 👤 Default Users

The system seeds with a default admin user:
- **Username**: `a7md`
- **Email**: `<EMAIL>`
- **Password**: `Myp@ssw0rdAe`

## 📁 Project Structure

```
ColorOasisSystemWeb/
├── Areas/                          # Admin and Identity areas
│   ├── Admin/                      # Administrative functionality
│   └── Identity/                   # Authentication pages
├── Controllers/                    # MVC Controllers
├── Models/                         # Data models and entities
│   ├── Chat/                       # Chat-related models
│   ├── Client/                     # Client and company models
│   ├── Notification/               # Notification models
│   ├── Service/                    # Service-related models
│   └── User/                       # User models
├── Views/                          # Razor views
├── Data/                           # Database context and repositories
├── Services/                       # Business logic services
├── Hubs/                           # SignalR hubs
├── Authorization/                  # Custom authorization system
├── Resources/                      # Localization files
├── wwwroot/                        # Static files
└── Migrations/                     # EF Core migrations
```

## 🔧 Configuration

### Database Configuration
The application supports both SQL Server and SQLite. Configure your preferred database in `appsettings.json`:

**SQL Server (Recommended for Production):**
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=your-server;Database=ColorOasisWebDB;Trusted_Connection=True;MultipleActiveResultSets=true;TrustServerCertificate=true;"
  }
}
```

**SQLite (Development/Testing):**
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Data Source=ColorOasisWeb.db"
  }
}
```

### Localization
The application supports English and Arabic. Language files are located in `Resources/`:
- `localization.en.json` - English translations
- `localization.ar.json` - Arabic translations

### SignalR Configuration
Real-time features are configured in `Program.cs`. Hub endpoints:
- Chat Hub: `/chatHub`
- Notification Hub: `/notificationHub`

## 🔐 Security & Permissions

### Role-based Access Control
The system implements a granular permission system with three main roles:

#### Admin Role
- Full system access
- User management
- Role and permission management
- System configuration

#### Manager Role
- Service management
- Client relations
- Quotation oversight
- Limited user management

#### User Role
- Basic operations
- View capabilities
- Limited editing rights

### Custom Permissions
Permissions are defined in `Authorization/Permissions.cs` and include:
- Users (View, Create, Edit, Delete)
- Clients (View, Create, Edit, Delete)
- Services (View, Create, Edit, Delete)
- Quotations (View, Create, Edit, Delete)
- Inspections (View, Create, Edit, Delete)
- And more...

## 📊 Database Schema

### Core Entities
- **Users** - System users with roles and permissions
- **Clients** - Individual customers
- **Companies** - Corporate clients
- **Services** - Service catalog with pricing
- **Quotations** - Price quotes with detailed line items
- **Inspections** - Property assessments
- **Chat** - Real-time messaging system
- **Notifications** - System alerts and messages

### Key Relationships
- Users create Quotations and Inspections
- Clients/Companies receive Quotations and Inspections
- Services are included in Quotations and Inspections
- Chat conversations involve multiple participants
- Notifications are sent to specific users

## 🌐 API Endpoints

### Main Routes
- `/` - Dashboard
- `/Clients` - Client management
- `/Companies` - Company management
- `/Services` - Service catalog
- `/Quotations` - Quote management
- `/Inspection` - Property inspections
- `/Chat` - Real-time messaging
- `/Notifications` - User notifications

### Admin Routes (Admin Role Required)
- `/Admin/Dashboard` - Administrative overview
- `/Admin/UserManagement` - User administration
- `/Admin/RoleManagement` - Role management
- `/Admin/PermissionsManagement` - Permission control
- `/Admin/Notification` - System notifications

### SignalR Hubs
- `/chatHub` - Real-time messaging
- `/notificationHub` - Real-time notifications

## 🧪 Testing

### Running Tests
```bash
# Run all tests
dotnet test

# Run tests with coverage
dotnet test --collect:"XPlat Code Coverage"
```

### Test Categories
- **Unit Tests** - Business logic and services
- **Integration Tests** - Database operations and API endpoints
- **SignalR Tests** - Real-time communication functionality

## 🚀 Deployment

### Development Deployment
```bash
# Build the application
dotnet build --configuration Release

# Publish the application
dotnet publish --configuration Release --output ./publish
```

### Production Deployment
1. **Update Configuration**:
   - Set production connection strings
   - Configure HTTPS certificates
   - Set up proper logging providers
   - Configure email services

2. **Database Migration**:
   ```bash
   dotnet ef database update --configuration Release
   ```

3. **Deploy to Server**:
   - Copy published files to server
   - Configure IIS or reverse proxy
   - Set up SSL certificates
   - Configure firewall rules

### Docker Deployment (Optional)
```dockerfile
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src
COPY ["ColorOasisSystemWeb.csproj", "."]
RUN dotnet restore
COPY . .
RUN dotnet build -c Release -o /app/build

FROM build AS publish
RUN dotnet publish -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "ColorOasisSystemWeb.dll"]
```

## 🔧 Development

### Adding New Features
1. **Models** - Add entities in `Models/` directory
2. **Controllers** - Create controllers in `Controllers/` directory
3. **Views** - Add Razor views in `Views/` directory
4. **Services** - Implement business logic in `Services/` directory
5. **Migrations** - Generate database migrations:
   ```bash
   dotnet ef migrations add YourMigrationName
   ```

### Code Style
- Follow C# coding conventions
- Use meaningful variable and method names
- Add XML documentation for public APIs
- Implement proper error handling
- Write unit tests for business logic

### Debugging
- Use Visual Studio debugger for step-through debugging
- Check browser console for JavaScript errors
- Monitor SignalR connections in browser developer tools
- Use SQL Server Profiler for database query analysis

## 📝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Contribution Guidelines
- Follow the existing code style
- Add tests for new functionality
- Update documentation as needed
- Ensure all tests pass before submitting

## 📄 License

This project is licensed under the MIT License - see the [LICENSE.txt](LICENSE.txt) file for details.

## 🆘 Support

### Common Issues

**Database Connection Issues:**
- Verify SQL Server is running
- Check connection string format
- Ensure database exists or can be created

**SignalR Connection Issues:**
- Check browser console for WebSocket errors
- Verify firewall settings
- Ensure proper HTTPS configuration

**Localization Issues:**
- Verify JSON files are properly formatted
- Check culture settings in browser
- Ensure resource files are included in build

### Getting Help
- Check the [Issues](../../issues) page for known problems
- Create a new issue for bugs or feature requests
- Contact the development team for support

## 🙏 Acknowledgments

- ASP.NET Core team for the excellent framework
- SignalR team for real-time communication capabilities
- Entity Framework team for the ORM
- Bootstrap team for the UI framework
- All contributors who have helped improve this project

---

**ColorOasisSystemWeb** - Your Complete Business Solution