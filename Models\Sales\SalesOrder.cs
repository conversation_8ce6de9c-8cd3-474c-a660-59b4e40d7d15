using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using ColorOasisSystemWeb.Enums;
using ColorOasisSystemWeb.Models.Accounting;

namespace ColorOasisSystemWeb.Models.Sales
{
    public class SalesOrder
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        [Display(Name = "Sales Order Number")]
        [MaxLength(50)]
        public string OrderNumber { get; set; } = string.Empty;

        [ForeignKey("Opportunity")]
        public int? OpportunityId { get; set; }
        public virtual Opportunity? Opportunity { get; set; }

        [ForeignKey("Quotation")]
        public int? QuotationId { get; set; }
        public virtual Quotation? Quotation { get; set; }

        [ForeignKey("Client")]
        public int? ClientId { get; set; }
        public virtual Client? Client { get; set; }

        [ForeignKey("Company")]
        public int? CompanyId { get; set; }
        public virtual Company? Company { get; set; }
        
        [Display(Name = "Order Date")]
        public DateTime OrderDate { get; set; } = DateTime.UtcNow;
        
        [Display(Name = "Required Date")]
        public DateTime RequiredDate { get; set; }
        
        [Display(Name = "Promised Date")]
        public DateTime? PromisedDate { get; set; }
        
        [Display(Name = "Shipped Date")]
        public DateTime? ShippedDate { get; set; }
        
        [Display(Name = "Delivered Date")]
        public DateTime? DeliveredDate { get; set; }
        
        [Display(Name = "Sales Order Status")]
        public SalesOrderStatus Status { get; set; } = SalesOrderStatus.Draft;
        
        [Display(Name = "Priority")]
        public Priority Priority { get; set; } = Priority.Normal;
        
        [Display(Name = "Sales Representative")]
        public string? SalesRepresentative { get; set; }

        [Display(Name = "Customer PO Number")]
        [MaxLength(50)]
        public string? CustomerPONumber { get; set; }

        [Display(Name = "Billing Address")]
        [MaxLength(500)]
        public string? BillingAddress { get; set; }

        [Display(Name = "Shipping Address")]
        [MaxLength(500)]
        public string? ShippingAddress { get; set; }

        [Display(Name = "Shipping Method")]
        [MaxLength(100)]
        public string? ShippingMethod { get; set; }

        [Display(Name = "Payment Terms")]
        [MaxLength(200)]
        public string? PaymentTerms { get; set; }
        
        [Display(Name = "Subtotal")]
        [NumericOnly]
        public decimal Subtotal { get; set; } = 0;
        
        [Display(Name = "Tax Amount")]
        [NumericOnly]
        public decimal TaxAmount { get; set; } = 0;
        
        [Display(Name = "Tax Rate")]
        [NumericOnly]
        public decimal TaxRate { get; set; } = 5; // Default 5% VAT
        
        [Display(Name = "Discount Amount")]
        [NumericOnly]
        public decimal DiscountAmount { get; set; } = 0;
        
        [Display(Name = "Shipping Cost")]
        [NumericOnly]
        public decimal ShippingCost { get; set; } = 0;
        
        [Display(Name = "Total Amount")]
        [NumericOnly]
        public decimal TotalAmount { get; set; } = 0;
        
        [Display(Name = "Currency")]
        [MaxLength(3)]
        public string Currency { get; set; } = "AED";
        
        [Display(Name = "Exchange Rate")]
        [NumericOnly]
        public decimal ExchangeRate { get; set; } = 1;
        
        [Display(Name = "Terms and Conditions")]
        [MaxLength(2000)]
        public string TermsAndConditions { get; set; }
        
        [Display(Name = "Internal Notes")]
        [MaxLength(1000)]
        public string InternalNotes { get; set; }
        
        [Display(Name = "Customer Notes")]
        [MaxLength(1000)]
        public string CustomerNotes { get; set; }
        
        [Display(Name = "Special Instructions")]
        [MaxLength(1000)]
        public string SpecialInstructions { get; set; }
        
        [Display(Name = "Created By")]
        public string CreatedBy { get; set; }
        
        [Display(Name = "Created Date")]
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
        
        [Display(Name = "Approved By")]
        public string ApprovedBy { get; set; }
        
        [Display(Name = "Approved Date")]
        public DateTime? ApprovedDate { get; set; }
        
        [Display(Name = "Last Modified Date")]
        public DateTime LastModifiedDate { get; set; } = DateTime.UtcNow;
        
        [Display(Name = "Last Modified By")]
        public string LastModifiedBy { get; set; }
        
        [Display(Name = "Is Invoiced")]
        public bool IsInvoiced { get; set; } = false;
        
        [Display(Name = "Is Shipped")]
        public bool IsShipped { get; set; } = false;
        
        [Display(Name = "Is Delivered")]
        public bool IsDelivered { get; set; } = false;
        
        [Display(Name = "Is Cancelled")]
        public bool IsCancelled { get; set; } = false;
        
        [Display(Name = "Cancellation Reason")]
        [MaxLength(500)]
        public string CancellationReason { get; set; }
        
        [Display(Name = "Cancelled Date")]
        public DateTime? CancelledDate { get; set; }
        
        [Display(Name = "Cancelled By")]
        public string CancelledBy { get; set; }
        
        // Navigation Properties
        public virtual ICollection<SalesOrderItem> Items { get; set; } = new List<SalesOrderItem>();
        public virtual ICollection<SalesOrderApproval> Approvals { get; set; } = new List<SalesOrderApproval>();
        public virtual ICollection<Shipment> Shipments { get; set; } = new List<Shipment>();
        public virtual ICollection<Invoice> Invoices { get; set; } = new List<Invoice>();
    }

    public class SalesOrderItem
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        [ForeignKey("SalesOrder")]
        public int SalesOrderId { get; set; }
        public virtual SalesOrder SalesOrder { get; set; }
        
        [ForeignKey("Service")]
        public int? ServiceId { get; set; }
        public virtual Service Service { get; set; }
        
        [Required]
        [Display(Name = "Item Description")]
        [MaxLength(200)]
        public string ItemDescription { get; set; }
        
        [Display(Name = "Specifications")]
        [MaxLength(1000)]
        public string Specifications { get; set; }
        
        [Display(Name = "Quantity Ordered")]
        [NumericOnly]
        public decimal QuantityOrdered { get; set; }
        
        [Display(Name = "Quantity Shipped")]
        [NumericOnly]
        public decimal QuantityShipped { get; set; } = 0;
        
        [Display(Name = "Quantity Outstanding")]
        [NumericOnly]
        public decimal QuantityOutstanding => QuantityOrdered - QuantityShipped;
        
        [Display(Name = "Unit of Measure")]
        [MaxLength(20)]
        public string UnitOfMeasure { get; set; } = "PCS";
        
        [Display(Name = "Unit Price")]
        [NumericOnly]
        public decimal UnitPrice { get; set; }
        
        [Display(Name = "Discount Percentage")]
        [NumericOnly]
        public decimal DiscountPercentage { get; set; } = 0;
        
        [Display(Name = "Discount Amount")]
        [NumericOnly]
        public decimal DiscountAmount { get; set; } = 0;
        
        [Display(Name = "Line Total")]
        [NumericOnly]
        public decimal LineTotal => (QuantityOrdered * UnitPrice) - DiscountAmount;
        
        [Display(Name = "Required Date")]
        public DateTime RequiredDate { get; set; }
        
        [Display(Name = "Promised Date")]
        public DateTime? PromisedDate { get; set; }
        
        [Display(Name = "Notes")]
        [MaxLength(500)]
        public string Notes { get; set; }
        
        [Display(Name = "Line Number")]
        public int LineNumber { get; set; }
        
        [Display(Name = "Is Shipped")]
        public bool IsShipped => QuantityShipped >= QuantityOrdered;
        
        [Display(Name = "Is Partially Shipped")]
        public bool IsPartiallyShipped => QuantityShipped > 0 && QuantityShipped < QuantityOrdered;
    }

    public class SalesOrderApproval
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        [ForeignKey("SalesOrder")]
        public int SalesOrderId { get; set; }
        public virtual SalesOrder SalesOrder { get; set; }
        
        [Display(Name = "Approver")]
        public string ApproverId { get; set; }
        
        [Display(Name = "Approval Level")]
        public int ApprovalLevel { get; set; }
        
        [Display(Name = "Approval Status")]
        public ApprovalStatus Status { get; set; } = ApprovalStatus.Pending;
        
        [Display(Name = "Approval Date")]
        public DateTime? ApprovalDate { get; set; }
        
        [Display(Name = "Comments")]
        [MaxLength(1000)]
        public string Comments { get; set; }
        
        [Display(Name = "Delegated To")]
        public string DelegatedTo { get; set; }
        
        [Display(Name = "Delegation Date")]
        public DateTime? DelegationDate { get; set; }
    }

    public class Shipment
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        [Display(Name = "Shipment Number")]
        [MaxLength(50)]
        public string ShipmentNumber { get; set; }
        
        [Required]
        [ForeignKey("SalesOrder")]
        public int SalesOrderId { get; set; }
        public virtual SalesOrder SalesOrder { get; set; }
        
        [Display(Name = "Shipment Date")]
        public DateTime ShipmentDate { get; set; } = DateTime.UtcNow;
        
        [Display(Name = "Expected Delivery Date")]
        public DateTime ExpectedDeliveryDate { get; set; }
        
        [Display(Name = "Actual Delivery Date")]
        public DateTime? ActualDeliveryDate { get; set; }
        
        [Display(Name = "Carrier")]
        [MaxLength(100)]
        public string Carrier { get; set; }
        
        [Display(Name = "Tracking Number")]
        [MaxLength(100)]
        public string TrackingNumber { get; set; }
        
        [Display(Name = "Shipping Method")]
        [MaxLength(100)]
        public string ShippingMethod { get; set; }
        
        [Display(Name = "Shipping Address")]
        [MaxLength(500)]
        public string ShippingAddress { get; set; }
        
        [Display(Name = "Shipping Cost")]
        [NumericOnly]
        public decimal ShippingCost { get; set; } = 0;
        
        [Display(Name = "Weight (kg)")]
        [NumericOnly]
        public decimal Weight { get; set; } = 0;
        
        [Display(Name = "Dimensions")]
        [MaxLength(100)]
        public string Dimensions { get; set; }
        
        [Display(Name = "Number of Packages")]
        public int NumberOfPackages { get; set; } = 1;
        
        [Display(Name = "Special Instructions")]
        [MaxLength(1000)]
        public string SpecialInstructions { get; set; }
        
        [Display(Name = "Notes")]
        [MaxLength(1000)]
        public string Notes { get; set; }
        
        [Display(Name = "Is Delivered")]
        public bool IsDelivered { get; set; } = false;
        
        [Display(Name = "Delivered By")]
        public string DeliveredBy { get; set; }
        
        [Display(Name = "Received By")]
        [MaxLength(100)]
        public string ReceivedBy { get; set; }
        
        [Display(Name = "Delivery Signature")]
        [MaxLength(500)]
        public string DeliverySignature { get; set; }
        
        [Display(Name = "Created By")]
        public string CreatedBy { get; set; }
        
        [Display(Name = "Created Date")]
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
        
        // Navigation Properties
        public virtual ICollection<ShipmentItem> Items { get; set; } = new List<ShipmentItem>();
    }

    public class ShipmentItem
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        [ForeignKey("Shipment")]
        public int ShipmentId { get; set; }
        public virtual Shipment Shipment { get; set; }
        
        [Required]
        [ForeignKey("SalesOrderItem")]
        public int SalesOrderItemId { get; set; }
        public virtual SalesOrderItem SalesOrderItem { get; set; }
        
        [Display(Name = "Quantity Shipped")]
        [NumericOnly]
        public decimal QuantityShipped { get; set; }
        
        [Display(Name = "Serial Numbers")]
        [MaxLength(500)]
        public string SerialNumbers { get; set; }
        
        [Display(Name = "Batch/Lot Numbers")]
        [MaxLength(500)]
        public string BatchLotNumbers { get; set; }
        
        [Display(Name = "Notes")]
        [MaxLength(500)]
        public string Notes { get; set; }
        
        [Display(Name = "Line Number")]
        public int LineNumber { get; set; }
    }
}
