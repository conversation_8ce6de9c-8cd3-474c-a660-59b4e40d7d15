@model ColorOasisSystemWeb.Models.Sales.SalesOrder

@{
    ViewData["Title"] = "Create Sales Order";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="nxl-content">
    <!-- [ page-header ] start -->
    <div class="page-header">
        <div class="page-header-left d-flex align-items-center">
            <div class="page-header-title">
                <h5 class="m-b-10">Create New Sales Order</h5>
            </div>
            <ul class="breadcrumb">
                <li class="breadcrumb-item"><a href="@Url.Action("Index", "Home")">Home</a></li>
                <li class="breadcrumb-item">ERP</li>
                <li class="breadcrumb-item">Sales</li>
                <li class="breadcrumb-item"><a href="@Url.Action("Index")">Sales Orders</a></li>
                <li class="breadcrumb-item">Create</li>
            </ul>
        </div>
        <div class="page-header-right ms-auto">
            <div class="page-header-right-items">
                <div class="d-flex align-items-center gap-2 page-header-right-items-wrapper">
                    <a href="@Url.Action("Index")" class="btn btn-outline-primary">
                        <i class="feather-arrow-left me-2"></i>
                        <span>Back to List</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
    <!-- [ page-header ] end -->

    <!-- [ Main Content ] start -->
    <div class="main-content">
        <form asp-action="Create" method="post" id="salesOrderForm">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            
            <div class="row">
                <!-- Order Header -->
                <div class="col-lg-8">
                    <div class="card stretch stretch-full">
                        <div class="card-header">
                            <h5 class="card-title">Order Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label asp-for="OrderNumber" class="form-label"></label>
                                        <input asp-for="OrderNumber" class="form-control" placeholder="Auto-generated if empty" />
                                        <span asp-validation-for="OrderNumber" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label asp-for="OrderDate" class="form-label"></label>
                                        <input asp-for="OrderDate" class="form-control" type="date" />
                                        <span asp-validation-for="OrderDate" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label asp-for="ClientId" class="form-label">Customer</label>
                                        <select asp-for="ClientId" class="form-select" asp-items="ViewBag.Clients">
                                            <option value="">Select Customer</option>
                                        </select>
                                        <span asp-validation-for="ClientId" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label asp-for="RequiredDate" class="form-label"></label>
                                        <input asp-for="RequiredDate" class="form-control" type="date" />
                                        <span asp-validation-for="RequiredDate" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label asp-for="CustomerPONumber" class="form-label"></label>
                                        <input asp-for="CustomerPONumber" class="form-control" placeholder="Customer's PO number" />
                                        <span asp-validation-for="CustomerPONumber" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label asp-for="PaymentTerms" class="form-label"></label>
                                        <select asp-for="PaymentTerms" class="form-select">
                                            <option value="">Select Payment Terms</option>
                                            <option value="Net 30">Net 30 Days</option>
                                            <option value="Net 15">Net 15 Days</option>
                                            <option value="COD">Cash on Delivery</option>
                                            <option value="Advance">Advance Payment</option>
                                            <option value="50% Advance">50% Advance, 50% on Delivery</option>
                                        </select>
                                        <span asp-validation-for="PaymentTerms" class="text-danger"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Order Items -->
                    <div class="card stretch stretch-full">
                        <div class="card-header">
                            <h5 class="card-title">Order Items</h5>
                            <div class="card-header-action">
                                <button type="button" class="btn btn-sm btn-primary" onclick="addOrderItem()">
                                    <i class="feather-plus me-1"></i>Add Item
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="orderItemsContainer">
                                <!-- Order items will be added here dynamically -->
                            </div>
                            <div class="text-center py-4" id="noItemsMessage">
                                <i class="feather-package" style="font-size: 3rem; color: #6c757d;"></i>
                                <h6 class="mt-3 text-muted">No items added yet</h6>
                                <p class="text-muted">Click "Add Item" to start building your order</p>
                            </div>
                        </div>
                    </div>

                    <!-- Addresses -->
                    <div class="card stretch stretch-full">
                        <div class="card-header">
                            <h5 class="card-title">Addresses</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label asp-for="BillingAddress" class="form-label"></label>
                                        <textarea asp-for="BillingAddress" class="form-control" rows="3" placeholder="Enter billing address"></textarea>
                                        <span asp-validation-for="BillingAddress" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label asp-for="ShippingAddress" class="form-label"></label>
                                        <textarea asp-for="ShippingAddress" class="form-control" rows="3" placeholder="Enter shipping address"></textarea>
                                        <span asp-validation-for="ShippingAddress" class="text-danger"></span>
                                        <div class="form-check mt-2">
                                            <input class="form-check-input" type="checkbox" id="sameAsBilling">
                                            <label class="form-check-label" for="sameAsBilling">
                                                Same as billing address
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label asp-for="ShippingMethod" class="form-label"></label>
                                        <select asp-for="ShippingMethod" class="form-select">
                                            <option value="">Select Shipping Method</option>
                                            <option value="Standard">Standard Delivery</option>
                                            <option value="Express">Express Delivery</option>
                                            <option value="Overnight">Overnight Delivery</option>
                                            <option value="Pickup">Customer Pickup</option>
                                        </select>
                                        <span asp-validation-for="ShippingMethod" class="text-danger"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Order Summary & Settings -->
                <div class="col-lg-4">
                    <div class="card stretch stretch-full">
                        <div class="card-header">
                            <h5 class="card-title">Order Summary</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-flex justify-content-between mb-2">
                                <span>Subtotal:</span>
                                <span id="subtotalDisplay">@Model.Subtotal.ToString("C")</span>
                            </div>
                            <div class="mb-3">
                                <label asp-for="DiscountAmount" class="form-label">Discount Amount</label>
                                <input asp-for="DiscountAmount" class="form-control" type="number" step="0.01" min="0" onchange="calculateTotals()" />
                                <span asp-validation-for="DiscountAmount" class="text-danger"></span>
                            </div>
                            <div class="mb-3">
                                <label asp-for="TaxRate" class="form-label">Tax Rate (%)</label>
                                <input asp-for="TaxRate" class="form-control" type="number" step="0.01" min="0" max="100" onchange="calculateTotals()" />
                                <span asp-validation-for="TaxRate" class="text-danger"></span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Tax Amount:</span>
                                <span id="taxAmountDisplay">@Model.TaxAmount.ToString("C")</span>
                            </div>
                            <div class="mb-3">
                                <label asp-for="ShippingCost" class="form-label">Shipping Cost</label>
                                <input asp-for="ShippingCost" class="form-control" type="number" step="0.01" min="0" onchange="calculateTotals()" />
                                <span asp-validation-for="ShippingCost" class="text-danger"></span>
                            </div>
                            <hr>
                            <div class="d-flex justify-content-between fw-bold fs-5">
                                <span>Total:</span>
                                <span class="text-primary" id="totalAmountDisplay">@Model.TotalAmount.ToString("C")</span>
                            </div>
                            
                            <!-- Hidden fields for calculated values -->
                            <input asp-for="Subtotal" type="hidden" />
                            <input asp-for="TaxAmount" type="hidden" />
                            <input asp-for="TotalAmount" type="hidden" />
                        </div>
                    </div>

                    <div class="card stretch stretch-full">
                        <div class="card-header">
                            <h5 class="card-title">Settings</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label asp-for="Currency" class="form-label"></label>
                                <select asp-for="Currency" class="form-select">
                                    <option value="AED">AED - UAE Dirham</option>
                                    <option value="USD">USD - US Dollar</option>
                                    <option value="EUR">EUR - Euro</option>
                                    <option value="GBP">GBP - British Pound</option>
                                </select>
                                <span asp-validation-for="Currency" class="text-danger"></span>
                            </div>
                            <div class="mb-3">
                                <label asp-for="ExchangeRate" class="form-label"></label>
                                <input asp-for="ExchangeRate" class="form-control" type="number" step="0.0001" min="0" />
                                <span asp-validation-for="ExchangeRate" class="text-danger"></span>
                            </div>
                            <div class="mb-3">
                                <label asp-for="Status" class="form-label"></label>
                                <select asp-for="Status" class="form-select" asp-items="Html.GetEnumSelectList<ColorOasisSystemWeb.Enums.SalesOrderStatus>()">
                                    <option value="">Select Status</option>
                                </select>
                                <span asp-validation-for="Status" class="text-danger"></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Notes & Terms -->
            <div class="row">
                <div class="col-12">
                    <div class="card stretch stretch-full">
                        <div class="card-header">
                            <h5 class="card-title">Additional Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label asp-for="Notes" class="form-label"></label>
                                        <textarea asp-for="Notes" class="form-control" rows="4" placeholder="Internal notes about this order..."></textarea>
                                        <span asp-validation-for="Notes" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label asp-for="TermsAndConditions" class="form-label"></label>
                                        <textarea asp-for="TermsAndConditions" class="form-control" rows="4" placeholder="Terms and conditions for this order..."></textarea>
                                        <span asp-validation-for="TermsAndConditions" class="text-danger"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="row">
                <div class="col-12">
                    <div class="card stretch stretch-full">
                        <div class="card-body">
                            <div class="d-flex justify-content-end gap-2">
                                <a href="@Url.Action("Index")" class="btn btn-outline-secondary">
                                    <i class="feather-x me-2"></i>Cancel
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="feather-save me-2"></i>Create Sales Order
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <!-- [ Main Content ] end -->
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    <script>
        let itemIndex = 0;

        // Initialize form
        $(document).ready(function() {
            // Set default dates
            if (!$('#OrderDate').val()) {
                $('#OrderDate').val(new Date().toISOString().split('T')[0]);
            }
            if (!$('#RequiredDate').val()) {
                const futureDate = new Date();
                futureDate.setDate(futureDate.getDate() + 7);
                $('#RequiredDate').val(futureDate.toISOString().split('T')[0]);
            }

            // Same as billing address checkbox
            $('#sameAsBilling').change(function() {
                if (this.checked) {
                    $('#ShippingAddress').val($('#BillingAddress').val());
                }
            });

            // Copy billing to shipping when billing changes
            $('#BillingAddress').on('input', function() {
                if ($('#sameAsBilling').is(':checked')) {
                    $('#ShippingAddress').val($(this).val());
                }
            });
        });

        function addOrderItem() {
            const container = $('#orderItemsContainer');
            const itemHtml = `
                <div class="order-item border rounded p-3 mb-3" data-index="${itemIndex}">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h6 class="mb-0">Item ${itemIndex + 1}</h6>
                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeOrderItem(${itemIndex})">
                            <i class="feather-trash-2"></i>
                        </button>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Item Description</label>
                                <input name="Items[${itemIndex}].ItemDescription" class="form-control" required />
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Specifications</label>
                                <input name="Items[${itemIndex}].Specifications" class="form-control" />
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label">Quantity</label>
                                <input name="Items[${itemIndex}].QuantityOrdered" class="form-control item-quantity" type="number" step="0.01" min="0" required onchange="calculateItemTotal(${itemIndex})" />
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label">Unit of Measure</label>
                                <select name="Items[${itemIndex}].UnitOfMeasure" class="form-select">
                                    <option value="PCS">PCS</option>
                                    <option value="KG">KG</option>
                                    <option value="M">Meter</option>
                                    <option value="SQM">Square Meter</option>
                                    <option value="LTR">Liter</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label">Unit Price</label>
                                <input name="Items[${itemIndex}].UnitPrice" class="form-control item-price" type="number" step="0.01" min="0" required onchange="calculateItemTotal(${itemIndex})" />
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label">Line Total</label>
                                <input class="form-control item-total" readonly />
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="mb-3">
                                <label class="form-label">Required Date</label>
                                <input name="Items[${itemIndex}].RequiredDate" class="form-control" type="date" required />
                            </div>
                        </div>
                    </div>
                    <input name="Items[${itemIndex}].LineNumber" type="hidden" value="${itemIndex + 1}" />
                </div>
            `;
            
            container.append(itemHtml);
            $('#noItemsMessage').hide();
            itemIndex++;
        }

        function removeOrderItem(index) {
            $(`.order-item[data-index="${index}"]`).remove();
            if ($('.order-item').length === 0) {
                $('#noItemsMessage').show();
            }
            calculateTotals();
        }

        function calculateItemTotal(index) {
            const quantity = parseFloat($(`.order-item[data-index="${index}"] .item-quantity`).val()) || 0;
            const price = parseFloat($(`.order-item[data-index="${index}"] .item-price`).val()) || 0;
            const total = quantity * price;
            
            $(`.order-item[data-index="${index}"] .item-total`).val(total.toFixed(2));
            calculateTotals();
        }

        function calculateTotals() {
            let subtotal = 0;
            $('.item-total').each(function() {
                subtotal += parseFloat($(this).val()) || 0;
            });

            const discountAmount = parseFloat($('#DiscountAmount').val()) || 0;
            const taxRate = parseFloat($('#TaxRate').val()) || 0;
            const shippingCost = parseFloat($('#ShippingCost').val()) || 0;

            const taxableAmount = subtotal - discountAmount;
            const taxAmount = (taxableAmount * taxRate) / 100;
            const totalAmount = taxableAmount + taxAmount + shippingCost;

            // Update display
            $('#subtotalDisplay').text(subtotal.toLocaleString('en-US', {style: 'currency', currency: 'AED'}));
            $('#taxAmountDisplay').text(taxAmount.toLocaleString('en-US', {style: 'currency', currency: 'AED'}));
            $('#totalAmountDisplay').text(totalAmount.toLocaleString('en-US', {style: 'currency', currency: 'AED'}));

            // Update hidden fields
            $('#Subtotal').val(subtotal.toFixed(2));
            $('#TaxAmount').val(taxAmount.toFixed(2));
            $('#TotalAmount').val(totalAmount.toFixed(2));
        }
    </script>
}
