@model IEnumerable<ColorOasisSystemWeb.Models.Sales.SalesOrder>

@{
    ViewData["Title"] = "Sales Orders";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="nxl-content">
    <!-- [ page-header ] start -->
    <div class="page-header">
        <div class="page-header-left d-flex align-items-center">
            <div class="page-header-title">
                <h5 class="m-b-10">Sales Orders</h5>
            </div>
            <ul class="breadcrumb">
                <li class="breadcrumb-item"><a href="@Url.Action("Index", "Home")">Home</a></li>
                <li class="breadcrumb-item">ERP</li>
                <li class="breadcrumb-item">Sales</li>
                <li class="breadcrumb-item">Sales Orders</li>
            </ul>
        </div>
        <div class="page-header-right ms-auto">
            <div class="page-header-right-items">
                <div class="d-flex d-md-none">
                    <a href="javascript:void(0)" class="page-header-right-close-toggle">
                        <i class="feather-arrow-left me-2"></i>
                        <span>Back</span>
                    </a>
                </div>
                <div class="d-flex align-items-center gap-2 page-header-right-items-wrapper">
                    <a href="@Url.Action("Create")" class="btn btn-primary">
                        <i class="feather-plus me-2"></i>
                        <span>Create Sales Order</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
    <!-- [ page-header ] end -->

    <!-- [ Main Content ] start -->
    <div class="main-content">
        <div class="row">
            <div class="col-lg-12">
                <div class="card stretch stretch-full">
                    <div class="card-header">
                        <h5 class="card-title">Sales Orders</h5>
                        <div class="card-header-action">
                            <div class="card-header-btn">
                                <div data-bs-toggle="tooltip" title="Refresh">
                                    <a href="javascript:void(0);" class="avatar-text avatar-md" onclick="location.reload();">
                                        <i class="feather-refresh-cw"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body custom-card-action p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0" id="salesOrdersTable">
                                <thead>
                                    <tr>
                                        <th>Order Number</th>
                                        <th>Customer</th>
                                        <th>Order Date</th>
                                        <th>Required Date</th>
                                        <th>Total Amount</th>
                                        <th>Status</th>
                                        <th class="text-end">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var order in Model)
                                    {
                                        <tr>
                                            <td>
                                                <div class="fw-bold text-dark">@order.OrderNumber</div>
                                                <div class="fs-12 text-muted">@order.Items?.Count() item(s)</div>
                                            </td>
                                            <td>
                                                <div class="fw-bold text-dark">@order.Client?.Name</div>
                                                @if (!string.IsNullOrEmpty(order.Client?.Email))
                                                {
                                                    <div class="fs-12 text-muted">@order.Client.Email</div>
                                                }
                                            </td>
                                            <td>
                                                <div class="fw-bold">@order.OrderDate.ToString("MMM dd, yyyy")</div>
                                            </td>
                                            <td>
                                                <div class="fw-bold">@order.RequiredDate.ToString("MMM dd, yyyy")</div>
                                                @{
                                                    var daysUntilRequired = (order.RequiredDate - DateTime.Now).Days;
                                                    var requiredClass = daysUntilRequired < 0 ? "text-danger" : daysUntilRequired <= 3 ? "text-warning" : "text-success";
                                                }
                                                <div class="fs-12 @requiredClass">
                                                    @if (daysUntilRequired < 0)
                                                    {
                                                        <span>@Math.Abs(daysUntilRequired) days overdue</span>
                                                    }
                                                    else if (daysUntilRequired == 0)
                                                    {
                                                        <span>Due today</span>
                                                    }
                                                    else
                                                    {
                                                        <span>@daysUntilRequired days remaining</span>
                                                    }
                                                </div>
                                            </td>
                                            <td>
                                                <div class="fw-bold">@order.TotalAmount.ToString("C")</div>
                                                @if (order.TaxAmount > 0)
                                                {
                                                    <div class="fs-12 text-muted">+@order.TaxAmount.ToString("C") tax</div>
                                                }
                                            </td>
                                            <td>
                                                @switch (order.Status)
                                                {
                                                    case ColorOasisSystemWeb.Enums.SalesOrderStatus.Draft:
                                                        <span class="badge bg-soft-secondary text-secondary">Draft</span>
                                                        break;
                                                    case ColorOasisSystemWeb.Enums.SalesOrderStatus.Confirmed:
                                                        <span class="badge bg-soft-success text-success">Confirmed</span>
                                                        break;
                                                    case ColorOasisSystemWeb.Enums.SalesOrderStatus.InProduction:
                                                        <span class="badge bg-soft-info text-info">In Production</span>
                                                        break;
                                                    case ColorOasisSystemWeb.Enums.SalesOrderStatus.ReadyToShip:
                                                        <span class="badge bg-soft-primary text-primary">Ready to Ship</span>
                                                        break;

                                                    case ColorOasisSystemWeb.Enums.SalesOrderStatus.Shipped:
                                                        <span class="badge bg-soft-success text-success">Shipped</span>
                                                        break;
                                                    case ColorOasisSystemWeb.Enums.SalesOrderStatus.Delivered:
                                                        <span class="badge bg-soft-success text-success">Delivered</span>
                                                        break;
                                                    case ColorOasisSystemWeb.Enums.SalesOrderStatus.Cancelled:
                                                        <span class="badge bg-soft-danger text-danger">Cancelled</span>
                                                        break;
                                                    case ColorOasisSystemWeb.Enums.SalesOrderStatus.OnHold:
                                                        <span class="badge bg-soft-secondary text-secondary">On Hold</span>
                                                        break;
                                                }
                                            </td>
                                            <td>
                                                <div class="hstack gap-2 justify-content-end">
                                                    <a href="@Url.Action("Details", new { id = order.Id })" class="avatar-text avatar-md" data-bs-toggle="tooltip" title="View Details">
                                                        <i class="feather-eye"></i>
                                                    </a>
                                                    @if (order.Status == ColorOasisSystemWeb.Enums.SalesOrderStatus.Draft)
                                                    {
                                                        <a href="@Url.Action("Edit", new { id = order.Id })" class="avatar-text avatar-md" data-bs-toggle="tooltip" title="Edit">
                                                            <i class="feather-edit-3"></i>
                                                        </a>
                                                    }
                                                    <div class="dropdown">
                                                        <a href="javascript:void(0);" class="avatar-text avatar-md" data-bs-toggle="dropdown" data-bs-offset="0,10">
                                                            <i class="feather-more-horizontal"></i>
                                                        </a>
                                                        <div class="dropdown-menu dropdown-menu-end">
                                                            @if (order.Status == ColorOasisSystemWeb.Enums.SalesOrderStatus.Draft)
                                                            {
                                                                <a class="dropdown-item" href="@Url.Action("Confirm", new { id = order.Id })">
                                                                    <i class="feather-check me-3"></i>
                                                                    <span>Confirm Order</span>
                                                                </a>
                                                            }
                                                            @if (order.Status == ColorOasisSystemWeb.Enums.SalesOrderStatus.Confirmed)
                                                            {
                                                                <a class="dropdown-item" href="@Url.Action("StartProduction", new { id = order.Id })">
                                                                    <i class="feather-play me-3"></i>
                                                                    <span>Start Production</span>
                                                                </a>
                                                            }
                                                            @if (order.Status != ColorOasisSystemWeb.Enums.SalesOrderStatus.Cancelled && order.Status != ColorOasisSystemWeb.Enums.SalesOrderStatus.Paid)
                                                            {
                                                                <a class="dropdown-item text-danger" href="@Url.Action("Cancel", new { id = order.Id })">
                                                                    <i class="feather-x me-3"></i>
                                                                    <span>Cancel</span>
                                                                </a>
                                                            }
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- [ Main Content ] end -->
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            $('#salesOrdersTable').DataTable({
                responsive: true,
                pageLength: 25,
                order: [[0, 'desc']],
                columnDefs: [
                    { orderable: false, targets: [6] }
                ]
            });
        });
    </script>
}
