@{
    ViewData["Title"] = "ERP Dashboard";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="nxl-content">
    <!-- [ page-header ] start -->
    <div class="page-header">
        <div class="page-header-left d-flex align-items-center">
            <div class="page-header-title">
                <h5 class="m-b-10">ERP Dashboard</h5>
            </div>
            <ul class="breadcrumb">
                <li class="breadcrumb-item"><a href="@Url.Action("Index", "Home")">Home</a></li>
                <li class="breadcrumb-item">ERP</li>
                <li class="breadcrumb-item">Dashboard</li>
            </ul>
        </div>
    </div>
    <!-- [ page-header ] end -->

    <!-- [ Main Content ] start -->
    <div class="main-content">
        <div class="row">
            <div class="col-lg-12">
                <div class="card stretch stretch-full">
                    <div class="card-body">
                        <div class="text-center py-5">
                            <div class="mb-4">
                                <i class="feather-pie-chart" style="font-size: 4rem; color: #6c757d;"></i>
                            </div>
                            <h4 class="mb-3">ERP Dashboard</h4>
                            <p class="text-muted mb-4">
                                The ERP Dashboard is coming soon! This will provide a comprehensive overview of all your business operations.
                            </p>
                            <div class="alert alert-info">
                                <i class="feather-info me-2"></i>
                                <strong>Coming Soon:</strong> Real-time KPIs, inventory levels, sales metrics, financial summaries, and operational insights.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- [ Main Content ] end -->
</div>
