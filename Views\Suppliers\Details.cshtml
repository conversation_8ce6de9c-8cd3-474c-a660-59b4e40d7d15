@model ColorOasisSystemWeb.Models.Purchasing.Supplier

@{
    ViewData["Title"] = "Supplier Details";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="nxl-content">
    <!-- [ page-header ] start -->
    <div class="page-header">
        <div class="page-header-left d-flex align-items-center">
            <div class="page-header-title">
                <h5 class="m-b-10">Supplier Details</h5>
            </div>
            <ul class="breadcrumb">
                <li class="breadcrumb-item"><a href="@Url.Action("Index", "Home")">Home</a></li>
                <li class="breadcrumb-item">ERP</li>
                <li class="breadcrumb-item">Purchasing</li>
                <li class="breadcrumb-item"><a href="@Url.Action("Index")">Suppliers</a></li>
                <li class="breadcrumb-item">Details</li>
            </ul>
        </div>
        <div class="page-header-right ms-auto">
            <div class="page-header-right-items">
                <div class="d-flex align-items-center gap-2 page-header-right-items-wrapper">
                    <a href="@Url.Action("Edit", new { id = Model.Id })" class="btn btn-primary">
                        <i class="feather-edit-3 me-2"></i>
                        <span>Edit Supplier</span>
                    </a>
                    <a href="@Url.Action("Index")" class="btn btn-outline-primary">
                        <i class="feather-arrow-left me-2"></i>
                        <span>Back to List</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
    <!-- [ page-header ] end -->

    <!-- [ Main Content ] start -->
    <div class="main-content">
        <div class="row">
            <!-- Basic Information -->
            <div class="col-lg-8">
                <div class="card stretch stretch-full">
                    <div class="card-header">
                        <h5 class="card-title">Basic Information</h5>
                        <div class="card-header-action">
                            @switch (Model.Status)
                            {
                                case ColorOasisSystemWeb.Enums.SupplierStatus.Active:
                                    <span class="badge bg-soft-success text-success">Active</span>
                                    break;
                                case ColorOasisSystemWeb.Enums.SupplierStatus.Inactive:
                                    <span class="badge bg-soft-secondary text-secondary">Inactive</span>
                                    break;
                                case ColorOasisSystemWeb.Enums.SupplierStatus.Blacklisted:
                                    <span class="badge bg-soft-danger text-danger">Blacklisted</span>
                                    break;
                                case ColorOasisSystemWeb.Enums.SupplierStatus.Pending:
                                    <span class="badge bg-soft-warning text-warning">Pending</span>
                                    break;
                            }
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Supplier Code</label>
                                    <div class="fw-bold">@Model.Code</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Supplier Name</label>
                                    <div class="fw-bold">@Model.Name</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Contact Person</label>
                                    <div>@Model.ContactPerson</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Supplier Type</label>
                                    <div><span class="badge bg-soft-info text-info">@Model.Type</span></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Email</label>
                                    <div><a href="mailto:@Model.Email">@Model.Email</a></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Phone</label>
                                    <div><a href="tel:@Model.Phone">@Model.Phone</a></div>
                                </div>
                            </div>
                            @if (!string.IsNullOrEmpty(Model.Mobile))
                            {
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label text-muted">Mobile</label>
                                        <div><a href="tel:@Model.Mobile">@Model.Mobile</a></div>
                                    </div>
                                </div>
                            }
                            @if (!string.IsNullOrEmpty(Model.Website))
                            {
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label text-muted">Website</label>
                                        <div><a href="@Model.Website" target="_blank">@Model.Website</a></div>
                                    </div>
                                </div>
                            }
                            @if (!string.IsNullOrEmpty(Model.Address))
                            {
                                <div class="col-12">
                                    <div class="mb-3">
                                        <label class="form-label text-muted">Address</label>
                                        <div>@Model.Address</div>
                                        @if (!string.IsNullOrEmpty(Model.Address))
                                        {
                                            <div class="text-muted">Complete address information</div>
                                        }
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>

            <!-- Ratings & Performance -->
            <div class="col-lg-4">
                <div class="card stretch stretch-full">
                    <div class="card-header">
                        <h5 class="card-title">Performance Ratings</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label text-muted">Quality Rating</label>
                            <div class="d-flex align-items-center">
                                @for (int i = 1; i <= 5; i++)
                                {
                                    if (i <= Model.QualityRating)
                                    {
                                        <i class="feather-star text-warning" style="fill: currentColor;"></i>
                                    }
                                    else
                                    {
                                        <i class="feather-star text-muted"></i>
                                    }
                                }
                                <span class="ms-2">(@Model.QualityRating/5)</span>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-muted">Delivery Rating</label>
                            <div class="d-flex align-items-center">
                                @for (int i = 1; i <= 5; i++)
                                {
                                    if (i <= Model.DeliveryRating)
                                    {
                                        <i class="feather-star text-warning" style="fill: currentColor;"></i>
                                    }
                                    else
                                    {
                                        <i class="feather-star text-muted"></i>
                                    }
                                }
                                <span class="ms-2">(@Model.DeliveryRating/5)</span>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-muted">Service Rating</label>
                            <div class="d-flex align-items-center">
                                @for (int i = 1; i <= 5; i++)
                                {
                                    if (i <= Model.ServiceRating)
                                    {
                                        <i class="feather-star text-warning" style="fill: currentColor;"></i>
                                    }
                                    else
                                    {
                                        <i class="feather-star text-muted"></i>
                                    }
                                }
                                <span class="ms-2">(@Model.ServiceRating/5)</span>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-muted">Performance Score</label>
                            <div class="fw-bold">@Model.PerformanceScore%</div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-muted">Lead Time</label>
                            <div>@Model.LeadTimeDays days</div>
                        </div>
                    </div>
                </div>

                <!-- Financial Information -->
                <div class="card stretch stretch-full">
                    <div class="card-header">
                        <h5 class="card-title">Financial Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label text-muted">Credit Limit</label>
                            <div class="fw-bold">@Model.CreditLimit.ToString("C")</div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-muted">Current Balance</label>
                            <div class="fw-bold">@Model.CurrentBalance.ToString("C")</div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-muted">Payment Terms</label>
                            <div>@Model.PaymentTermsDays days</div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-muted">Minimum Order</label>
                            <div>@Model.MinimumOrderAmount.ToString("C")</div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-muted">Currency</label>
                            <div>@Model.Currency</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        @if (!string.IsNullOrEmpty(Model.Notes))
        {
            <div class="row">
                <div class="col-12">
                    <div class="card stretch stretch-full">
                        <div class="card-header">
                            <h5 class="card-title">Notes</h5>
                        </div>
                        <div class="card-body">
                            <p>@Model.Notes</p>
                        </div>
                    </div>
                </div>
            </div>
        }

        <!-- Audit Information -->
        <div class="row">
            <div class="col-12">
                <div class="card stretch stretch-full">
                    <div class="card-header">
                        <h5 class="card-title">Audit Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Created Date</label>
                                    <div>@Model.CreatedDate.ToString("MMM dd, yyyy HH:mm")</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Created By</label>
                                    <div>@Model.CreatedBy</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Last Modified</label>
                                    <div>@Model.LastModifiedDate.ToString("MMM dd, yyyy HH:mm")</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Modified By</label>
                                    <div>@Model.LastModifiedBy</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- [ Main Content ] end -->
</div>
