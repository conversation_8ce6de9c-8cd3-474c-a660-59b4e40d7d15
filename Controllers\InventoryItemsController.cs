using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using ColorOasisSystemWeb.Authorization;

namespace ColorOasisSystemWeb.Controllers
{
    [Authorize]
    public class InventoryItemsController : Controller
    {
        [Authorize(Permissions.Inventory.View)]
        public IActionResult Index()
        {
            ViewBag.Title = "Inventory Items";
            ViewBag.Message = "Manage your inventory items, track stock levels, and monitor item details.";
            return View("~/Views/Shared/ComingSoon.cshtml");
        }

        [Authorize(Permissions.Inventory.Create)]
        public IActionResult Create()
        {
            ViewBag.Title = "Add Inventory Item";
            ViewBag.Message = "Add new inventory item functionality will be implemented here.";
            return View("~/Views/Shared/ComingSoon.cshtml");
        }
    }
}
