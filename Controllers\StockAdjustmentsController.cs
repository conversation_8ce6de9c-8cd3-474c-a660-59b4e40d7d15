using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using ColorOasisSystemWeb.Data;
using ColorOasisSystemWeb.Models.Inventory;
using ColorOasisSystemWeb.Authorization;
using Microsoft.AspNetCore.Identity;
using ColorOasisSystemWeb.Models;
using ColorOasisSystemWeb.AdvancedNotification.Services;
using ColorOasisSystemWeb.Enums;
using Microsoft.AspNetCore.Mvc.Rendering;

namespace ColorOasisSystemWeb.Controllers
{
    [Authorize]
    public class StockAdjustmentsController : Controller
    {
        private readonly AppDbContext _context;
        private readonly UserManager<User> _userManager;
        private readonly NotificationService _notificationService;
        private readonly ILogger<StockAdjustmentsController> _logger;

        public StockAdjustmentsController(
            AppDbContext context,
            UserManager<User> userManager,
            NotificationService notificationService,
            ILogger<StockAdjustmentsController> logger)
        {
            _context = context;
            _userManager = userManager;
            _notificationService = notificationService;
            _logger = logger;
        }

        // GET: StockAdjustments
        [Authorize(Permissions.Inventory.StockAdjustment)]
        public async Task<IActionResult> Index()
        {
            try
            {
                var adjustments = await _context.StockAdjustments
                    .Include(s => s.InventoryItem)
                    .Include(s => s.Warehouse)
                    .OrderByDescending(s => s.AdjustmentDate)
                    .ToListAsync();

                return View(adjustments);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving stock adjustments");
                TempData["Error"] = "An error occurred while retrieving stock adjustments.";
                return View(new List<StockAdjustment>());
            }
        }

        // GET: StockAdjustments/Details/5
        [Authorize(Permissions.Inventory.StockAdjustment)]
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var stockAdjustment = await _context.StockAdjustments
                .Include(s => s.InventoryItem)
                .Include(s => s.Warehouse)
                .FirstOrDefaultAsync(m => m.Id == id);

            if (stockAdjustment == null)
            {
                return NotFound();
            }

            return View(stockAdjustment);
        }

        // GET: StockAdjustments/Create
        [Authorize(Permissions.Inventory.StockAdjustment)]
        public IActionResult Create()
        {
            ViewData["ItemId"] = new SelectList(_context.InventoryItems.Where(i => !i.IsDeleted), "Id", "Name");
            ViewData["WarehouseId"] = new SelectList(_context.Warehouses.Where(w => w.IsActive), "Id", "Name");
            return View();
        }

        // POST: StockAdjustments/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        [Authorize(Permissions.Inventory.StockAdjustment)]
        public async Task<IActionResult> Create([Bind("ItemId,WarehouseId,Reason,QuantityBefore,QuantityAfter,UnitCost,Notes")] StockAdjustment stockAdjustment)
        {
            if (ModelState.IsValid)
            {
                try
                {
                    // Generate adjustment number
                    stockAdjustment.AdjustmentNumber = await GenerateAdjustmentNumber();
                    stockAdjustment.AdjustmentDate = DateTime.UtcNow;

                    var currentUser = await _userManager.GetUserAsync(User);
                    stockAdjustment.CreatedBy = currentUser?.UserName ?? "System";

                    _context.Add(stockAdjustment);

                    // Create inventory transaction
                    var transaction = new InventoryTransaction
                    {
                        ItemId = stockAdjustment.ItemId,
                        WarehouseId = stockAdjustment.WarehouseId,
                        Type = TransactionType.Adjustment,
                        Quantity = stockAdjustment.QuantityAfter - stockAdjustment.QuantityBefore,
                        TransactionDate = stockAdjustment.AdjustmentDate,
                        Reference = stockAdjustment.AdjustmentNumber,
                        UnitCost = stockAdjustment.UnitCost,
                        Notes = $"Stock adjustment: {stockAdjustment.Reason}",
                        CreatedBy = stockAdjustment.CreatedBy
                    };

                    _context.InventoryTransactions.Add(transaction);

                    // Update inventory location
                    var location = await _context.InventoryLocations
                        .FirstOrDefaultAsync(l => l.ItemId == stockAdjustment.ItemId && l.WarehouseId == stockAdjustment.WarehouseId);

                    if (location != null)
                    {
                        location.QuantityOnHand = stockAdjustment.QuantityAfter;
                        location.UpdatedAt = DateTime.UtcNow;
                        location.UpdatedBy = stockAdjustment.CreatedBy;
                    }

                    await _context.SaveChangesAsync();

                    // Send notification
                    if (currentUser != null)
                    {
                        await _notificationService.CreateAndSendNotification(
                            ModelType.InventoryItem,
                            ProcessType.Edit,
                            stockAdjustment.ItemId,
                            currentUser);
                    }

                    TempData["Success"] = "Stock adjustment created successfully.";
                    return RedirectToAction(nameof(Index));
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error creating stock adjustment");
                    TempData["Error"] = "An error occurred while creating the stock adjustment.";
                }
            }

            ViewData["ItemId"] = new SelectList(_context.InventoryItems.Where(i => !i.IsDeleted), "Id", "Name", stockAdjustment.ItemId);
            ViewData["WarehouseId"] = new SelectList(_context.Warehouses.Where(w => w.IsActive), "Id", "Name", stockAdjustment.WarehouseId);
            return View(stockAdjustment);
        }

        // GET: StockAdjustments/Delete/5
        [Authorize(Permissions.Inventory.Delete)]
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var stockAdjustment = await _context.StockAdjustments
                .Include(s => s.InventoryItem)
                .Include(s => s.Warehouse)
                .FirstOrDefaultAsync(m => m.Id == id);
            if (stockAdjustment == null)
            {
                return NotFound();
            }

            return View(stockAdjustment);
        }

        // POST: StockAdjustments/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        [Authorize(Permissions.Inventory.Delete)]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            try
            {
                var stockAdjustment = await _context.StockAdjustments.FindAsync(id);
                if (stockAdjustment != null)
                {
                    _context.StockAdjustments.Remove(stockAdjustment);
                    await _context.SaveChangesAsync();

                    TempData["Success"] = "Stock adjustment deleted successfully.";
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting stock adjustment");
                TempData["Error"] = "An error occurred while deleting the stock adjustment.";
            }

            return RedirectToAction(nameof(Index));
        }

        // Helper method to generate adjustment number
        private async Task<string> GenerateAdjustmentNumber()
        {
            var today = DateTime.Today;
            var prefix = $"ADJ{today:yyyyMMdd}";

            var lastAdjustment = await _context.StockAdjustments
                .Where(a => a.AdjustmentNumber.StartsWith(prefix))
                .OrderByDescending(a => a.AdjustmentNumber)
                .FirstOrDefaultAsync();

            int nextNumber = 1;
            if (lastAdjustment != null)
            {
                var numberPart = lastAdjustment.AdjustmentNumber.Substring(prefix.Length);
                if (int.TryParse(numberPart, out int lastNumber))
                {
                    nextNumber = lastNumber + 1;
                }
            }

            return $"{prefix}{nextNumber:D3}";
        }

        // API method to get current stock for an item in a warehouse
        [HttpGet]
        public async Task<IActionResult> GetCurrentStock(int itemId, int warehouseId)
        {
            try
            {
                var location = await _context.InventoryLocations
                    .FirstOrDefaultAsync(l => l.ItemId == itemId && l.WarehouseId == warehouseId);

                return Json(new {
                    success = true,
                    currentStock = location?.QuantityOnHand ?? 0,
                    binLocation = location?.BinLocation ?? ""
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting current stock");
                return Json(new { success = false, message = "Error retrieving current stock" });
            }
        }
    }
}
