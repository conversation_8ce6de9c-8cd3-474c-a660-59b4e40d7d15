@{
    ViewData["Title"] = "ERP Settings";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="nxl-content">
    <!-- [ page-header ] start -->
    <div class="page-header">
        <div class="page-header-left d-flex align-items-center">
            <div class="page-header-title">
                <h5 class="m-b-10">ERP Settings</h5>
            </div>
            <ul class="breadcrumb">
                <li class="breadcrumb-item"><a href="@Url.Action("Index", "Home")">Home</a></li>
                <li class="breadcrumb-item">ERP</li>
                <li class="breadcrumb-item">Settings</li>
            </ul>
        </div>
    </div>
    <!-- [ page-header ] end -->

    <!-- [ Main Content ] start -->
    <div class="main-content">
        <div class="row">
            <div class="col-lg-12">
                <div class="card stretch stretch-full">
                    <div class="card-body">
                        <div class="text-center py-5">
                            <div class="mb-4">
                                <i class="feather-settings" style="font-size: 4rem; color: #6c757d;"></i>
                            </div>
                            <h4 class="mb-3">ERP Settings</h4>
                            <p class="text-muted mb-4">
                                The ERP Settings module is coming soon! This will allow you to configure all aspects of your ERP system.
                            </p>
                            <div class="alert alert-info">
                                <i class="feather-info me-2"></i>
                                <strong>Coming Soon:</strong> System configuration, workflow settings, approval processes, and integration management.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- [ Main Content ] end -->
</div>
