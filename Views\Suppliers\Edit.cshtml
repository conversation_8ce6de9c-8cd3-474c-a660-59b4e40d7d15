@model ColorOasisSystemWeb.Models.Purchasing.Supplier

@{
    ViewData["Title"] = "Edit Supplier";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="nxl-content">
    <!-- [ page-header ] start -->
    <div class="page-header">
        <div class="page-header-left d-flex align-items-center">
            <div class="page-header-title">
                <h5 class="m-b-10">Edit Supplier</h5>
            </div>
            <ul class="breadcrumb">
                <li class="breadcrumb-item"><a href="@Url.Action("Index", "Home")">Home</a></li>
                <li class="breadcrumb-item">ERP</li>
                <li class="breadcrumb-item">Purchasing</li>
                <li class="breadcrumb-item"><a href="@Url.Action("Index")">Suppliers</a></li>
                <li class="breadcrumb-item">Edit</li>
            </ul>
        </div>
        <div class="page-header-right ms-auto">
            <div class="page-header-right-items">
                <div class="d-flex align-items-center gap-2 page-header-right-items-wrapper">
                    <a href="@Url.Action("Details", new { id = Model.Id })" class="btn btn-outline-info">
                        <i class="feather-eye me-2"></i>
                        <span>View Details</span>
                    </a>
                    <a href="@Url.Action("Index")" class="btn btn-outline-primary">
                        <i class="feather-arrow-left me-2"></i>
                        <span>Back to List</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
    <!-- [ page-header ] end -->

    <!-- [ Main Content ] start -->
    <div class="main-content">
        <form asp-action="Edit" method="post">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <input type="hidden" asp-for="Id" />
            <input type="hidden" asp-for="CreatedDate" />
            <input type="hidden" asp-for="CreatedBy" />
            
            <div class="row">
                <!-- Basic Information -->
                <div class="col-lg-8">
                    <div class="card stretch stretch-full">
                        <div class="card-header">
                            <h5 class="card-title">Basic Information</h5>
                            <div class="card-header-action">
                                <span class="badge bg-soft-info text-info">Code: @Model.Code</span>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label asp-for="Code" class="form-label"></label>
                                        <input asp-for="Code" class="form-control" readonly />
                                        <span asp-validation-for="Code" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label asp-for="Name" class="form-label"></label>
                                        <input asp-for="Name" class="form-control" required />
                                        <span asp-validation-for="Name" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label asp-for="NameEn" class="form-label"></label>
                                        <input asp-for="NameEn" class="form-control" />
                                        <span asp-validation-for="NameEn" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label asp-for="ContactPerson" class="form-label"></label>
                                        <input asp-for="ContactPerson" class="form-control" />
                                        <span asp-validation-for="ContactPerson" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label asp-for="Email" class="form-label"></label>
                                        <input asp-for="Email" class="form-control" type="email" required />
                                        <span asp-validation-for="Email" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label asp-for="Phone" class="form-label"></label>
                                        <input asp-for="Phone" class="form-control" />
                                        <span asp-validation-for="Phone" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label asp-for="Mobile" class="form-label"></label>
                                        <input asp-for="Mobile" class="form-control" />
                                        <span asp-validation-for="Mobile" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label asp-for="Website" class="form-label"></label>
                                        <input asp-for="Website" class="form-control" type="url" />
                                        <span asp-validation-for="Website" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="mb-3">
                                        <label asp-for="Address" class="form-label"></label>
                                        <textarea asp-for="Address" class="form-control" rows="3"></textarea>
                                        <span asp-validation-for="Address" class="text-danger"></span>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>

                <!-- Settings & Configuration -->
                <div class="col-lg-4">
                    <div class="card stretch stretch-full">
                        <div class="card-header">
                            <h5 class="card-title">Settings</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label asp-for="Type" class="form-label"></label>
                                <select asp-for="Type" class="form-select" asp-items="Html.GetEnumSelectList<ColorOasisSystemWeb.Enums.SupplierType>()">
                                    <option value="">Select Type</option>
                                </select>
                                <span asp-validation-for="Type" class="text-danger"></span>
                            </div>
                            <div class="mb-3">
                                <label asp-for="Status" class="form-label"></label>
                                <select asp-for="Status" class="form-select" asp-items="Html.GetEnumSelectList<ColorOasisSystemWeb.Enums.SupplierStatus>()">
                                    <option value="">Select Status</option>
                                </select>
                                <span asp-validation-for="Status" class="text-danger"></span>
                            </div>
                            <div class="mb-3">
                                <label asp-for="Currency" class="form-label"></label>
                                <select asp-for="Currency" class="form-select">
                                    <option value="AED">AED - UAE Dirham</option>
                                    <option value="USD">USD - US Dollar</option>
                                    <option value="EUR">EUR - Euro</option>
                                    <option value="GBP">GBP - British Pound</option>
                                </select>
                                <span asp-validation-for="Currency" class="text-danger"></span>
                            </div>
                            <div class="mb-3">
                                <label asp-for="PaymentTermsDays" class="form-label"></label>
                                <input asp-for="PaymentTermsDays" class="form-control" type="number" min="0" />
                                <span asp-validation-for="PaymentTermsDays" class="text-danger"></span>
                            </div>
                            <div class="mb-3">
                                <label asp-for="LeadTimeDays" class="form-label"></label>
                                <input asp-for="LeadTimeDays" class="form-control" type="number" min="0" />
                                <span asp-validation-for="LeadTimeDays" class="text-danger"></span>
                            </div>
                            <div class="mb-3">
                                <label asp-for="CreditLimit" class="form-label"></label>
                                <input asp-for="CreditLimit" class="form-control" type="number" step="0.01" min="0" />
                                <span asp-validation-for="CreditLimit" class="text-danger"></span>
                            </div>
                            <div class="mb-3">
                                <label asp-for="MinimumOrderAmount" class="form-label"></label>
                                <input asp-for="MinimumOrderAmount" class="form-control" type="number" step="0.01" min="0" />
                                <span asp-validation-for="MinimumOrderAmount" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <!-- Ratings -->
                    <div class="card stretch stretch-full">
                        <div class="card-header">
                            <h5 class="card-title">Performance Ratings</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label asp-for="QualityRating" class="form-label"></label>
                                <select asp-for="QualityRating" class="form-select">
                                    <option value="1">1 - Poor</option>
                                    <option value="2">2 - Fair</option>
                                    <option value="3">3 - Good</option>
                                    <option value="4">4 - Very Good</option>
                                    <option value="5">5 - Excellent</option>
                                </select>
                                <span asp-validation-for="QualityRating" class="text-danger"></span>
                            </div>
                            <div class="mb-3">
                                <label asp-for="DeliveryRating" class="form-label"></label>
                                <select asp-for="DeliveryRating" class="form-select">
                                    <option value="1">1 - Poor</option>
                                    <option value="2">2 - Fair</option>
                                    <option value="3">3 - Good</option>
                                    <option value="4">4 - Very Good</option>
                                    <option value="5">5 - Excellent</option>
                                </select>
                                <span asp-validation-for="DeliveryRating" class="text-danger"></span>
                            </div>
                            <div class="mb-3">
                                <label asp-for="ServiceRating" class="form-label"></label>
                                <select asp-for="ServiceRating" class="form-select">
                                    <option value="1">1 - Poor</option>
                                    <option value="2">2 - Fair</option>
                                    <option value="3">3 - Good</option>
                                    <option value="4">4 - Very Good</option>
                                    <option value="5">5 - Excellent</option>
                                </select>
                                <span asp-validation-for="ServiceRating" class="text-danger"></span>
                            </div>
                            <div class="mb-3">
                                <label asp-for="PerformanceScore" class="form-label"></label>
                                <input asp-for="PerformanceScore" class="form-control" type="number" step="0.1" min="0" max="100" />
                                <span asp-validation-for="PerformanceScore" class="text-danger"></span>
                            </div>
                            <div class="form-check">
                                <input asp-for="IsActive" class="form-check-input" type="checkbox" />
                                <label asp-for="IsActive" class="form-check-label"></label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Notes -->
            <div class="row">
                <div class="col-12">
                    <div class="card stretch stretch-full">
                        <div class="card-header">
                            <h5 class="card-title">Additional Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label asp-for="Notes" class="form-label"></label>
                                <textarea asp-for="Notes" class="form-control" rows="4" placeholder="Any additional notes about this supplier..."></textarea>
                                <span asp-validation-for="Notes" class="text-danger"></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="row">
                <div class="col-12">
                    <div class="card stretch stretch-full">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <small class="text-muted">
                                        Created: @Model.CreatedDate.ToString("MMM dd, yyyy") by @Model.CreatedBy
                                    </small>
                                </div>
                                <div class="d-flex gap-2">
                                    <a href="@Url.Action("Index")" class="btn btn-outline-secondary">
                                        <i class="feather-x me-2"></i>Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="feather-save me-2"></i>Update Supplier
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <!-- [ Main Content ] end -->
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
