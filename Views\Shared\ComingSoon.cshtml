@{
    ViewData["Title"] = ViewBag.Title ?? "Coming Soon";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">@ViewBag.Title</h4>
                </div>
                <div class="card-body">
                    <div class="text-center py-5">
                        <div class="mb-4">
                            <i data-feather="clock" class="feather-icon" style="width: 64px; height: 64px; color: #7367f0;"></i>
                        </div>
                        <h3 class="mb-3">@ViewBag.Title</h3>
                        <p class="text-muted mb-4">@ViewBag.Message</p>
                        <div class="alert alert-info" role="alert">
                            <i data-feather="info" class="feather-icon me-2"></i>
                            This module is currently under development and will be available soon.
                        </div>
                        <a href="@Url.Action("Index", "Home")" class="btn btn-primary">
                            <i data-feather="home" class="feather-icon me-2"></i>
                            Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Initialize Feather icons
        if (typeof feather !== 'undefined') {
            feather.replace();
        }
    </script>
}
