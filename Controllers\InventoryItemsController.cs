using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using ColorOasisSystemWeb.Data;
using ColorOasisSystemWeb.Models.Inventory;
using ColorOasisSystemWeb.Authorization;
using Microsoft.AspNetCore.Identity;
using ColorOasisSystemWeb.Models;
using ColorOasisSystemWeb.AdvancedNotification.Services;
using ColorOasisSystemWeb.Enums;
using Microsoft.AspNetCore.Mvc.Rendering;

namespace ColorOasisSystemWeb.Controllers
{
    [Authorize]
    public class InventoryItemsController : Controller
    {
        private readonly AppDbContext _context;
        private readonly UserManager<User> _userManager;
        private readonly NotificationService _notificationService;
        private readonly ILogger<InventoryItemsController> _logger;

        public InventoryItemsController(
            AppDbContext context,
            UserManager<User> userManager,
            NotificationService notificationService,
            ILogger<InventoryItemsController> logger)
        {
            _context = context;
            _userManager = userManager;
            _notificationService = notificationService;
            _logger = logger;
        }

        // GET: InventoryItems
        [Authorize(Permissions.Inventory.View)]
        public async Task<IActionResult> Index()
        {
            try
            {
                var inventoryItems = await _context.InventoryItems
                    .Include(i => i.ServiceCategory)
                    .Include(i => i.ServiceType)
                    .Include(i => i.Locations)
                    .ThenInclude(il => il.Warehouse)
                    .ToListAsync();

                return View(inventoryItems);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving inventory items");
                TempData["Error"] = "An error occurred while retrieving inventory items.";
                return View(new List<InventoryItem>());
            }
        }

        // GET: InventoryItems/Details/5
        [Authorize(Permissions.Inventory.View)]
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var inventoryItem = await _context.InventoryItems
                .Include(i => i.ServiceCategory)
                .Include(i => i.ServiceType)
                .Include(i => i.Locations)
                .ThenInclude(il => il.Warehouse)
                .Include(i => i.Transactions)
                .FirstOrDefaultAsync(m => m.Id == id);

            if (inventoryItem == null)
            {
                return NotFound();
            }

            return View(inventoryItem);
        }

        // GET: InventoryItems/Create
        [Authorize(Permissions.Inventory.Create)]
        public IActionResult Create()
        {
            ViewData["ServiceCategoryId"] = new SelectList(_context.ServiceCategories, "Id", "Name");
            ViewData["ServiceTypeId"] = new SelectList(_context.ServiceTypes, "Id", "Name");
            return View();
        }

        // POST: InventoryItems/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        [Authorize(Permissions.Inventory.Create)]
        public async Task<IActionResult> Create([Bind("Code,Name,NameEn,Photo,MinimumPrice,MaximumPrice,Price,Barcode,Discount,ServiceTypeId,ServiceCategoryId,Type,SKU,ReorderPoint,MaxStockLevel,PreferredSupplier,IsSerialTracked,IsLotTracked,UnitOfMeasure,LeadTimeDays,SafetyStock,ABCClass,LastCost,AverageCost,StandardCost")] InventoryItem inventoryItem)
        {
            if (ModelState.IsValid)
            {
                try
                {
                    // Generate SKU if not provided
                    if (string.IsNullOrEmpty(inventoryItem.SKU))
                    {
                        inventoryItem.SKU = await GenerateSKU(inventoryItem.ServiceCategoryId);
                    }

                    _context.Add(inventoryItem);
                    await _context.SaveChangesAsync();

                    // Send notification
                    var currentUser = await _userManager.GetUserAsync(User);
                    if (currentUser != null)
                    {
                        await _notificationService.CreateAndSendNotification(
                            ModelType.InventoryItem,
                            ProcessType.Add,
                            inventoryItem.Id,
                            currentUser);
                    }

                    TempData["Success"] = "Inventory item created successfully.";
                    return RedirectToAction(nameof(Index));
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error creating inventory item");
                    TempData["Error"] = "An error occurred while creating the inventory item.";
                }
            }

            ViewData["ServiceCategoryId"] = new SelectList(_context.ServiceCategories, "Id", "Name", inventoryItem.ServiceCategoryId);
            ViewData["ServiceTypeId"] = new SelectList(_context.ServiceTypes, "Id", "Name", inventoryItem.ServiceTypeId);
            return View(inventoryItem);
        }

        // GET: InventoryItems/Edit/5
        [Authorize(Permissions.Inventory.Edit)]
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var inventoryItem = await _context.InventoryItems.FindAsync(id);
            if (inventoryItem == null)
            {
                return NotFound();
            }

            ViewData["ServiceCategoryId"] = new SelectList(_context.ServiceCategories, "Id", "Name", inventoryItem.ServiceCategoryId);
            ViewData["ServiceTypeId"] = new SelectList(_context.ServiceTypes, "Id", "Name", inventoryItem.ServiceTypeId);
            return View(inventoryItem);
        }

        // POST: InventoryItems/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        [Authorize(Permissions.Inventory.Edit)]
        public async Task<IActionResult> Edit(int id, [Bind("Id,Code,Name,NameEn,Photo,MinimumPrice,MaximumPrice,Price,Barcode,Discount,ServiceTypeId,ServiceCategoryId,Type,SKU,ReorderPoint,MaxStockLevel,PreferredSupplier,IsSerialTracked,IsLotTracked,UnitOfMeasure,LeadTimeDays,SafetyStock,ABCClass,LastCost,AverageCost,StandardCost,IsDeleted")] InventoryItem inventoryItem)
        {
            if (id != inventoryItem.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    _context.Update(inventoryItem);
                    await _context.SaveChangesAsync();

                    // Send notification
                    var currentUser = await _userManager.GetUserAsync(User);
                    if (currentUser != null)
                    {
                        await _notificationService.CreateAndSendNotification(
                            ModelType.InventoryItem,
                            ProcessType.Edit,
                            inventoryItem.Id,
                            currentUser);
                    }

                    TempData["Success"] = "Inventory item updated successfully.";
                    return RedirectToAction(nameof(Index));
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!InventoryItemExists(inventoryItem.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error updating inventory item");
                    TempData["Error"] = "An error occurred while updating the inventory item.";
                }
            }

            ViewData["ServiceCategoryId"] = new SelectList(_context.ServiceCategories, "Id", "Name", inventoryItem.ServiceCategoryId);
            ViewData["ServiceTypeId"] = new SelectList(_context.ServiceTypes, "Id", "Name", inventoryItem.ServiceTypeId);
            return View(inventoryItem);
        }

        // GET: InventoryItems/Delete/5
        [Authorize(Permissions.Inventory.Delete)]
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var inventoryItem = await _context.InventoryItems
                .Include(i => i.ServiceCategory)
                .Include(i => i.ServiceType)
                .FirstOrDefaultAsync(m => m.Id == id);
            if (inventoryItem == null)
            {
                return NotFound();
            }

            return View(inventoryItem);
        }

        // POST: InventoryItems/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        [Authorize(Permissions.Inventory.Delete)]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            try
            {
                var inventoryItem = await _context.InventoryItems.FindAsync(id);
                if (inventoryItem != null)
                {
                    _context.InventoryItems.Remove(inventoryItem);
                    await _context.SaveChangesAsync();

                    // Send notification
                    var currentUser = await _userManager.GetUserAsync(User);
                    if (currentUser != null)
                    {
                        await _notificationService.CreateAndSendNotification(
                            ModelType.InventoryItem,
                            ProcessType.Delete,
                            id,
                            currentUser);
                    }

                    TempData["Success"] = "Inventory item deleted successfully.";
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting inventory item");
                TempData["Error"] = "An error occurred while deleting the inventory item.";
            }

            return RedirectToAction(nameof(Index));
        }

        // Helper method to generate SKU
        private async Task<string> GenerateSKU(int? categoryId)
        {
            var category = await _context.ServiceCategories.FindAsync(categoryId);
            var prefix = category?.Name?.Substring(0, Math.Min(3, category.Name.Length)).ToUpper() ?? "ITM";

            var lastItem = await _context.InventoryItems
                .Where(i => i.SKU.StartsWith(prefix))
                .OrderByDescending(i => i.SKU)
                .FirstOrDefaultAsync();

            int nextNumber = 1;
            if (lastItem != null && lastItem.SKU.Length > prefix.Length)
            {
                var numberPart = lastItem.SKU.Substring(prefix.Length);
                if (int.TryParse(numberPart, out int lastNumber))
                {
                    nextNumber = lastNumber + 1;
                }
            }

            return $"{prefix}{nextNumber:D4}";
        }

        private bool InventoryItemExists(int id)
        {
            return _context.InventoryItems.Any(e => e.Id == id);
        }
    }
}
