using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using ColorOasisSystemWeb.Authorization;

namespace ColorOasisSystemWeb.Controllers
{
    [Authorize]
    public class GoodsReceiptsController : Controller
    {
        [Authorize(Permissions.Purchasing.Receive)]
        public IActionResult Index()
        {
            ViewBag.Title = "Goods Receipts";
            ViewBag.Message = "Manage goods receipts, delivery confirmations, and quality checks.";
            return View("~/Views/Shared/ComingSoon.cshtml");
        }
    }
}
