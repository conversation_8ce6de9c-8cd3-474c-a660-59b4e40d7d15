<nav class="nxl-navigation">
    @using Microsoft.AspNetCore.Identity
    @using ColorOasisSystemWeb.Authorization
    @inject UserManager<ColorOasisSystemWeb.Models.User> UserManager
    @{
        var isAdmin = User.IsInRole("Admin");
        var isUser = User.IsInRole("User");
        var isManager = User.IsInRole("Manager");
        
        // Helper function to check if user has access to any action in a module
        Func<string[], bool> HasAnyAccess = (permissions) => {
            if (isAdmin) return true;
            foreach (var permission in permissions) {
                if (User.HasClaim("Permission", permission)) return true;
            }
            return false;
        };
        
        // Define permission groups for each module to check visibility
        var servicePermissions = new[] { 
            Permissions.Services_View, Permissions.Services_Create, Permissions.Services_Edit, Permissions.Services_Delete,
            Permissions.ServiceCategories_View, Permissions.ServiceCategories_Create, Permissions.ServiceCategories_Edit, Permissions.ServiceCategories_Delete,
            Permissions.ServiceTypes_View, Permissions.ServiceTypes_Create, Permissions.ServiceTypes_Edit, Permissions.ServiceTypes_Delete
        };
        
        var clientPermissions = new[] {
            Permissions.Clients_View, Permissions.Clients_Create, Permissions.Clients_Edit, Permissions.Clients_Delete
        };
        
        var companyPermissions = new[] {
            Permissions.Companies_View, Permissions.Companies_Create, Permissions.Companies_Edit, Permissions.Companies_Delete
        };
        
        var inspectionPermissions = new[] {
            Permissions.Inspections_View, Permissions.Inspections_Create, Permissions.Inspections_Edit, Permissions.Inspections_Delete,
            Permissions.InspectionDetails_View, Permissions.InspectionDetails_Create, Permissions.InspectionDetails_Edit, Permissions.InspectionDetails_Delete
        };
        
        var quotationPermissions = new[] {
            Permissions.Quotations_View, Permissions.Quotations_Create, Permissions.Quotations_Edit, Permissions.Quotations_Delete,
            Permissions.QuotationDetails_View, Permissions.QuotationDetails_Create, Permissions.QuotationDetails_Edit, Permissions.QuotationDetails_Delete
        };
        
        var chatPermissions = new[] {
            Permissions.Chat_View, Permissions.Chat_Send, Permissions.Chat_Manage
        };
        
        var companyInfoPermissions = new[] {
            Permissions.CompanyInfo_View, Permissions.CompanyInfo_Create, Permissions.CompanyInfo_Edit, Permissions.CompanyInfo_Delete
        };
        
        var notificationPermissions = new[] {
            Permissions.Notifications_View, Permissions.Notifications_Create, Permissions.Notifications_Edit, Permissions.Notifications_Delete
        };
        
        var connectionPermissions = new[] {
            Permissions.ConnectionGroups_View, Permissions.ConnectionGroups_Create, Permissions.ConnectionGroups_Edit, Permissions.ConnectionGroups_Delete,
            Permissions.UserConnections_View, Permissions.UserConnections_Create, Permissions.UserConnections_Edit, Permissions.UserConnections_Delete
        };
        
        var userPermissions = new[] {
            Permissions.Users_View, Permissions.Users_Create, Permissions.Users_Edit, Permissions.Users_Delete
        };
        
        var rolePermissions = new[] {
            Permissions.Roles_View, Permissions.Roles_Create, Permissions.Roles_Edit, Permissions.Roles_Delete
        };
        
        var signalRPermissions = new[] {
            Permissions.SignalR_View, Permissions.SignalR_Manage
        };
        
        // Define permission groups for connection management
        var connectionManagementPermissions = new[] {
            Permissions.ConnectionGroups_View, Permissions.ConnectionGroups_Create, Permissions.ConnectionGroups_Edit, Permissions.ConnectionGroups_Delete,
            Permissions.UserConnections_View, Permissions.UserConnections_Create, Permissions.UserConnections_Edit, Permissions.UserConnections_Delete
        };
        
        // Define permission groups for system management
        var systemManagementPermissions = new[] {
            Permissions.SignalR_View, Permissions.SignalR_Manage,
            Permissions.Users_View, Permissions.Users_Create, Permissions.Users_Edit, Permissions.Users_Delete,
            Permissions.Roles_View, Permissions.Roles_Create, Permissions.Roles_Edit, Permissions.Roles_Delete
        };

        // Define permission groups for ERP modules
        var inventoryPermissions = new[] {
            Permissions.Inventory.View, Permissions.Inventory.Create, Permissions.Inventory.Edit, Permissions.Inventory.Delete,
            Permissions.Inventory.StockAdjustment, Permissions.Inventory.Transfer,
            Permissions.Warehouse.View, Permissions.Warehouse.Create, Permissions.Warehouse.Edit, Permissions.Warehouse.Delete, Permissions.Warehouse.Manage
        };

        var purchasingPermissions = new[] {
            Permissions.Purchasing.View, Permissions.Purchasing.Create, Permissions.Purchasing.Edit, Permissions.Purchasing.Delete,
            Permissions.Purchasing.Approve, Permissions.Purchasing.Receive,
            Permissions.Suppliers.View, Permissions.Suppliers.Create, Permissions.Suppliers.Edit, Permissions.Suppliers.Delete, Permissions.Suppliers.Evaluate
        };

        var salesPermissions = new[] {
            Permissions.Sales.View, Permissions.Sales.Create, Permissions.Sales.Edit, Permissions.Sales.Delete,
            Permissions.Sales.Approve, Permissions.Sales.Ship,
            Permissions.Leads.View, Permissions.Leads.Create, Permissions.Leads.Edit, Permissions.Leads.Delete, Permissions.Leads.Convert,
            Permissions.Opportunities.View, Permissions.Opportunities.Create, Permissions.Opportunities.Edit, Permissions.Opportunities.Delete, Permissions.Opportunities.Close
        };

        var accountingPermissions = new[] {
            Permissions.Accounting.View, Permissions.Accounting.Create, Permissions.Accounting.Edit, Permissions.Accounting.Delete, Permissions.Accounting.ProcessPayments,
            Permissions.Invoices.View, Permissions.Invoices.Create, Permissions.Invoices.Edit, Permissions.Invoices.Delete, Permissions.Invoices.Send, Permissions.Invoices.Void,
            Permissions.Payments.View, Permissions.Payments.Create, Permissions.Payments.Edit, Permissions.Payments.Delete, Permissions.Payments.Process, Permissions.Payments.Refund
        };
    }
    <div class="navbar-wrapper">
        <div class="m-header">
            <a asp-area="" asp-controller="Home" asp-action="Index" class="b-brand">
                <!-- ========   change your logo hear   ============ -->
                <img src="~/images/logo-full.png" alt="" class="logo logo-lg">
                <img src="~/images/logo-abbr.png" alt="" class="logo logo-sm">
            </a>
        </div>
        <div class="navbar-content">
            <div class="nxl-search-box">
                <div class="input-group">
                    <span class="input-group-text"><i class="feather-search"></i></span>
                    <input type="text" class="form-control" id="nxl-menu-search" placeholder="@SharedLocalizer["Navigation.Search"]" onkeyup="filterMenuItems()">
                    <span class="input-group-text clear-search" onclick="clearSearch()" style="cursor: pointer; display: none;"><i class="feather-x"></i></span>
                </div>
            </div>
            <ul class="nxl-navbar">
                <li class="nxl-item nxl-caption">
                    <label>@SharedLocalizer["Navigation.Main"]</label>
                </li>
                <li class="nxl-item nxl-hasmenu">
                    <a href="javascript:void(0);" class="nxl-link">
                        <span class="nxl-micon"><i class="feather-airplay"></i></span>
                        <span class="nxl-mtext">@SharedLocalizer["Navigation.Dashboards"]</span><span class="nxl-arrow"><i class="feather-chevron-right"></i></span>
                    </a>
                    <ul class="nxl-submenu">
                        <li class="nxl-item"><a class="nxl-link" asp-area="" asp-controller="Home" asp-action="Index">@SharedLocalizer["Navigation.CRM"]</a></li>
                        <li class="nxl-item"><a class="nxl-link" asp-area="" asp-controller="Home" asp-action="Index">@SharedLocalizer["Navigation.Analytics"]</a></li>
                    </ul>
                </li>
                @if (HasAnyAccess(servicePermissions))
                {
                    <li class="nxl-item nxl-hasmenu">
                        <a href="javascript:void(0);" class="nxl-link">
                            <span class="nxl-micon"><i class="feather-briefcase"></i></span>
                            <span class="nxl-mtext">@SharedLocalizer["Services.Title"]</span><span class="nxl-arrow"><i class="feather-chevron-right"></i></span>
                        </a>
                        <ul class="nxl-submenu">
                            @if (isAdmin || User.HasClaim("Permission", Permissions.Services_Create))
                            {
                                <li class="nxl-item"><a class="nxl-link" asp-area="" asp-controller="Services" asp-action="Create" >@SharedLocalizer["Services.ServiceCreate"]</a></li>
                            }
                            @if (isAdmin || User.HasClaim("Permission", Permissions.Services_View))
                            {
                                <li class="nxl-item"><a class="nxl-link" asp-area="" asp-controller="Services" asp-action="Index" >@SharedLocalizer["Services.Title"]</a></li>
                            }
                            @if (isAdmin || User.HasClaim("Permission", Permissions.ServiceCategories_Create))
                            {
                                <li class="nxl-item"><a class="nxl-link" asp-area="" asp-controller="ServiceCategories" asp-action="Create">@SharedLocalizer["Services.ServiceCategoryCreate"]</a></li>
                            }
                            @if (isAdmin || User.HasClaim("Permission", Permissions.ServiceCategories_View))
                            {
                                <li class="nxl-item"><a class="nxl-link" asp-area="" asp-controller="ServiceCategories" asp-action="Index">@SharedLocalizer["Services.ServiceCategories"]</a></li>
                            }
                            @if (isAdmin || User.HasClaim("Permission", Permissions.ServiceTypes_Create))
                            {
                                <li class="nxl-item"><a class="nxl-link" asp-area="" asp-controller="ServiceTypes" asp-action="Create">@SharedLocalizer["Services.ServiceTypeCreate"]</a></li>
                            }
                            @if (isAdmin || User.HasClaim("Permission", Permissions.ServiceTypes_View))
                            {
                                <li class="nxl-item"><a class="nxl-link" asp-area="" asp-controller="ServiceTypes" asp-action="Index">@SharedLocalizer["Services.ServiceTypes"]</a></li>
                            }
                        </ul>
                    </li>
                }
                
                @if (HasAnyAccess(clientPermissions))
                {
                    <li class="nxl-item nxl-hasmenu">
                        <a href="javascript:void(0);" class="nxl-link">
                            <span class="nxl-micon"><i class="feather-users"></i></span>
                            <span class="nxl-mtext">@SharedLocalizer["Clients.Title"]</span><span class="nxl-arrow"><i class="feather-chevron-right"></i></span>
                        </a>
                        <ul class="nxl-submenu">
                            @if (isAdmin || User.HasClaim("Permission", Permissions.Clients_View))
                            {
                                <li class="nxl-item"><a class="nxl-link" asp-area="" asp-controller="Clients" asp-action="Index">@SharedLocalizer["Clients.Title"]</a></li>
                            }
                            @if (isAdmin || User.HasClaim("Permission", Permissions.Clients_Create))
                            {
                                <li class="nxl-item"><a class="nxl-link" asp-area="" asp-controller="Clients" asp-action="Create">@SharedLocalizer["Clients.ClientCreate"]</a></li>
                            }
                        </ul>
                    </li>
                }
                
                @if (HasAnyAccess(companyPermissions))
                {
                    <li class="nxl-item nxl-hasmenu">
                        <a href="javascript:void(0);" class="nxl-link">
                            <span class="nxl-micon"><i class="feather-users"></i></span>
                            <span class="nxl-mtext">@SharedLocalizer["Company.Companies"]</span><span class="nxl-arrow"><i class="feather-chevron-right"></i></span>
                        </a>
                        <ul class="nxl-submenu">
                            @if (isAdmin || User.HasClaim("Permission", Permissions.Companies_View))
                            {
                                <li class="nxl-item"><a class="nxl-link" asp-area="" asp-controller="Companies" asp-action="Index">@SharedLocalizer["Company.Companies"]</a></li>
                            }
                            @if (isAdmin || User.HasClaim("Permission", Permissions.Companies_Create))
                            {
                                <li class="nxl-item"><a class="nxl-link" asp-area="" asp-controller="Companies" asp-action="Create">@SharedLocalizer["Company.CompanyCreate"]</a></li>
                            }
                        </ul>
                    </li>
                }
                
                @if (HasAnyAccess(inspectionPermissions))
                {
                    <li class="nxl-item nxl-hasmenu">
                        <a href="javascript:void(0);" class="nxl-link">
                            <span class="nxl-micon"><i class="feather-send"></i></span>
                            <span class="nxl-mtext">@SharedLocalizer["Inspection.Title"]</span><span class="nxl-arrow"><i class="feather-chevron-right"></i></span>
                        </a>
                        <ul class="nxl-submenu">
                            @if (isAdmin || User.HasClaim("Permission", Permissions.Inspections_View))
                            {
                                <li class="nxl-item"><a class="nxl-link" asp-area="" asp-controller="Inspection" asp-action="Index">@SharedLocalizer["Inspection.Inspections"]</a></li>
                            }
                            @if (isAdmin || User.HasClaim("Permission", Permissions.Inspections_Create))
                            {
                                <li class="nxl-item"><a class="nxl-link" asp-area="" asp-controller="Inspection" asp-action="Create">@SharedLocalizer["Inspection.InspectionCreate"]</a></li>
                            }
                        </ul>
                    </li>
                }
                
                @if (HasAnyAccess(quotationPermissions))
                {
                    <li class="nxl-item nxl-hasmenu">
                        <a href="javascript:void(0);" class="nxl-link">
                            <span class="nxl-micon"><i class="feather-layout"></i></span>
                            <span class="nxl-mtext">@SharedLocalizer["Quotation.Title"]</span><span class="nxl-arrow"><i class="feather-chevron-right"></i></span>
                        </a>
                        <ul class="nxl-submenu">
                            @if (isAdmin || User.HasClaim("Permission", Permissions.Quotations_View))
                            {
                                <li class="nxl-item"><a class="nxl-link" asp-area="" asp-controller="Quotations" asp-action="Index">@SharedLocalizer["Quotation.Quotations"]</a></li>
                            }
                            @if (isAdmin || User.HasClaim("Permission", Permissions.Quotations_Create))
                            {
                                <li class="nxl-item"><a class="nxl-link" asp-area="" asp-controller="Quotations" asp-action="Create">@SharedLocalizer["Quotation.QuotationCreate"]</a></li>
                            }
 @*                            @if (isAdmin || User.HasClaim("Permission", Permissions.QuotationDetails_View))
                            {
                                <li class="nxl-item"><a class="nxl-link" asp-area="" asp-controller="Quotations" asp-action="Details">@SharedLocalizer["Quotation.QuotationDetails"]</a></li>
                            }
                            @if (isAdmin || User.HasClaim("Permission", Permissions.Quotations_Edit))
                            {
                                <li class="nxl-item"><a class="nxl-link" asp-area="" asp-controller="Quotations" asp-action="Edit">@SharedLocalizer["Quotation.QuotationEdit"]</a></li>
                            } *@
                        </ul>
                    </li>
                }

                @if (HasAnyAccess(chatPermissions))
                {
                    <li class="nxl-item nxl-hasmenu">
                        <a href="javascript:void(0);" class="nxl-link">
                            <span class="nxl-micon"><i class="feather-message-circle"></i></span>
                            <span class="nxl-mtext">@SharedLocalizer["Chat.Title"]</span><span class="nxl-arrow"><i class="feather-chevron-right"></i></span>
                        </a>
                        <ul class="nxl-submenu">
                            @if (isAdmin || User.HasClaim("Permission", Permissions.Chat_View))
                            {
                                <li class="nxl-item"><a class="nxl-link" asp-area="" asp-controller="Chat" asp-action="Index">@SharedLocalizer["Chat.Messages"]</a></li>
                            }
@*                             @if (isAdmin || User.HasClaim("Permission", Permissions.Chat_Send))
                            {
                                <li class="nxl-item"><a class="nxl-link" asp-area="" asp-controller="Chat" asp-action="Create">@SharedLocalizer["Chat.NewMessage"]</a></li>
                            }
                            @if (isAdmin || User.HasClaim("Permission", Permissions.Chat_Manage))
                            {
                                <li class="nxl-item"><a class="nxl-link" asp-area="" asp-controller="Chat" asp-action="Manage">@SharedLocalizer["Chat.ManageChats"]</a></li>
                            } *@
                        </ul>
                    </li>
                }

                <!-- ERP Modules Section -->
                @if (HasAnyAccess(inventoryPermissions) || HasAnyAccess(purchasingPermissions) || HasAnyAccess(salesPermissions) || HasAnyAccess(accountingPermissions))
                {
                    <li class="nxl-item nxl-caption">
                        <label>@SharedLocalizer["ERP.Title"]</label>
                    </li>
                }

                @if (HasAnyAccess(inventoryPermissions))
                {
                    <li class="nxl-item nxl-hasmenu">
                        <a href="javascript:void(0);" class="nxl-link">
                            <span class="nxl-micon"><i class="feather-package"></i></span>
                            <span class="nxl-mtext">@SharedLocalizer["ERP.Inventory.Title"]</span><span class="nxl-arrow"><i class="feather-chevron-right"></i></span>
                        </a>
                        <ul class="nxl-submenu">
                            @if (isAdmin || User.HasClaim("Permission", Permissions.Inventory.View))
                            {
                                <li class="nxl-item"><a class="nxl-link" asp-area="" asp-controller="InventoryItems" asp-action="Index">@SharedLocalizer["ERP.Inventory.Items"]</a></li>
                            }
                            @if (isAdmin || User.HasClaim("Permission", Permissions.Inventory.Create))
                            {
                                <li class="nxl-item"><a class="nxl-link" asp-area="" asp-controller="InventoryItems" asp-action="Create">@SharedLocalizer["ERP.Inventory.AddItem"]</a></li>
                            }
                            @if (isAdmin || User.HasClaim("Permission", Permissions.Warehouse.View))
                            {
                                <li class="nxl-item"><a class="nxl-link" asp-area="" asp-controller="Warehouses" asp-action="Index">@SharedLocalizer["ERP.Inventory.Warehouses"]</a></li>
                            }
                            @if (isAdmin || User.HasClaim("Permission", Permissions.Warehouse.Create))
                            {
                                <li class="nxl-item"><a class="nxl-link" asp-area="" asp-controller="Warehouses" asp-action="Create">@SharedLocalizer["ERP.Inventory.AddWarehouse"]</a></li>
                            }
                            @if (isAdmin || User.HasClaim("Permission", Permissions.Inventory.StockAdjustment))
                            {
                                <li class="nxl-item"><a class="nxl-link" asp-area="" asp-controller="StockAdjustments" asp-action="Index">@SharedLocalizer["ERP.Inventory.StockAdjustments"]</a></li>
                            }
                            @if (isAdmin || User.HasClaim("Permission", Permissions.Inventory.Transfer))
                            {
                                <li class="nxl-item"><a class="nxl-link" asp-area="" asp-controller="InventoryTransactions" asp-action="Index">@SharedLocalizer["ERP.Inventory.Transactions"]</a></li>
                            }
                        </ul>
                    </li>
                }

                @if (HasAnyAccess(purchasingPermissions))
                {
                    <li class="nxl-item nxl-hasmenu">
                        <a href="javascript:void(0);" class="nxl-link">
                            <span class="nxl-micon"><i class="feather-shopping-cart"></i></span>
                            <span class="nxl-mtext">@SharedLocalizer["ERP.Purchasing.Title"]</span><span class="nxl-arrow"><i class="feather-chevron-right"></i></span>
                        </a>
                        <ul class="nxl-submenu">
                            @if (isAdmin || User.HasClaim("Permission", Permissions.Suppliers.View))
                            {
                                <li class="nxl-item"><a class="nxl-link" asp-area="" asp-controller="Suppliers" asp-action="Index">@SharedLocalizer["ERP.Purchasing.Suppliers"]</a></li>
                            }
                            @if (isAdmin || User.HasClaim("Permission", Permissions.Suppliers.Create))
                            {
                                <li class="nxl-item"><a class="nxl-link" asp-area="" asp-controller="Suppliers" asp-action="Create">@SharedLocalizer["ERP.Purchasing.AddSupplier"]</a></li>
                            }
                            @if (isAdmin || User.HasClaim("Permission", Permissions.Purchasing.View))
                            {
                                <li class="nxl-item"><a class="nxl-link" asp-area="" asp-controller="PurchaseRequisitions" asp-action="Index">@SharedLocalizer["ERP.Purchasing.Requisitions"]</a></li>
                            }
                            @if (isAdmin || User.HasClaim("Permission", Permissions.Purchasing.Create))
                            {
                                <li class="nxl-item"><a class="nxl-link" asp-area="" asp-controller="PurchaseRequisitions" asp-action="Create">@SharedLocalizer["ERP.Purchasing.CreateRequisition"]</a></li>
                            }
                            @if (isAdmin || User.HasClaim("Permission", Permissions.Purchasing.View))
                            {
                                <li class="nxl-item"><a class="nxl-link" asp-area="" asp-controller="PurchaseOrders" asp-action="Index">@SharedLocalizer["ERP.Purchasing.PurchaseOrders"]</a></li>
                            }
                            @if (isAdmin || User.HasClaim("Permission", Permissions.Purchasing.Create))
                            {
                                <li class="nxl-item"><a class="nxl-link" asp-area="" asp-controller="PurchaseOrders" asp-action="Create">@SharedLocalizer["ERP.Purchasing.CreatePurchaseOrder"]</a></li>
                            }
                            @if (isAdmin || User.HasClaim("Permission", Permissions.Purchasing.Receive))
                            {
                                <li class="nxl-item"><a class="nxl-link" asp-area="" asp-controller="GoodsReceipts" asp-action="Index">@SharedLocalizer["ERP.Purchasing.GoodsReceipts"]</a></li>
                            }
                        </ul>
                    </li>
                }

                @if (HasAnyAccess(salesPermissions))
                {
                    <li class="nxl-item nxl-hasmenu">
                        <a href="javascript:void(0);" class="nxl-link">
                            <span class="nxl-micon"><i class="feather-trending-up"></i></span>
                            <span class="nxl-mtext">@SharedLocalizer["ERP.Sales.Title"]</span><span class="nxl-arrow"><i class="feather-chevron-right"></i></span>
                        </a>
                        <ul class="nxl-submenu">
                            @if (isAdmin || User.HasClaim("Permission", Permissions.Leads.View))
                            {
                                <li class="nxl-item"><a class="nxl-link" asp-area="" asp-controller="Leads" asp-action="Index">@SharedLocalizer["ERP.Sales.Leads"]</a></li>
                            }
                            @if (isAdmin || User.HasClaim("Permission", Permissions.Leads.Create))
                            {
                                <li class="nxl-item"><a class="nxl-link" asp-area="" asp-controller="Leads" asp-action="Create">@SharedLocalizer["ERP.Sales.AddLead"]</a></li>
                            }
                            @if (isAdmin || User.HasClaim("Permission", Permissions.Opportunities.View))
                            {
                                <li class="nxl-item"><a class="nxl-link" asp-area="" asp-controller="Opportunities" asp-action="Index">@SharedLocalizer["ERP.Sales.Opportunities"]</a></li>
                            }
                            @if (isAdmin || User.HasClaim("Permission", Permissions.Opportunities.Create))
                            {
                                <li class="nxl-item"><a class="nxl-link" asp-area="" asp-controller="Opportunities" asp-action="Create">@SharedLocalizer["ERP.Sales.AddOpportunity"]</a></li>
                            }
                            @if (isAdmin || User.HasClaim("Permission", Permissions.Sales.View))
                            {
                                <li class="nxl-item"><a class="nxl-link" asp-area="" asp-controller="SalesOrders" asp-action="Index">@SharedLocalizer["ERP.Sales.SalesOrders"]</a></li>
                            }
                            @if (isAdmin || User.HasClaim("Permission", Permissions.Sales.Create))
                            {
                                <li class="nxl-item"><a class="nxl-link" asp-area="" asp-controller="SalesOrders" asp-action="Create">@SharedLocalizer["ERP.Sales.CreateSalesOrder"]</a></li>
                            }
                            @if (isAdmin || User.HasClaim("Permission", Permissions.Sales.Ship))
                            {
                                <li class="nxl-item"><a class="nxl-link" asp-area="" asp-controller="Shipments" asp-action="Index">@SharedLocalizer["ERP.Sales.Shipments"]</a></li>
                            }
                        </ul>
                    </li>
                }

                @if (HasAnyAccess(accountingPermissions))
                {
                    <li class="nxl-item nxl-hasmenu">
                        <a href="javascript:void(0);" class="nxl-link">
                            <span class="nxl-micon"><i class="feather-dollar-sign"></i></span>
                            <span class="nxl-mtext">@SharedLocalizer["ERP.Accounting.Title"]</span><span class="nxl-arrow"><i class="feather-chevron-right"></i></span>
                        </a>
                        <ul class="nxl-submenu">
                            @if (isAdmin || User.HasClaim("Permission", Permissions.Invoices.View))
                            {
                                <li class="nxl-item"><a class="nxl-link" asp-area="" asp-controller="Invoices" asp-action="Index">@SharedLocalizer["ERP.Accounting.Invoices"]</a></li>
                            }
                            @if (isAdmin || User.HasClaim("Permission", Permissions.Invoices.Create))
                            {
                                <li class="nxl-item"><a class="nxl-link" asp-area="" asp-controller="Invoices" asp-action="Create">@SharedLocalizer["ERP.Accounting.CreateInvoice"]</a></li>
                            }
                            @if (isAdmin || User.HasClaim("Permission", Permissions.Payments.View))
                            {
                                <li class="nxl-item"><a class="nxl-link" asp-area="" asp-controller="Payments" asp-action="Index">@SharedLocalizer["ERP.Accounting.Payments"]</a></li>
                            }
                            @if (isAdmin || User.HasClaim("Permission", Permissions.Payments.Create))
                            {
                                <li class="nxl-item"><a class="nxl-link" asp-area="" asp-controller="Payments" asp-action="Create">@SharedLocalizer["ERP.Accounting.RecordPayment"]</a></li>
                            }
                            @if (isAdmin || User.HasClaim("Permission", Permissions.Accounting.View))
                            {
                                <li class="nxl-item"><a class="nxl-link" asp-area="" asp-controller="Accounting" asp-action="Dashboard">@SharedLocalizer["ERP.Accounting.Dashboard"]</a></li>
                            }
                        </ul>
                    </li>
                }

                <li class="nxl-item nxl-hasmenu">
                    <a href="javascript:void(0);" class="nxl-link">
                        <span class="nxl-micon"><i class="feather-user"></i></span>
                        <span class="nxl-mtext">@SharedLocalizer["Profile.Title"]</span><span class="nxl-arrow"><i class="feather-chevron-right"></i></span>
                    </a>
                    <ul class="nxl-submenu">
                        <li class="nxl-item"><a class="nxl-link" asp-area="" asp-controller="Profile" asp-action="Index">@SharedLocalizer["Profile.MyProfile"]</a></li>
                        <li class="nxl-item"><a class="nxl-link" asp-area="" asp-controller="Profile" asp-action="Edit">@SharedLocalizer["Profile.EditProfile"]</a></li>
                        <li class="nxl-item"><a class="nxl-link" asp-area="" asp-controller="Profile" asp-action="PrivateDetails">@SharedLocalizer["Profile.PrivateDetails"]</a></li>
                    </ul>
                </li>

                @if (HasAnyAccess(companyInfoPermissions))
                {
                    <li class="nxl-item nxl-hasmenu">
                        <a href="javascript:void(0);" class="nxl-link">
                            <span class="nxl-micon"><i class="feather-info"></i></span>
                            <span class="nxl-mtext">@SharedLocalizer["CompanyInfo.Title"]</span><span class="nxl-arrow"><i class="feather-chevron-right"></i></span>
                        </a>
                        <ul class="nxl-submenu">
                            @if (isAdmin || User.HasClaim("Permission", Permissions.CompanyInfo_View))
                            {
                                <li class="nxl-item"><a class="nxl-link" asp-area="" asp-controller="CompanyInfo" asp-action="Index">@SharedLocalizer["CompanyInfo.ViewInfo"]</a></li>
                            }
                            @if (isAdmin || User.HasClaim("Permission", Permissions.CompanyInfo_Edit))
                            {
                                <li class="nxl-item"><a class="nxl-link" asp-area="" asp-controller="CompanyInfo" asp-action="Edit">@SharedLocalizer["CompanyInfo.EditInfo"]</a></li>
                            }
                        </ul>
                    </li>
                }
 faster                 
                @if (HasAnyAccess(systemManagementPermissions))
                {
                    <li class="nxl-item nxl-caption">
                        <label>@SharedLocalizer["Navigation.AdminSection"]</label>
                    </li>
                    <li class="nxl-item nxl-hasmenu">
                        <a href="javascript:void(0);" class="nxl-link">
                            <span class="nxl-micon"><i class="feather-shield"></i></span>
                            <span class="nxl-mtext">@SharedLocalizer["Admin.UserAdministration"]</span><span class="nxl-arrow"><i class="feather-chevron-right"></i></span>
                        </a>
                        <ul class="nxl-submenu">
                            @if (isAdmin || User.HasClaim("Permission", Permissions.Users_View))
                            {
                                <li class="nxl-item"><a class="nxl-link" asp-area="Admin" asp-controller="UserManagement" asp-action="Index">@SharedLocalizer["Admin.UserManagement"]</a></li>
                            }
                            @if (isAdmin || User.HasClaim("Permission", Permissions.Roles_View))
                            {
                                <li class="nxl-item"><a class="nxl-link" asp-area="Admin" asp-controller="RoleManagement" asp-action="Index">@SharedLocalizer["Admin.RoleManagement"]</a></li>
                            }
                            @if (isAdmin)
                            {
                                <li class="nxl-item"><a class="nxl-link" asp-area="Admin" asp-controller="PermissionsManagement" asp-action="Index">@SharedLocalizer["Admin.PermissionsManagement"]</a></li>
                                <li class="nxl-item"><a class="nxl-link" asp-area="Admin" asp-controller="ClaimsManagement" asp-action="Index">@SharedLocalizer["Admin.ClaimsManagement"]</a></li>
                                <li class="nxl-item"><a class="nxl-link" asp-area="Admin" asp-controller="ProfileManagement" asp-action="Index">@SharedLocalizer["Admin.ProfileManagement"]</a></li>
                            }
                        </ul>
                    </li>
                    @if (isAdmin || User.HasClaim("Permission", Permissions.Notifications_View))
                    {
                        <li class="nxl-item nxl-hasmenu">
                            <a href="javascript:void(0);" class="nxl-link">
                                <span class="nxl-micon"><i class="feather-bell"></i></span>
                                <span class="nxl-mtext">@SharedLocalizer["Admin.NotificationManagement"]</span><span class="nxl-arrow"><i class="feather-chevron-right"></i></span>
                            </a>
                            <ul class="nxl-submenu">                                <li class="nxl-item"><a class="nxl-link" asp-area="Admin" asp-controller="Notification" asp-action="Dashboard">@SharedLocalizer["Admin.Notifications"]</a></li>
                            </ul>
                        </li>
                    }
                    <li class="nxl-item nxl-hasmenu">
                        <a href="javascript:void(0);" class="nxl-link">
                            <span class="nxl-micon"><i class="feather-activity"></i></span>
                            <span class="nxl-mtext">@SharedLocalizer["Admin.SystemManagement"]</span><span class="nxl-arrow"><i class="feather-chevron-right"></i></span>
                        </a>
                        <ul class="nxl-submenu">
                            <li class="nxl-item"><a class="nxl-link" asp-area="Admin" asp-controller="Dashboard" asp-action="Index">@SharedLocalizer["Admin.Dashboard"]</a></li>
                            @if (isAdmin || User.HasClaim("Permission", Permissions.SignalR_View))
                            {
                                <li class="nxl-item"><a class="nxl-link" asp-area="Admin" asp-controller="SignalRManagement" asp-action="Index">@SharedLocalizer["Admin.SignalRManagement"]</a></li>
                            }
                        </ul>
                    </li>
                }
                
                @if (HasAnyAccess(notificationPermissions))
                {
                    <li class="nxl-item nxl-hasmenu">
                        <a href="javascript:void(0);" class="nxl-link">
                            <span class="nxl-micon"><i class="feather-bell"></i></span>
                            <span class="nxl-mtext">@SharedLocalizer["Notification.Title"]</span><span class="nxl-arrow"><i class="feather-chevron-right"></i></span>
                        </a>
                        <ul class="nxl-submenu">
                            @if (isAdmin || User.HasClaim("Permission", Permissions.Notifications_View))
                            {
                                <li class="nxl-item"><a class="nxl-link" asp-area="" asp-controller="Home" asp-action="Notifications">@SharedLocalizer["Notification.MyNotifications"]</a></li>
                            }
                            @if (isAdmin || User.HasClaim("Permission", Permissions.Notifications_Create))
                            {
                                <li class="nxl-item"><a class="nxl-link" asp-area="" asp-controller="Home" asp-action="CreateNotification">@SharedLocalizer["Notification.SendNotification"]</a></li>
                            }
                        </ul>
                    </li>
                }
                
                @if (HasAnyAccess(connectionManagementPermissions))
                {
                    <li class="nxl-item nxl-hasmenu">
                        <a href="javascript:void(0);" class="nxl-link">
                            <span class="nxl-micon"><i class="feather-link"></i></span>
                            <span class="nxl-mtext">@SharedLocalizer["Connection.Title"]</span><span class="nxl-arrow"><i class="feather-chevron-right"></i></span>
                        </a>
                        <ul class="nxl-submenu">
                            @if (isAdmin || User.HasClaim("Permission", Permissions.ConnectionGroups_View))
                            {
                                <li class="nxl-item"><a class="nxl-link" asp-area="" asp-controller="ConnectionGroups" asp-action="Index">@SharedLocalizer["Connection.Groups"]</a></li>
                            }
                            @if (isAdmin || User.HasClaim("Permission", Permissions.ConnectionGroups_Create))
                            {
                                <li class="nxl-item"><a class="nxl-link" asp-area="" asp-controller="ConnectionGroups" asp-action="Create">@SharedLocalizer["Connection.CreateGroup"]</a></li>
                            }
                            @if (isAdmin || User.HasClaim("Permission", Permissions.UserConnections_View))
                            {
                                <li class="nxl-item"><a class="nxl-link" asp-area="" asp-controller="UserConnections" asp-action="Index">@SharedLocalizer["Connection.UserConnections"]</a></li>
                            }
                            @if (isAdmin || User.HasClaim("Permission", Permissions.UserConnections_Create))
                            {
                                <li class="nxl-item"><a class="nxl-link" asp-area="" asp-controller="UserConnections" asp-action="Create">@SharedLocalizer["Connection.CreateConnection"]</a></li>
                            }
                        </ul>
                    </li>
                }
                
                @if (isAdmin || User.HasClaim("Permission", Permissions.Users_View))
                {
                    <li class="nxl-item nxl-hasmenu">
                        <a href="javascript:void(0);" class="nxl-link">
                            <span class="nxl-micon"><i class="feather-log-out"></i></span>
                            <span class="nxl-mtext">@SharedLocalizer["UserSessions.Title"]</span><span class="nxl-arrow"><i class="feather-chevron-right"></i></span>
                        </a>
                        <ul class="nxl-submenu">
                            <li class="nxl-item"><a class="nxl-link" asp-area="" asp-controller="GettingUserOut" asp-action="Index">@SharedLocalizer["UserSessions.ActiveSessions"]</a></li>
                            @if (isAdmin)
                            {
                                <li class="nxl-item"><a class="nxl-link" asp-area="" asp-controller="GettingUserOut" asp-action="ManageSessions">@SharedLocalizer["UserSessions.ManageSessions"]</a></li>
                            }
                        </ul>
                    </li>
                }
                <!-- End of main navigation items -->
            </ul>
            <div class="card text-center">
                <div class="card-body">
                    <i class="feather-alert-circle fs-4 text-dark"></i>
                    <h6 class="mt-4 text-dark fw-bolder">@SharedLocalizer["Navigation.InfoCenter"]</h6>
                    <p class="fs-11 my-3 text-dark">@SharedLocalizer["Company.CompanyProduct"]</p>
                    <a href="mailto:<EMAIL>" class="btn btn-primary text-dark w-100">@SharedLocalizer["Company.MailUs"]</a>
                </div>
            </div>
        </div>
    </div>
</nav>

<script>
    // Function to filter menu items based on search text
    function filterMenuItems() {
        const searchText = document.getElementById('nxl-menu-search').value.toLowerCase();
        const menuItems = document.querySelectorAll('.nxl-navbar .nxl-item:not(.nxl-caption)');
        const clearButton = document.querySelector('.clear-search');
        
        // Show/hide clear button based on search text
        if (searchText.length > 0) {
            clearButton.style.display = 'block';
        } else {
            clearButton.style.display = 'none';
        }
        
        // Reset all menu items and sections before filtering
        resetMenuItems();
        
        if (searchText.length === 0) return;
        
        let hasResults = false;
        
        // Filter menu items based on search text
        menuItems.forEach(item => {
            const menuText = item.textContent.toLowerCase();
            const isSubmenu = item.classList.contains('nxl-hasmenu');
            const hasMatch = menuText.includes(searchText);
            
            if (hasMatch) {
                hasResults = true;
                
                // For dropdown menus, expand them to show matches
                if (isSubmenu) {
                    item.classList.add('nxl-trigger');
                    const submenu = item.querySelector('.nxl-submenu');
                    if (submenu) {
                        submenu.style.display = 'block';
                    }
                    
                    // Highlight matching text in submenu items
                    highlightMatches(item, searchText);
                } else {
                    // Highlight matching text in regular menu items
                    highlightMatches(item, searchText);
                }
            } else {
                // Hide non-matching items
                item.style.display = 'none';
            }
        });
        
        // Hide section labels if no items in that section are visible
        document.querySelectorAll('.nxl-caption').forEach(caption => {
            let nextSibling = caption.nextElementSibling;
            let hasVisibleItems = false;
            
            while (nextSibling && !nextSibling.classList.contains('nxl-caption')) {
                if (nextSibling.style.display !== 'none') {
                    hasVisibleItems = true;
                    break;
                }
                nextSibling = nextSibling.nextElementSibling;
            }
            
            caption.style.display = hasVisibleItems ? '' : 'none';
        });
    }
    
    // Function to highlight matching text
    function highlightMatches(item, searchText) {
        const links = item.querySelectorAll('.nxl-link');
        
        links.forEach(link => {
            const originalText = link.innerHTML;
            const lowerText = link.textContent.toLowerCase();
            const index = lowerText.indexOf(searchText);
            
            if (index >= 0) {
                // Only highlight text in the span with class nxl-mtext
                const mtext = link.querySelector('.nxl-mtext');
                if (mtext) {
                    const mtextContent = mtext.textContent;
                    const mtextLower = mtextContent.toLowerCase();
                    const mtextIndex = mtextLower.indexOf(searchText);
                    
                    if (mtextIndex >= 0) {
                        const before = mtextContent.substring(0, mtextIndex);
                        const match = mtextContent.substring(mtextIndex, mtextIndex + searchText.length);
                        const after = mtextContent.substring(mtextIndex + searchText.length);
                        
                        mtext.innerHTML = before + '<span class="search-highlight">' + match + '</span>' + after;
                    }
                }
            }
        });
    }
    
    // Function to reset menu items to original state
    function resetMenuItems() {
        // Reset all menu items visibility
        document.querySelectorAll('.nxl-navbar .nxl-item').forEach(item => {
            item.style.display = '';
        });
        
        // Reset all section captions visibility
        document.querySelectorAll('.nxl-caption').forEach(caption => {
            caption.style.display = '';
        });
        
        // Reset all highlighted text
        document.querySelectorAll('.search-highlight').forEach(highlight => {
            const parent = highlight.parentNode;
            parent.textContent = parent.textContent;
        });
        
        // Reset expanded menus if not using the default menu click behavior
        if (!document.querySelector('#nxl-menu-search').value) {
            document.querySelectorAll('.nxl-navbar .nxl-hasmenu').forEach(menu => {
                if (!menu.classList.contains('active')) {
                    menu.classList.remove('nxl-trigger');
                    const submenu = menu.querySelector('.nxl-submenu');
                    if (submenu) {
                        submenu.style.display = '';
                    }
                }
            });
        }
    }
    
    // Function to clear search
    function clearSearch() {
        document.getElementById('nxl-menu-search').value = '';
        document.querySelector('.clear-search').style.display = 'none';
        resetMenuItems();
    }
</script>

<style>
    /* Styles for the search box */
    .nxl-search-box {
        padding: 15px;
        margin-bottom: 10px;
    }
    
    .nxl-search-box .input-group {
        background-color: rgba(0, 0, 0, 0.05);
        border-radius: 5px;
    }
    
    .nxl-search-box .input-group-text {
        background-color: transparent;
        border: none;
        color: #666;
    }
    
    .nxl-search-box .form-control {
        background-color: transparent;
        border: none;
        box-shadow: none;
        color: #333;
    }
    
    .nxl-search-box .form-control:focus {
        box-shadow: none;
    }
    
    /* Style for highlighted text */
    .search-highlight {
        background-color: rgba(255, 230, 0, 0.4);
        border-radius: 2px;
        padding: 0 2px;
        font-weight: bold;
    }
</style>