using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using ColorOasisSystemWeb.Authorization;

namespace ColorOasisSystemWeb.Controllers
{
    [Authorize]
    public class SuppliersController : Controller
    {
        [Authorize(Permissions.Suppliers.View)]
        public IActionResult Index()
        {
            ViewBag.Title = "Suppliers";
            ViewBag.Message = "Manage supplier information, contacts, and vendor relationships.";
            return View("~/Views/Shared/ComingSoon.cshtml");
        }

        [Authorize(Permissions.Suppliers.Create)]
        public IActionResult Create()
        {
            ViewBag.Title = "Add Supplier";
            ViewBag.Message = "Add new supplier functionality will be implemented here.";
            return View("~/Views/Shared/ComingSoon.cshtml");
        }
    }
}
