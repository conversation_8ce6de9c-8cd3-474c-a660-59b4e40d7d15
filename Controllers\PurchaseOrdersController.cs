using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using ColorOasisSystemWeb.Data;
using ColorOasisSystemWeb.Models.Purchasing;
using ColorOasisSystemWeb.Authorization;
using Microsoft.AspNetCore.Identity;
using ColorOasisSystemWeb.Models;
using ColorOasisSystemWeb.AdvancedNotification.Services;
using ColorOasisSystemWeb.Enums;
using Microsoft.AspNetCore.Mvc.Rendering;

namespace ColorOasisSystemWeb.Controllers
{
    [Authorize]
    public class PurchaseOrdersController : Controller
    {
        private readonly AppDbContext _context;
        private readonly UserManager<User> _userManager;
        private readonly NotificationService _notificationService;
        private readonly ILogger<PurchaseOrdersController> _logger;

        public PurchaseOrdersController(
            AppDbContext context,
            UserManager<User> userManager,
            NotificationService notificationService,
            ILogger<PurchaseOrdersController> logger)
        {
            _context = context;
            _userManager = userManager;
            _notificationService = notificationService;
            _logger = logger;
        }

        // GET: PurchaseOrders
        [Authorize(Permissions.Purchasing.View)]
        public async Task<IActionResult> Index()
        {
            try
            {
                var purchaseOrders = await _context.PurchaseOrders
                    .Include(p => p.Supplier)
                    .Include(p => p.Items)
                    .OrderByDescending(p => p.OrderDate)
                    .ToListAsync();

                return View(purchaseOrders);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving purchase orders");
                TempData["Error"] = "An error occurred while retrieving purchase orders.";
                return View(new List<PurchaseOrder>());
            }
        }

        // GET: PurchaseOrders/Details/5
        [Authorize(Permissions.Purchasing.View)]
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var purchaseOrder = await _context.PurchaseOrders
                .Include(p => p.Supplier)
                .Include(p => p.Items)
                .ThenInclude(i => i.InventoryItem)
                .Include(p => p.Approvals)
                .FirstOrDefaultAsync(m => m.Id == id);

            if (purchaseOrder == null)
            {
                return NotFound();
            }

            return View(purchaseOrder);
        }

        // GET: PurchaseOrders/Create
        [Authorize(Permissions.Purchasing.Create)]
        public IActionResult Create()
        {
            ViewData["SupplierId"] = new SelectList(_context.Suppliers.Where(s => s.IsActive), "Id", "Name");
            return View();
        }

        // POST: PurchaseOrders/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        [Authorize(Permissions.Purchasing.Create)]
        public async Task<IActionResult> Create([Bind("SupplierId,ExpectedDeliveryDate,DeliveryAddress,PaymentTerms,Notes,TaxRate,DiscountAmount,ShippingCost")] PurchaseOrder purchaseOrder)
        {
            if (ModelState.IsValid)
            {
                try
                {
                    // Generate PO number
                    purchaseOrder.PONumber = await GeneratePONumber();
                    purchaseOrder.OrderDate = DateTime.UtcNow;
                    purchaseOrder.Status = POStatus.Draft;

                    var currentUser = await _userManager.GetUserAsync(User);
                    purchaseOrder.CreatedBy = currentUser?.UserName ?? "System";
                    purchaseOrder.CreatedAt = DateTime.UtcNow;
                    purchaseOrder.UpdatedAt = DateTime.UtcNow;

                    _context.Add(purchaseOrder);
                    await _context.SaveChangesAsync();

                    // Send notification
                    if (currentUser != null)
                    {
                        await _notificationService.CreateAndSendNotification(
                            ModelType.PurchaseOrder,
                            ProcessType.Add,
                            purchaseOrder.Id,
                            currentUser);
                    }

                    TempData["Success"] = "Purchase order created successfully.";
                    return RedirectToAction(nameof(Details), new { id = purchaseOrder.Id });
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error creating purchase order");
                    TempData["Error"] = "An error occurred while creating the purchase order.";
                }
            }

            ViewData["SupplierId"] = new SelectList(_context.Suppliers.Where(s => s.IsActive), "Id", "Name", purchaseOrder.SupplierId);
            return View(purchaseOrder);
        }

        // GET: PurchaseOrders/Edit/5
        [Authorize(Permissions.Purchasing.Edit)]
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var purchaseOrder = await _context.PurchaseOrders.FindAsync(id);
            if (purchaseOrder == null)
            {
                return NotFound();
            }

            // Only allow editing of draft orders
            if (purchaseOrder.Status != POStatus.Draft)
            {
                TempData["Error"] = "Only draft purchase orders can be edited.";
                return RedirectToAction(nameof(Details), new { id });
            }

            ViewData["SupplierId"] = new SelectList(_context.Suppliers.Where(s => s.IsActive), "Id", "Name", purchaseOrder.SupplierId);
            return View(purchaseOrder);
        }

        // POST: PurchaseOrders/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        [Authorize(Permissions.Purchasing.Edit)]
        public async Task<IActionResult> Edit(int id, [Bind("Id,PONumber,SupplierId,ExpectedDeliveryDate,DeliveryAddress,PaymentTerms,Notes,TaxRate,DiscountAmount,ShippingCost,Status,OrderDate,CreatedBy,CreatedAt")] PurchaseOrder purchaseOrder)
        {
            if (id != purchaseOrder.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    purchaseOrder.UpdatedAt = DateTime.UtcNow;
                    var currentUser = await _userManager.GetUserAsync(User);
                    purchaseOrder.UpdatedBy = currentUser?.UserName ?? "System";

                    _context.Update(purchaseOrder);
                    await _context.SaveChangesAsync();

                    // Send notification
                    if (currentUser != null)
                    {
                        await _notificationService.CreateAndSendNotification(
                            ModelType.PurchaseOrder,
                            ProcessType.Edit,
                            purchaseOrder.Id,
                            currentUser);
                    }

                    TempData["Success"] = "Purchase order updated successfully.";
                    return RedirectToAction(nameof(Details), new { id });
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!PurchaseOrderExists(purchaseOrder.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error updating purchase order");
                    TempData["Error"] = "An error occurred while updating the purchase order.";
                }
            }

            ViewData["SupplierId"] = new SelectList(_context.Suppliers.Where(s => s.IsActive), "Id", "Name", purchaseOrder.SupplierId);
            return View(purchaseOrder);
        }

        // POST: PurchaseOrders/Submit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        [Authorize(Permissions.Purchasing.Edit)]
        public async Task<IActionResult> Submit(int id)
        {
            try
            {
                var purchaseOrder = await _context.PurchaseOrders
                    .Include(p => p.Items)
                    .FirstOrDefaultAsync(p => p.Id == id);

                if (purchaseOrder == null)
                {
                    return NotFound();
                }

                if (purchaseOrder.Status != POStatus.Draft)
                {
                    TempData["Error"] = "Only draft purchase orders can be submitted.";
                    return RedirectToAction(nameof(Details), new { id });
                }

                if (!purchaseOrder.Items.Any())
                {
                    TempData["Error"] = "Cannot submit purchase order without items.";
                    return RedirectToAction(nameof(Details), new { id });
                }

                purchaseOrder.Status = POStatus.PendingApproval;
                purchaseOrder.SubmittedDate = DateTime.UtcNow;

                var currentUser = await _userManager.GetUserAsync(User);
                purchaseOrder.SubmittedBy = currentUser?.UserName ?? "System";
                purchaseOrder.UpdatedAt = DateTime.UtcNow;
                purchaseOrder.UpdatedBy = currentUser?.UserName ?? "System";

                // Calculate totals
                purchaseOrder.Subtotal = purchaseOrder.Items.Sum(i => i.QuantityOrdered * i.UnitPrice);
                purchaseOrder.TaxAmount = purchaseOrder.Subtotal * (purchaseOrder.TaxRate / 100);
                purchaseOrder.TotalAmount = purchaseOrder.Subtotal + purchaseOrder.TaxAmount + purchaseOrder.ShippingCost - purchaseOrder.DiscountAmount;

                await _context.SaveChangesAsync();

                TempData["Success"] = "Purchase order submitted for approval.";
                return RedirectToAction(nameof(Details), new { id });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error submitting purchase order");
                TempData["Error"] = "An error occurred while submitting the purchase order.";
                return RedirectToAction(nameof(Details), new { id });
            }
        }

        // POST: PurchaseOrders/Cancel/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        [Authorize(Permissions.Purchasing.Edit)]
        public async Task<IActionResult> Cancel(int id)
        {
            try
            {
                var purchaseOrder = await _context.PurchaseOrders.FindAsync(id);

                if (purchaseOrder == null)
                {
                    return NotFound();
                }

                if (purchaseOrder.Status == POStatus.Completed || purchaseOrder.Status == POStatus.Cancelled)
                {
                    TempData["Error"] = "Cannot cancel completed or already cancelled purchase orders.";
                    return RedirectToAction(nameof(Details), new { id });
                }

                purchaseOrder.Status = POStatus.Cancelled;
                purchaseOrder.CancelledDate = DateTime.UtcNow;

                var currentUser = await _userManager.GetUserAsync(User);
                purchaseOrder.CancelledBy = currentUser?.UserName ?? "System";
                purchaseOrder.UpdatedAt = DateTime.UtcNow;
                purchaseOrder.UpdatedBy = currentUser?.UserName ?? "System";

                await _context.SaveChangesAsync();

                TempData["Success"] = "Purchase order cancelled successfully.";
                return RedirectToAction(nameof(Details), new { id });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cancelling purchase order");
                TempData["Error"] = "An error occurred while cancelling the purchase order.";
                return RedirectToAction(nameof(Details), new { id });
            }
        }

        // Helper method to generate PO number
        private async Task<string> GeneratePONumber()
        {
            var today = DateTime.Today;
            var prefix = $"PO{today:yyyyMM}";

            var lastPO = await _context.PurchaseOrders
                .Where(p => p.PONumber.StartsWith(prefix))
                .OrderByDescending(p => p.PONumber)
                .FirstOrDefaultAsync();

            int nextNumber = 1;
            if (lastPO != null)
            {
                var numberPart = lastPO.PONumber.Substring(prefix.Length);
                if (int.TryParse(numberPart, out int lastNumber))
                {
                    nextNumber = lastNumber + 1;
                }
            }

            return $"{prefix}{nextNumber:D4}";
        }

        private bool PurchaseOrderExists(int id)
        {
            return _context.PurchaseOrders.Any(e => e.Id == id);
        }
    }
}
