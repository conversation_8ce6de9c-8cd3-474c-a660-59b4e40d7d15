@model ColorOasisSystemWeb.Models.Purchasing.PurchaseOrder

@{
    ViewData["Title"] = "Purchase Order Details";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="nxl-content">
    <!-- [ page-header ] start -->
    <div class="page-header">
        <div class="page-header-left d-flex align-items-center">
            <div class="page-header-title">
                <h5 class="m-b-10">Purchase Order Details</h5>
            </div>
            <ul class="breadcrumb">
                <li class="breadcrumb-item"><a href="@Url.Action("Index", "Home")">Home</a></li>
                <li class="breadcrumb-item">ERP</li>
                <li class="breadcrumb-item">Purchasing</li>
                <li class="breadcrumb-item"><a href="@Url.Action("Index")">Purchase Orders</a></li>
                <li class="breadcrumb-item">Details</li>
            </ul>
        </div>
        <div class="page-header-right ms-auto">
            <div class="page-header-right-items">
                <div class="d-flex align-items-center gap-2 page-header-right-items-wrapper">
                    @if (Model.Status == ColorOasisSystemWeb.Enums.POStatus.Draft)
                    {
                        <a href="@Url.Action("Edit", new { id = Model.Id })" class="btn btn-primary">
                            <i class="feather-edit-3 me-2"></i>
                            <span>Edit PO</span>
                        </a>
                    }
                    <div class="dropdown">
                        <a href="javascript:void(0);" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="feather-more-horizontal me-2"></i>Actions
                        </a>
                        <div class="dropdown-menu dropdown-menu-end">
                            <a class="dropdown-item" href="#">
                                <i class="feather-printer me-3"></i>Print PO
                            </a>
                            <a class="dropdown-item" href="#">
                                <i class="feather-download me-3"></i>Export PDF
                            </a>
                            <div class="dropdown-divider"></div>
                            @if (Model.Status == ColorOasisSystemWeb.Enums.POStatus.Draft)
                            {
                                <a class="dropdown-item" href="#">
                                    <i class="feather-send me-3"></i>Submit for Approval
                                </a>
                            }
                            @if (Model.Status == ColorOasisSystemWeb.Enums.POStatus.Submitted)
                            {
                                <a class="dropdown-item" href="#">
                                    <i class="feather-check me-3"></i>Approve
                                </a>
                            }
                        </div>
                    </div>
                    <a href="@Url.Action("Index")" class="btn btn-outline-secondary">
                        <i class="feather-arrow-left me-2"></i>
                        <span>Back to List</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
    <!-- [ page-header ] end -->

    <!-- [ Main Content ] start -->
    <div class="main-content">
        <div class="row">
            <!-- PO Header -->
            <div class="col-lg-8">
                <div class="card stretch stretch-full">
                    <div class="card-header">
                        <h5 class="card-title">Purchase Order Information</h5>
                        <div class="card-header-action">
                            @switch (Model.Status)
                            {
                                case ColorOasisSystemWeb.Enums.POStatus.Draft:
                                    <span class="badge bg-soft-secondary text-secondary">Draft</span>
                                    break;
                                case ColorOasisSystemWeb.Enums.POStatus.Submitted:
                                    <span class="badge bg-soft-warning text-warning">Submitted</span>
                                    break;
                                case ColorOasisSystemWeb.Enums.POStatus.Approved:
                                    <span class="badge bg-soft-info text-info">Approved</span>
                                    break;
                                case ColorOasisSystemWeb.Enums.POStatus.Sent:
                                    <span class="badge bg-soft-primary text-primary">Sent</span>
                                    break;
                                case ColorOasisSystemWeb.Enums.POStatus.PartiallyReceived:
                                    <span class="badge bg-soft-warning text-warning">Partially Received</span>
                                    break;
                                case ColorOasisSystemWeb.Enums.POStatus.FullyReceived:
                                    <span class="badge bg-soft-success text-success">Fully Received</span>
                                    break;
                                case ColorOasisSystemWeb.Enums.POStatus.Cancelled:
                                    <span class="badge bg-soft-danger text-danger">Cancelled</span>
                                    break;
                                case ColorOasisSystemWeb.Enums.POStatus.Closed:
                                    <span class="badge bg-soft-dark text-dark">Closed</span>
                                    break;
                            }
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label text-muted">PO Number</label>
                                    <div class="fw-bold">@Model.PONumber</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Order Date</label>
                                    <div>@Model.OrderDate.ToString("MMM dd, yyyy")</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Supplier</label>
                                    <div class="fw-bold">@Model.Supplier?.Name</div>
                                    @if (!string.IsNullOrEmpty(Model.Supplier?.ContactPerson))
                                    {
                                        <div class="text-muted">Contact: @Model.Supplier.ContactPerson</div>
                                    }
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Expected Delivery Date</label>
                                    <div>@Model.ExpectedDeliveryDate.ToString("MMM dd, yyyy")</div>
                                </div>
                            </div>
                            @if (!string.IsNullOrEmpty(Model.PaymentTerms))
                            {
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label text-muted">Payment Terms</label>
                                        <div>@Model.PaymentTerms</div>
                                    </div>
                                </div>
                            }
                            @if (!string.IsNullOrEmpty(Model.DeliveryTerms))
                            {
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label text-muted">Delivery Terms</label>
                                        <div>@Model.DeliveryTerms</div>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                </div>

                <!-- PO Items -->
                <div class="card stretch stretch-full">
                    <div class="card-header">
                        <h5 class="card-title">Purchase Order Items</h5>
                    </div>
                    <div class="card-body">
                        @if (Model.Items?.Any() == true)
                        {
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Item</th>
                                            <th>Description</th>
                                            <th>Qty Ordered</th>
                                            <th>Qty Received</th>
                                            <th>Unit Price</th>
                                            <th>Total</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var item in Model.Items)
                                        {
                                            <tr>
                                                <td>
                                                    <div class="fw-bold">@item.ItemDescription</div>
                                                    <div class="text-muted">@item.ItemCode</div>
                                                </td>
                                                <td>
                                                    @if (!string.IsNullOrEmpty(item.Specifications))
                                                    {
                                                        <small class="text-muted">@item.Specifications</small>
                                                    }
                                                </td>
                                                <td>@item.QuantityOrdered @item.UnitOfMeasure</td>
                                                <td>
                                                    @item.QuantityReceived @item.UnitOfMeasure
                                                    @if (item.QuantityReceived >= item.QuantityOrdered)
                                                    {
                                                        <i class="feather-check-circle text-success ms-1"></i>
                                                    }
                                                    else if (item.QuantityReceived > 0)
                                                    {
                                                        <i class="feather-clock text-warning ms-1"></i>
                                                    }
                                                </td>
                                                <td>@item.UnitPrice.ToString("C")</td>
                                                <td class="fw-bold">@item.LineTotal.ToString("C")</td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                        }
                        else
                        {
                            <div class="text-center py-4">
                                <i class="feather-package" style="font-size: 3rem; color: #6c757d;"></i>
                                <h6 class="mt-3 text-muted">No items in this purchase order</h6>
                            </div>
                        }
                    </div>
                </div>
            </div>

            <!-- PO Summary & Actions -->
            <div class="col-lg-4">
                <div class="card stretch stretch-full">
                    <div class="card-header">
                        <h5 class="card-title">Order Summary</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between mb-2">
                            <span>Subtotal:</span>
                            <span>@Model.Subtotal.ToString("C")</span>
                        </div>
                        @if (Model.DiscountAmount > 0)
                        {
                            <div class="d-flex justify-content-between mb-2 text-success">
                                <span>Discount:</span>
                                <span>-@Model.DiscountAmount.ToString("C")</span>
                            </div>
                        }
                        <div class="d-flex justify-content-between mb-2">
                            <span>Tax (@Model.TaxRate%):</span>
                            <span>@Model.TaxAmount.ToString("C")</span>
                        </div>
                        @if (Model.ShippingCost > 0)
                        {
                            <div class="d-flex justify-content-between mb-2">
                                <span>Shipping:</span>
                                <span>@Model.ShippingCost.ToString("C")</span>
                            </div>
                        }
                        <hr>
                        <div class="d-flex justify-content-between fw-bold fs-5">
                            <span>Total:</span>
                            <span class="text-primary">@Model.TotalAmount.ToString("C")</span>
                        </div>
                        <div class="text-muted mt-2">
                            Currency: @Model.Currency
                            @if (Model.ExchangeRate != 1)
                            {
                                <br>Exchange Rate: @Model.ExchangeRate
                            }
                        </div>
                    </div>
                </div>

                <!-- Delivery Information -->
                <div class="card stretch stretch-full">
                    <div class="card-header">
                        <h5 class="card-title">Delivery Information</h5>
                    </div>
                    <div class="card-body">
                        @if (!string.IsNullOrEmpty(Model.ShippingAddress))
                        {
                            <div class="mb-3">
                                <label class="form-label text-muted">Shipping Address</label>
                                <div>@Model.ShippingAddress</div>
                            </div>
                        }
                        @if (!string.IsNullOrEmpty(Model.ShippingMethod))
                        {
                            <div class="mb-3">
                                <label class="form-label text-muted">Shipping Method</label>
                                <div>@Model.ShippingMethod</div>
                            </div>
                        }
                    </div>
                </div>

                <!-- PO Progress -->
                <div class="card stretch stretch-full">
                    <div class="card-header">
                        <h5 class="card-title">Order Progress</h5>
                    </div>
                    <div class="card-body">
                        <div class="timeline">
                            <div class="timeline-item">
                                <div class="timeline-marker bg-success"></div>
                                <div class="timeline-content">
                                    <h6 class="timeline-title">PO Created</h6>
                                    <p class="timeline-text">@Model.OrderDate.ToString("MMM dd, yyyy HH:mm")</p>
                                </div>
                            </div>
                            @if (Model.Status != ColorOasisSystemWeb.Enums.POStatus.Draft)
                            {
                                <div class="timeline-item">
                                    <div class="timeline-marker bg-success"></div>
                                    <div class="timeline-content">
                                        <h6 class="timeline-title">PO Submitted</h6>
                                        <p class="timeline-text">@Model.SubmittedDate?.ToString("MMM dd, yyyy HH:mm")</p>
                                    </div>
                                </div>
                            }
                            @if (Model.Status == ColorOasisSystemWeb.Enums.POStatus.Approved || Model.Status == ColorOasisSystemWeb.Enums.POStatus.Sent || Model.Status == ColorOasisSystemWeb.Enums.POStatus.PartiallyReceived || Model.Status == ColorOasisSystemWeb.Enums.POStatus.FullyReceived)
                            {
                                <div class="timeline-item">
                                    <div class="timeline-marker bg-success"></div>
                                    <div class="timeline-content">
                                        <h6 class="timeline-title">PO Approved</h6>
                                        <p class="timeline-text">@Model.ApprovedDate?.ToString("MMM dd, yyyy HH:mm")</p>
                                    </div>
                                </div>
                            }
                            @if (Model.Status == ColorOasisSystemWeb.Enums.POStatus.Sent || Model.Status == ColorOasisSystemWeb.Enums.POStatus.PartiallyReceived || Model.Status == ColorOasisSystemWeb.Enums.POStatus.FullyReceived)
                            {
                                <div class="timeline-item">
                                    <div class="timeline-marker bg-success"></div>
                                    <div class="timeline-content">
                                        <h6 class="timeline-title">PO Sent to Supplier</h6>
                                        <p class="timeline-text">@Model.SentDate?.ToString("MMM dd, yyyy HH:mm")</p>
                                    </div>
                                </div>
                            }
                            @if (Model.Status == ColorOasisSystemWeb.Enums.POStatus.FullyReceived)
                            {
                                <div class="timeline-item">
                                    <div class="timeline-marker bg-success"></div>
                                    <div class="timeline-content">
                                        <h6 class="timeline-title">Fully Received</h6>
                                        <p class="timeline-text">@Model.LastUpdated.ToString("MMM dd, yyyy HH:mm")</p>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Notes & Terms -->
        @if (!string.IsNullOrEmpty(Model.TermsAndConditions) || !string.IsNullOrEmpty(Model.Notes))
        {
            <div class="row">
                <div class="col-12">
                    <div class="card stretch stretch-full">
                        <div class="card-header">
                            <h5 class="card-title">Additional Information</h5>
                        </div>
                        <div class="card-body">
                            @if (!string.IsNullOrEmpty(Model.TermsAndConditions))
                            {
                                <div class="mb-3">
                                    <h6>Terms and Conditions</h6>
                                    <p>@Model.TermsAndConditions</p>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        }

        <!-- Audit Information -->
        <div class="row">
            <div class="col-12">
                <div class="card stretch stretch-full">
                    <div class="card-header">
                        <h5 class="card-title">Audit Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Created Date</label>
                                    <div>@Model.CreatedDate.ToString("MMM dd, yyyy HH:mm")</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Created By</label>
                                    <div>@Model.CreatedBy</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Last Updated</label>
                                    <div>@Model.LastUpdated.ToString("MMM dd, yyyy HH:mm")</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Updated By</label>
                                    <div>@Model.LastUpdatedBy</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- [ Main Content ] end -->
</div>

@section Styles {
    <style>
        .timeline {
            position: relative;
            padding-left: 30px;
        }
        .timeline::before {
            content: '';
            position: absolute;
            left: 10px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #e9ecef;
        }
        .timeline-item {
            position: relative;
            margin-bottom: 20px;
        }
        .timeline-marker {
            position: absolute;
            left: -25px;
            top: 5px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 2px solid #fff;
            box-shadow: 0 0 0 2px #e9ecef;
        }
        .timeline-title {
            font-size: 14px;
            margin-bottom: 5px;
        }
        .timeline-text {
            font-size: 12px;
            color: #6c757d;
            margin: 0;
        }
    </style>
}
