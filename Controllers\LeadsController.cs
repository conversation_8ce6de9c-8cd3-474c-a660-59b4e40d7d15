using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using ColorOasisSystemWeb.Authorization;

namespace ColorOasisSystemWeb.Controllers
{
    [Authorize]
    public class LeadsController : Controller
    {
        [Authorize(Permissions.Leads.View)]
        public IActionResult Index()
        {
            ViewBag.Title = "Leads";
            ViewBag.Message = "Manage sales leads, prospect tracking, and lead conversion.";
            return View("~/Views/Shared/ComingSoon.cshtml");
        }

        [Authorize(Permissions.Leads.Create)]
        public IActionResult Create()
        {
            ViewBag.Title = "Add Lead";
            ViewBag.Message = "Add new lead functionality will be implemented here.";
            return View("~/Views/Shared/ComingSoon.cshtml");
        }
    }
}
