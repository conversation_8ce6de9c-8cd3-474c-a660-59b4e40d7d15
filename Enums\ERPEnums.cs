namespace ColorOasisSystemWeb.Enums
{
    // Inventory Management Enums
    public enum ItemType
    {
        Physical = 1,
        Service = 2,
        Digital = 3,
        Kit = 4,
        Assembly = 5
    }

    public enum TransactionType
    {
        Receipt = 1,
        Issue = 2,
        Transfer = 3,
        Adjustment = 4,
        Sale = 5,
        Purchase = 6,
        Return = 7,
        Scrap = 8,
        Production = 9,
        Consumption = 10
    }

    public enum ABCClassification
    {
        A = 1, // High value items
        B = 2, // Medium value items
        C = 3  // Low value items
    }

    public enum AdjustmentReason
    {
        PhysicalCount = 1,
        Damage = 2,
        Theft = 3,
        Obsolescence = 4,
        SystemError = 5,
        Spoilage = 6,
        Other = 7
    }

    // Purchasing Enums
    public enum SupplierStatus
    {
        Active = 1,
        Inactive = 2,
        Suspended = 3,
        Blacklisted = 4,
        Pending = 5
    }

    public enum SupplierType
    {
        Vendor = 1,
        Contractor = 2,
        ServiceProvider = 3,
        Manufacturer = 4,
        Distributor = 5,
        Consultant = 6
    }

    public enum ContractStatus
    {
        Draft = 1,
        UnderReview = 2,
        Approved = 3,
        Active = 4,
        Expired = 5,
        Terminated = 6,
        Renewed = 7
    }

    public enum RequisitionStatus
    {
        Draft = 1,
        Submitted = 2,
        UnderReview = 3,
        Approved = 4,
        Rejected = 5,
        PartiallyOrdered = 6,
        FullyOrdered = 7,
        Cancelled = 8
    }

    public enum POStatus
    {
        Draft = 1,
        Submitted = 2,
        Approved = 3,
        Sent = 4,
        Acknowledged = 5,
        PartiallyReceived = 6,
        FullyReceived = 7,
        Closed = 8,
        Cancelled = 9
    }

    public enum Priority
    {
        Low = 1,
        Normal = 2,
        High = 3,
        Urgent = 4,
        Critical = 5
    }

    public enum ReceiptStatus
    {
        Draft = 1,
        Received = 2,
        QualityCheck = 3,
        Approved = 4,
        Rejected = 5,
        PartiallyAccepted = 6
    }

    public enum QualityStatus
    {
        Pending = 1,
        Passed = 2,
        Failed = 3,
        Conditional = 4,
        Waived = 5
    }

    // Sales Management Enums
    public enum LeadSource
    {
        Website = 1,
        Referral = 2,
        Advertisement = 3,
        SocialMedia = 4,
        EmailCampaign = 5,
        TradeShow = 6,
        ColdCall = 7,
        Partner = 8,
        Other = 9
    }

    public enum LeadStatus
    {
        New = 1,
        Contacted = 2,
        Qualified = 3,
        Proposal = 4,
        Negotiation = 5,
        Won = 6,
        Lost = 7,
        Cancelled = 8
    }

    public enum OpportunityStage
    {
        Prospecting = 1,
        Qualification = 2,
        NeedsAnalysis = 3,
        ValueProposition = 4,
        Proposal = 5,
        Negotiation = 6,
        ClosedWon = 7,
        ClosedLost = 8
    }

    public enum SalesOrderStatus
    {
        Draft = 1,
        Confirmed = 2,
        InProduction = 3,
        ReadyToShip = 4,
        Shipped = 5,
        Delivered = 6,
        Invoiced = 7,
        Paid = 8,
        Cancelled = 9,
        OnHold = 10
    }

    // Accounting Enums
    public enum AccountType
    {
        Asset = 1,
        Liability = 2,
        Equity = 3,
        Revenue = 4,
        Expense = 5,
        CostOfGoodsSold = 6
    }

    public enum JournalEntryType
    {
        General = 1,
        Sales = 2,
        Purchase = 3,
        Payment = 4,
        Receipt = 5,
        Adjustment = 6,
        Closing = 7,
        Opening = 8
    }

    public enum InvoiceStatus
    {
        Draft = 1,
        Sent = 2,
        Viewed = 3,
        PartiallyPaid = 4,
        Paid = 5,
        Overdue = 6,
        Cancelled = 7,
        Refunded = 8
    }

    public enum PaymentMethod
    {
        Cash = 1,
        Check = 2,
        BankTransfer = 3,
        CreditCard = 4,
        DebitCard = 5,
        OnlinePayment = 6,
        MobilePayment = 7,
        Cryptocurrency = 8
    }

    public enum PaymentStatus
    {
        Pending = 1,
        Processing = 2,
        Completed = 3,
        Failed = 4,
        Cancelled = 5,
        Refunded = 6,
        PartiallyRefunded = 7
    }

    // Workflow Enums
    public enum WorkflowStatus
    {
        Draft = 1,
        Active = 2,
        Paused = 3,
        Completed = 4,
        Cancelled = 5,
        Failed = 6
    }

    public enum ApprovalStatus
    {
        Pending = 1,
        Approved = 2,
        Rejected = 3,
        Delegated = 4,
        Escalated = 5
    }

    // Document Management Enums
    public enum DocumentType
    {
        Contract = 1,
        Invoice = 2,
        Receipt = 3,
        PurchaseOrder = 4,
        Quotation = 5,
        Report = 6,
        Certificate = 7,
        Image = 8,
        Spreadsheet = 9,
        Presentation = 10,
        Other = 11
    }

    public enum DocumentStatus
    {
        Draft = 1,
        UnderReview = 2,
        Approved = 3,
        Published = 4,
        Archived = 5,
        Obsolete = 6
    }

    // Analytics Enums
    public enum ReportType
    {
        Financial = 1,
        Sales = 2,
        Inventory = 3,
        Purchasing = 4,
        Customer = 5,
        Supplier = 6,
        Performance = 7,
        Compliance = 8,
        Custom = 9
    }

    public enum ReportFormat
    {
        PDF = 1,
        Excel = 2,
        CSV = 3,
        Word = 4,
        HTML = 5,
        JSON = 6,
        XML = 7
    }

    public enum KPIStatus
    {
        OnTrack = 1,
        AtRisk = 2,
        OffTrack = 3,
        Achieved = 4,
        NotStarted = 5
    }

    // Integration Enums
    public enum IntegrationStatus
    {
        Connected = 1,
        Disconnected = 2,
        Error = 3,
        Syncing = 4,
        Paused = 5
    }

    public enum SyncDirection
    {
        Import = 1,
        Export = 2,
        Bidirectional = 3
    }

    // Mobile App Enums
    public enum DeviceType
    {
        Phone = 1,
        Tablet = 2,
        Desktop = 3,
        Scanner = 4,
        Other = 5
    }

    public enum SyncStatus
    {
        Pending = 1,
        InProgress = 2,
        Completed = 3,
        Failed = 4,
        Conflict = 5
    }

    // Security Enums
    public enum EncryptionLevel
    {
        None = 0,
        Basic = 1,
        Standard = 2,
        High = 3,
        Maximum = 4
    }

    public enum AuditAction
    {
        Create = 1,
        Read = 2,
        Update = 3,
        Delete = 4,
        Login = 5,
        Logout = 6,
        Export = 7,
        Import = 8,
        Approve = 9,
        Reject = 10
    }
}
