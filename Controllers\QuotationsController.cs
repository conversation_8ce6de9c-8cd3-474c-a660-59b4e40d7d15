using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using ColorOasisSystemWeb.Data;
using ColorOasisSystemWeb.Models;
using Microsoft.AspNetCore.Identity;
using Newtonsoft.Json;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Localization;
using ColorOasisSystemWeb.AdvancedNotification.Services;
using ColorOasisSystemWeb.Enums;

namespace ColorOasisSystemWeb.Controllers
{
    [Authorize(Roles = "User, Admin")]
    public class QuotationsController : Controller
    {
        private readonly AppDbContext _context;
        private readonly UserManager<User> _userManager;
        private readonly IStringLocalizer<SharedResource> SharedLocalizer;
        private readonly NotificationService _notificationService;

        public QuotationsController(AppDbContext context, UserManager<User> userManager, IStringLocalizer<SharedResource> sharedLocalizer, NotificationService notificationService)
        {
            _context = context;
            _userManager = userManager;
            SharedLocalizer = sharedLocalizer;
            _notificationService = notificationService;
        }

        // GET: Quotations
        public async Task<IActionResult> Index()
        {
            var appDbContext = _context.Quotations.Include(q => q.Inspection).Include(q => q.User);
            return View(await appDbContext.ToListAsync());
        }

        // GET: Quotations/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var quotation = await _context.Quotations
                .Include(q => q.Inspection)
                .Include(q => q.QuotationDetails)
                .Include(q => q.User)
                .FirstOrDefaultAsync(m => m.Id == id);
            if (quotation == null)
            {
                return NotFound();
            }

            return View(quotation);
        }

        // GET: Quotations/Create
        public async Task<IActionResult> CreateAsync(int InspectionId)
        {
            if (InspectionId!=0)
            {
                Inspection inspection = _context.Inspections
                                         .Include(i => i.InspectionDetails) // Ensure InspectionDetails are loaded
                                         .ThenInclude(Details => Details.Service)
                                         .AsNoTracking()
                                         .FirstOrDefaultAsync(inspect => inspect.Id == InspectionId).Result;

                if (inspection == null)
                    return RedirectToAction("Index", "Inspections");
                // Create a new Quotation instance and set properties based on inspection
                Quotation quotation = new Quotation
                {
                    Code = GetQuotationCode(),
                    Inspection = inspection,
                    InspectionId = InspectionId,
                    RoomsNo = inspection.RoomsNo,
                    POBox = inspection.POBox,
                    ClientId = inspection.ClientId,
                    ClientType = inspection.ClientType,
                    TypeofUnit = inspection.TypeofUnit,
                    UnitCode = inspection.UnitCode,
                    DateTime = DateTime.Now,
                    IsValid = true,
                    UserId = _userManager.GetUserAsync(User)?.Result.Id ?? "0",
                    ValidTo= DateTime.Now,
                };

                quotation.QuotationDetails = inspection.InspectionDetails.Select(d => new QuotationDetails
                {
                    ServiceId = d.ServiceId,
                    ServiceName = d.ServiceName,
                    RoomId = d.RoomId,
                    Qty = d.Qty,
                    Service = d.Service,
                    // Populate fields with values from the Service entity
                    MinimumPrice = d.Service.MinimumPrice,
                    MaximumPrice = d.Service.MaximumPrice,
                    Discount = d.Service.Discount,
                    UnitPrice = d.Service.Price,
                    
                    // Calculate total values based on Qty and UnitPrice
                    Price = d.Qty * (d.Service.Price-d.Service.Discount),
                    TotalDiscount = d.Qty * d.Service.Discount
                }).ToList();
                if (inspection.ClientType == Enums.ClientType.Customer)
                {
                    var Client = _context.Clients
                        .Where(c => c.Id == inspection.ClientId)
                        .Select(c => new {
                            name = c.Name,
                            email = c.Email,
                            phone = c.Phone,
                            address = c.Address,
                            trn = ""
                        })
                        .FirstOrDefault();
                    if (Client != null)
                    {
                        ViewBag.ClientName = Client.name;
                        ViewBag.ClientEmail = Client.email;
                        ViewBag.ClientPhone = Client.phone;
                        ViewBag.ClientAddress = Client.address;
                        ViewBag.ClientTRN = Client.trn;
                    }
                }
                else
                {
                    var Client = _context.Companies
                        .Where(c => c.Id == inspection.ClientId)
                        .Select(c => new {
                            name = c.Name,
                            email = c.Email,
                            phone = c.Phone,
                            address = c.Address,
                            trn = c.CompanyTRN
                        })
                        .FirstOrDefault();
                    if (Client != null)
                    {
                        ViewBag.ClientName = Client.name;
                        ViewBag.ClientEmail = Client.email;
                        ViewBag.ClientPhone = Client.phone;
                        ViewBag.ClientAddress = Client.address;
                        ViewBag.ClientTRN = Client.trn;
                    }
                }
                return View(quotation);
            }

            ViewBag.Code =GetQuotationCode();
            ViewData["InspectionId"] = new SelectList(_context.Inspections, "Id", "Id");
            ViewData["UserId"] = new SelectList(_context.Users, "Id", "Id");
            return View();
        }
        public string GetQuotationCode()
        {
            // Get the date part in the format "ddMMyyyy"
            string datePart = DateTime.Now.ToString("ddMMyyyy");

            // Get the latest quotation code that matches today's date
            var lastQuotation = _context.Quotations
                .Where(q => q.Code.Contains(datePart))
                .OrderByDescending(q => q.Code)
                .FirstOrDefaultAsync().Result;

            // Initialize the sequence number
            int sequenceNumber = 1;

            if (lastQuotation != null)
            {
                // Extract the numeric part and increment it
                string lastSequence = lastQuotation.Code.Substring(13);
                sequenceNumber = int.Parse(lastSequence) + 1;
            }

            // Generate the new quotation code
            string newQuotationCode = $"{SharedLocalizer["Quotation.QuotePrefix"]}{datePart}{sequenceNumber.ToString("D4")}";

            return newQuotationCode;
        }
        // POST: Quotations/Create
        // To protect from overposting attacks, enable the specific properties you want to bind to.
        // For more details, see http://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]//[Bind("Id,Status,Code,UserId,DateTime,ValidTo,InspectionId,IsComapany,TypeofUnit,RoomsNo,UnitCode,POBox,Note,ClientType,ClientId,ClientName,ClientLocation,ClientPhoneNo,ClientTRN,VAT,SubTotal,Discount,Total,Paid,Remain,AddedBy,EditedBy,DeletedBy,IsPaid,IsConverted,IsValid,IsDeleted")] 
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(Quotation quotation, string tableData)
        {
            var quotationDetails = JsonConvert.DeserializeObject<List<QuotationDetails>>(tableData);
            if (quotationDetails.Count < 1)
            {
                ModelState.AddModelError("Inspection Services", SharedLocalizer["Validation.InspectionServices.Required"]);
                return View(quotation);
            }
            else if (quotationDetails.Count > 0)
            {
                quotation.QuotationDetails = quotationDetails;
                quotation.AddedBy = quotation.UserId = _userManager.GetUserAsync(User)?.Result.Id ?? "0";
                quotation.EditedBy = quotation.DeletedBy = "";
                quotation.IsValid = true;
                quotation.IsDeleted = false;
                quotation.DateTime = DateTime.Now;
                quotation.Note=quotation.Note==null?"": quotation.Note;
            }
            if (ModelState.IsValid)
            {
                _context.Add(quotation);
                await _context.SaveChangesAsync();
                
                // Send notification for new quotation
                var currentUser = await _userManager.GetUserAsync(User);
                if (currentUser != null)
                {
                    await _notificationService.CreateAndSendNotification(
                        ModelType.Quotation,
                        ProcessType.Add,
                        quotation.Id,
                        currentUser);
                }
                
                return RedirectToAction(nameof(Index));
            }
            if (quotation.ClientId==0&&(quotation.InspectionId != null || quotation.InspectionId != 0))
            {
                Inspection inspection = _context.Inspections.AsNoTracking().FirstOrDefault(Inspect => Inspect.Id == quotation.InspectionId);
                quotation.ClientType = inspection.ClientType;
                quotation.ClientId = inspection.ClientId;
            }
            if (quotation.ClientType == Enums.ClientType.Customer)
            {
                var Client = _context.Clients
                    .Where(c => c.Id == quotation.ClientId)
                    .AsNoTracking()
                    .Select(c => new {
                        name = c.Name,
                        email = c.Email,
                        phone = c.Phone,
                        address = c.Address,
                        trn = ""
                    })
                    .FirstOrDefault();
                if (Client != null)
                {
                    ViewBag.ClientName = Client.name;
                    ViewBag.ClientEmail = Client.email;
                    ViewBag.ClientPhone = Client.phone;
                    ViewBag.ClientAddress = Client.address;
                    ViewBag.ClientTRN = Client.trn;
                }
            }
            else
            {
                var Client = _context.Companies
                    .Where(c => c.Id == quotation.ClientId)
                    .AsNoTracking()
                    .Select(c => new {
                        name = c.Name,
                        email = c.Email,
                        phone = c.Phone,
                        address = c.Address,
                        trn = c.CompanyTRN
                    })
                    .FirstOrDefault();
                if (Client != null)
                {
                    ViewBag.ClientName = Client.name;
                    ViewBag.ClientEmail = Client.email;
                    ViewBag.ClientPhone = Client.phone;
                    ViewBag.ClientAddress = Client.address;
                    ViewBag.ClientTRN = Client.trn;
                }
            }

            ViewData["InspectionId"] = new SelectList(_context.Inspections, "Id", "Id", quotation.InspectionId);
            ViewData["UserId"] = new SelectList(_context.Users, "Id", "Id", quotation.UserId);
            return View(quotation);
        }

        // GET: Quotations/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var quotation = await _context.Quotations.FindAsync(id);
            if (quotation == null)
            {
                return NotFound();
            }
            ViewData["InspectionId"] = new SelectList(_context.Inspections, "Id", "Id", quotation.InspectionId);
            ViewData["UserId"] = new SelectList(_context.Users, "Id", "Id", quotation.UserId);
            return View(quotation);
        }

        // POST: Quotations/Edit/5
        // To protect from overposting attacks, enable the specific properties you want to bind to.
        // For more details, see http://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("Id,Status,Code,UserId,DateTime,ValidTo,InspectionId,IsComapany,TypeofUnit,RoomsNo,UnitCode,POBox,Note,ClientType,ClientId,ClientName,ClientLocation,ClientPhoneNo,ClientTRN,VAT,SubTotal,Discount,Total,Paid,Remain,AddedBy,EditedBy,DeletedBy,IsPaid,IsConverted,IsValid,IsDeleted")] Quotation quotation)
        {
            if (id != quotation.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    _context.Update(quotation);
                    await _context.SaveChangesAsync();
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!QuotationExists(quotation.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
            }
            ViewData["InspectionId"] = new SelectList(_context.Inspections, "Id", "Id", quotation.InspectionId);
            ViewData["UserId"] = new SelectList(_context.Users, "Id", "Id", quotation.UserId);
            return View(quotation);
        }

        // GET: Quotations/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var quotation = await _context.Quotations
                .Include(q => q.Inspection)
                .Include(q => q.User)
                .FirstOrDefaultAsync(m => m.Id == id);
            if (quotation == null)
            {
                return NotFound();
            }

            return View(quotation);
        }

        // POST: Quotations/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var quotation = await _context.Quotations.FindAsync(id);
            if (quotation != null)
            {
                _context.Quotations.Remove(quotation);
            }

            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
        }

        private bool QuotationExists(int id)
        {
            return _context.Quotations.Any(e => e.Id == id);
        }
    }
}
