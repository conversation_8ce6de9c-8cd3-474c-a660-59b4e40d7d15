@model IEnumerable<ColorOasisSystemWeb.Models.Sales.Invoice>

@{
    ViewData["Title"] = "Invoices";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="nxl-content">
    <!-- [ page-header ] start -->
    <div class="page-header">
        <div class="page-header-left d-flex align-items-center">
            <div class="page-header-title">
                <h5 class="m-b-10">Invoices</h5>
            </div>
            <ul class="breadcrumb">
                <li class="breadcrumb-item"><a href="@Url.Action("Index", "Home")">Home</a></li>
                <li class="breadcrumb-item">ERP</li>
                <li class="breadcrumb-item">Accounting</li>
                <li class="breadcrumb-item">Invoices</li>
            </ul>
        </div>
        <div class="page-header-right ms-auto">
            <div class="page-header-right-items">
                <div class="d-flex d-md-none">
                    <a href="javascript:void(0)" class="page-header-right-close-toggle">
                        <i class="feather-arrow-left me-2"></i>
                        <span>Back</span>
                    </a>
                </div>
                <div class="d-flex align-items-center gap-2 page-header-right-items-wrapper">
                    <a href="@Url.Action("Create")" class="btn btn-primary">
                        <i class="feather-plus me-2"></i>
                        <span>Create Invoice</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
    <!-- [ page-header ] end -->

    <!-- [ Main Content ] start -->
    <div class="main-content">
        <div class="row">
            <div class="col-lg-12">
                <div class="card stretch stretch-full">
                    <div class="card-header">
                        <h5 class="card-title">Invoices</h5>
                        <div class="card-header-action">
                            <div class="card-header-btn">
                                <div data-bs-toggle="tooltip" title="Refresh">
                                    <a href="javascript:void(0);" class="avatar-text avatar-md" onclick="location.reload();">
                                        <i class="feather-refresh-cw"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body custom-card-action p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0" id="invoicesTable">
                                <thead>
                                    <tr>
                                        <th>Invoice Number</th>
                                        <th>Customer</th>
                                        <th>Invoice Date</th>
                                        <th>Due Date</th>
                                        <th>Total Amount</th>
                                        <th>Status</th>
                                        <th class="text-end">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var invoice in Model)
                                    {
                                        <tr>
                                            <td>
                                                <div class="fw-bold text-dark">@invoice.InvoiceNumber</div>
                                                <div class="fs-12 text-muted">@invoice.Items?.Count() item(s)</div>
                                            </td>
                                            <td>
                                                <div class="fw-bold text-dark">@invoice.Client?.Name</div>
                                                @if (!string.IsNullOrEmpty(invoice.Client?.Email))
                                                {
                                                    <div class="fs-12 text-muted">@invoice.Client.Email</div>
                                                }
                                            </td>
                                            <td>
                                                <div class="fw-bold">@invoice.InvoiceDate.ToString("MMM dd, yyyy")</div>
                                            </td>
                                            <td>
                                                <div class="fw-bold">@invoice.DueDate.ToString("MMM dd, yyyy")</div>
                                                @{
                                                    var daysUntilDue = (invoice.DueDate - DateTime.Now).Days;
                                                    var dueClass = daysUntilDue < 0 ? "text-danger" : daysUntilDue <= 3 ? "text-warning" : "text-success";
                                                }
                                                <div class="fs-12 @dueClass">
                                                    @if (daysUntilDue < 0)
                                                    {
                                                        <span>@Math.Abs(daysUntilDue) days overdue</span>
                                                    }
                                                    else if (daysUntilDue == 0)
                                                    {
                                                        <span>Due today</span>
                                                    }
                                                    else
                                                    {
                                                        <span>@daysUntilDue days remaining</span>
                                                    }
                                                </div>
                                            </td>
                                            <td>
                                                <div class="fw-bold">@invoice.TotalAmount.ToString("C")</div>
                                                @if (invoice.TaxAmount > 0)
                                                {
                                                    <div class="fs-12 text-muted">+@invoice.TaxAmount.ToString("C") tax</div>
                                                }
                                                @if (invoice.AmountPaid > 0)
                                                {
                                                    <div class="fs-12 text-success">@invoice.AmountPaid.ToString("C") paid</div>
                                                }
                                            </td>
                                            <td>
                                                @switch (invoice.Status)
                                                {
                                                    case ColorOasisSystemWeb.Models.Sales.InvoiceStatus.Draft:
                                                        <span class="badge bg-soft-secondary text-secondary">Draft</span>
                                                        break;
                                                    case ColorOasisSystemWeb.Models.Sales.InvoiceStatus.Sent:
                                                        <span class="badge bg-soft-primary text-primary">Sent</span>
                                                        break;
                                                    case ColorOasisSystemWeb.Models.Sales.InvoiceStatus.PartiallyPaid:
                                                        <span class="badge bg-soft-warning text-warning">Partially Paid</span>
                                                        break;
                                                    case ColorOasisSystemWeb.Models.Sales.InvoiceStatus.Paid:
                                                        <span class="badge bg-soft-success text-success">Paid</span>
                                                        break;
                                                    case ColorOasisSystemWeb.Models.Sales.InvoiceStatus.Overdue:
                                                        <span class="badge bg-soft-danger text-danger">Overdue</span>
                                                        break;
                                                    case ColorOasisSystemWeb.Models.Sales.InvoiceStatus.Cancelled:
                                                        <span class="badge bg-soft-danger text-danger">Cancelled</span>
                                                        break;
                                                    case ColorOasisSystemWeb.Models.Sales.InvoiceStatus.Refunded:
                                                        <span class="badge bg-soft-info text-info">Refunded</span>
                                                        break;
                                                }
                                            </td>
                                            <td>
                                                <div class="hstack gap-2 justify-content-end">
                                                    <a href="@Url.Action("Details", new { id = invoice.Id })" class="avatar-text avatar-md" data-bs-toggle="tooltip" title="View Details">
                                                        <i class="feather-eye"></i>
                                                    </a>
                                                    @if (invoice.Status == ColorOasisSystemWeb.Models.Sales.InvoiceStatus.Draft)
                                                    {
                                                        <a href="@Url.Action("Edit", new { id = invoice.Id })" class="avatar-text avatar-md" data-bs-toggle="tooltip" title="Edit">
                                                            <i class="feather-edit-3"></i>
                                                        </a>
                                                    }
                                                    <div class="dropdown">
                                                        <a href="javascript:void(0);" class="avatar-text avatar-md" data-bs-toggle="dropdown" data-bs-offset="0,10">
                                                            <i class="feather-more-horizontal"></i>
                                                        </a>
                                                        <div class="dropdown-menu dropdown-menu-end">
                                                            @if (invoice.Status == ColorOasisSystemWeb.Models.Sales.InvoiceStatus.Draft)
                                                            {
                                                                <a class="dropdown-item" href="@Url.Action("Send", new { id = invoice.Id })">
                                                                    <i class="feather-send me-3"></i>
                                                                    <span>Send Invoice</span>
                                                                </a>
                                                            }
                                                            @if (invoice.Status != ColorOasisSystemWeb.Models.Sales.InvoiceStatus.Paid && invoice.Status != ColorOasisSystemWeb.Models.Sales.InvoiceStatus.Cancelled)
                                                            {
                                                                <a class="dropdown-item" href="@Url.Action("RecordPayment", new { id = invoice.Id })">
                                                                    <i class="feather-dollar-sign me-3"></i>
                                                                    <span>Record Payment</span>
                                                                </a>
                                                            }
                                                            <a class="dropdown-item" href="@Url.Action("Print", new { id = invoice.Id })" target="_blank">
                                                                <i class="feather-printer me-3"></i>
                                                                <span>Print</span>
                                                            </a>
                                                            <a class="dropdown-item" href="@Url.Action("Download", new { id = invoice.Id })">
                                                                <i class="feather-download me-3"></i>
                                                                <span>Download PDF</span>
                                                            </a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- [ Main Content ] end -->
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            $('#invoicesTable').DataTable({
                responsive: true,
                pageLength: 25,
                order: [[0, 'desc']],
                columnDefs: [
                    { orderable: false, targets: [6] }
                ]
            });
        });
    </script>
}
