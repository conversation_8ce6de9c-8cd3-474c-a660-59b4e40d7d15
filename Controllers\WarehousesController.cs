using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using ColorOasisSystemWeb.Data;
using ColorOasisSystemWeb.Models.Inventory;
using ColorOasisSystemWeb.Authorization;
using Microsoft.AspNetCore.Identity;
using ColorOasisSystemWeb.Models;
using ColorOasisSystemWeb.AdvancedNotification.Services;
using ColorOasisSystemWeb.Enums;

namespace ColorOasisSystemWeb.Controllers
{
    [Authorize]
    public class WarehousesController : Controller
    {
        private readonly AppDbContext _context;
        private readonly UserManager<User> _userManager;
        private readonly NotificationService _notificationService;
        private readonly ILogger<WarehousesController> _logger;

        public WarehousesController(
            AppDbContext context,
            UserManager<User> userManager,
            NotificationService notificationService,
            ILogger<WarehousesController> logger)
        {
            _context = context;
            _userManager = userManager;
            _notificationService = notificationService;
            _logger = logger;
        }

        // GET: Warehouses
        [Authorize(Permissions.Warehouse.View)]
        public async Task<IActionResult> Index()
        {
            try
            {
                var warehouses = await _context.Warehouses
                    .Include(w => w.BinLocations)
                    .Include(w => w.InventoryLocations)
                    .ToListAsync();

                return View(warehouses);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving warehouses");
                TempData["Error"] = "An error occurred while retrieving warehouses.";
                return View(new List<Warehouse>());
            }
        }

        // GET: Warehouses/Details/5
        [Authorize(Permissions.Warehouse.View)]
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var warehouse = await _context.Warehouses
                .Include(w => w.BinLocations)
                .Include(w => w.InventoryLocations)
                .ThenInclude(il => il.InventoryItem)
                .FirstOrDefaultAsync(m => m.Id == id);

            if (warehouse == null)
            {
                return NotFound();
            }

            return View(warehouse);
        }

        // GET: Warehouses/Create
        [Authorize(Permissions.Warehouse.Create)]
        public IActionResult Create()
        {
            return View();
        }

        // POST: Warehouses/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        [Authorize(Permissions.Warehouse.Create)]
        public async Task<IActionResult> Create([Bind("Code,Name,Address,Manager,Phone,Email,IsActive,IsDefault")] Warehouse warehouse)
        {
            if (ModelState.IsValid)
            {
                try
                {
                    warehouse.CreatedAt = DateTime.UtcNow;
                    warehouse.UpdatedAt = DateTime.UtcNow;

                    // If this is set as default, unset other defaults
                    if (warehouse.IsDefault)
                    {
                        var existingDefaults = await _context.Warehouses
                            .Where(w => w.IsDefault)
                            .ToListAsync();

                        foreach (var existing in existingDefaults)
                        {
                            existing.IsDefault = false;
                        }
                    }

                    _context.Add(warehouse);
                    await _context.SaveChangesAsync();

                    // Send notification
                    var currentUser = await _userManager.GetUserAsync(User);
                    if (currentUser != null)
                    {
                        await _notificationService.CreateAndSendNotification(
                            ModelType.Warehouse,
                            ProcessType.Add,
                            warehouse.Id,
                            currentUser);
                    }

                    TempData["Success"] = "Warehouse created successfully.";
                    return RedirectToAction(nameof(Index));
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error creating warehouse");
                    TempData["Error"] = "An error occurred while creating the warehouse.";
                }
            }
            return View(warehouse);
        }

        // GET: Warehouses/Edit/5
        [Authorize(Permissions.Warehouse.Edit)]
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var warehouse = await _context.Warehouses.FindAsync(id);
            if (warehouse == null)
            {
                return NotFound();
            }
            return View(warehouse);
        }

        // POST: Warehouses/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        [Authorize(Permissions.Warehouse.Edit)]
        public async Task<IActionResult> Edit(int id, [Bind("Id,Code,Name,Address,Manager,Phone,Email,IsActive,IsDefault,CreatedAt")] Warehouse warehouse)
        {
            if (id != warehouse.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    warehouse.UpdatedAt = DateTime.UtcNow;

                    // If this is set as default, unset other defaults
                    if (warehouse.IsDefault)
                    {
                        var existingDefaults = await _context.Warehouses
                            .Where(w => w.IsDefault && w.Id != warehouse.Id)
                            .ToListAsync();

                        foreach (var existing in existingDefaults)
                        {
                            existing.IsDefault = false;
                        }
                    }

                    _context.Update(warehouse);
                    await _context.SaveChangesAsync();

                    // Send notification
                    var currentUser = await _userManager.GetUserAsync(User);
                    if (currentUser != null)
                    {
                        await _notificationService.CreateAndSendNotification(
                            ModelType.Warehouse,
                            ProcessType.Edit,
                            warehouse.Id,
                            currentUser);
                    }

                    TempData["Success"] = "Warehouse updated successfully.";
                    return RedirectToAction(nameof(Index));
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!WarehouseExists(warehouse.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error updating warehouse");
                    TempData["Error"] = "An error occurred while updating the warehouse.";
                }
            }
            return View(warehouse);
        }

        // GET: Warehouses/Delete/5
        [Authorize(Permissions.Warehouse.Delete)]
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var warehouse = await _context.Warehouses
                .Include(w => w.BinLocations)
                .Include(w => w.InventoryLocations)
                .FirstOrDefaultAsync(m => m.Id == id);
            if (warehouse == null)
            {
                return NotFound();
            }

            return View(warehouse);
        }

        // POST: Warehouses/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        [Authorize(Permissions.Warehouse.Delete)]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            try
            {
                var warehouse = await _context.Warehouses
                    .Include(w => w.InventoryLocations)
                    .Include(w => w.BinLocations)
                    .FirstOrDefaultAsync(w => w.Id == id);

                if (warehouse != null)
                {
                    // Check if warehouse has inventory
                    if (warehouse.InventoryLocations?.Any(il => il.QuantityOnHand > 0) == true)
                    {
                        TempData["Error"] = "Cannot delete warehouse with existing inventory. Please move or remove all inventory first.";
                        return RedirectToAction(nameof(Delete), new { id });
                    }

                    _context.Warehouses.Remove(warehouse);
                    await _context.SaveChangesAsync();

                    // Send notification
                    var currentUser = await _userManager.GetUserAsync(User);
                    if (currentUser != null)
                    {
                        await _notificationService.CreateAndSendNotification(
                            ModelType.Warehouse,
                            ProcessType.Delete,
                            id,
                            currentUser);
                    }

                    TempData["Success"] = "Warehouse deleted successfully.";
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting warehouse");
                TempData["Error"] = "An error occurred while deleting the warehouse.";
            }

            return RedirectToAction(nameof(Index));
        }

        private bool WarehouseExists(int id)
        {
            return _context.Warehouses.Any(e => e.Id == id);
        }
    }
}
