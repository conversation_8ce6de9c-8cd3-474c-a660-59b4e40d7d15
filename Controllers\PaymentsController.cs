using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using ColorOasisSystemWeb.Data;
using ColorOasisSystemWeb.Models.Accounting;
using ColorOasisSystemWeb.Authorization;
using Microsoft.AspNetCore.Identity;
using ColorOasisSystemWeb.Models;
using ColorOasisSystemWeb.AdvancedNotification.Services;
using ColorOasisSystemWeb.Enums;
using Microsoft.AspNetCore.Mvc.Rendering;

namespace ColorOasisSystemWeb.Controllers
{
    [Authorize]
    public class PaymentsController : Controller
    {
        private readonly AppDbContext _context;
        private readonly UserManager<User> _userManager;
        private readonly NotificationService _notificationService;
        private readonly ILogger<PaymentsController> _logger;

        public PaymentsController(
            AppDbContext context,
            UserManager<User> userManager,
            NotificationService notificationService,
            ILogger<PaymentsController> logger)
        {
            _context = context;
            _userManager = userManager;
            _notificationService = notificationService;
            _logger = logger;
        }

        // GET: Payments
        [Authorize(Permissions.Payments.View)]
        public async Task<IActionResult> Index()
        {
            try
            {
                var payments = await _context.Payments
                    .Include(p => p.Invoice)
                    .ThenInclude(i => i.Client)
                    .Include(p => p.Invoice)
                    .ThenInclude(i => i.Company)
                    .OrderByDescending(p => p.PaymentDate)
                    .ToListAsync();

                return View(payments);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving payments");
                TempData["Error"] = "An error occurred while retrieving payments.";
                return View(new List<Payment>());
            }
        }

        // GET: Payments/Details/5
        [Authorize(Permissions.Payments.View)]
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var payment = await _context.Payments
                .Include(p => p.Invoice)
                .ThenInclude(i => i.Client)
                .Include(p => p.Invoice)
                .ThenInclude(i => i.Company)
                .FirstOrDefaultAsync(m => m.Id == id);

            if (payment == null)
            {
                return NotFound();
            }

            return View(payment);
        }

        // GET: Payments/Create
        [Authorize(Permissions.Payments.Create)]
        public IActionResult Create(int? invoiceId)
        {
            var invoicesQuery = _context.Invoices
                .Where(i => i.Status == InvoiceStatus.Sent || i.Status == InvoiceStatus.PartiallyPaid)
                .Include(i => i.Client)
                .Include(i => i.Company);

            if (invoiceId.HasValue)
            {
                ViewData["InvoiceId"] = new SelectList(invoicesQuery, "Id", "InvoiceNumber", invoiceId.Value);
            }
            else
            {
                ViewData["InvoiceId"] = new SelectList(invoicesQuery, "Id", "InvoiceNumber");
            }

            return View();
        }

        // POST: Payments/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        [Authorize(Permissions.Payments.Create)]
        public async Task<IActionResult> Create([Bind("InvoiceId,PaymentAmount,PaymentMethod,PaymentDate,ReferenceNumber,BankName,CheckNumber,TransactionId,Notes,Currency,ExchangeRate")] Payment payment)
        {
            if (ModelState.IsValid)
            {
                try
                {
                    // Generate payment number
                    payment.PaymentNumber = await GeneratePaymentNumber();
                    payment.Status = PaymentStatus.Completed;

                    var currentUser = await _userManager.GetUserAsync(User);
                    payment.CreatedBy = currentUser?.UserName ?? "System";
                    payment.ReceivedBy = currentUser?.UserName ?? "System";
                    payment.CreatedAt = DateTime.UtcNow;
                    payment.UpdatedAt = DateTime.UtcNow;

                    _context.Add(payment);

                    // Update invoice payment status
                    var invoice = await _context.Invoices.FindAsync(payment.InvoiceId);
                    if (invoice != null)
                    {
                        invoice.AmountPaid += payment.PaymentAmount;

                        if (invoice.AmountPaid >= invoice.TotalAmount)
                        {
                            invoice.Status = InvoiceStatus.Paid;
                            invoice.PaidDate = payment.PaymentDate;
                        }
                        else if (invoice.AmountPaid > 0)
                        {
                            invoice.Status = InvoiceStatus.PartiallyPaid;
                        }

                        invoice.UpdatedAt = DateTime.UtcNow;
                        invoice.UpdatedBy = payment.CreatedBy;

                        // Create invoice history entry
                        var historyEntry = new InvoiceHistory
                        {
                            InvoiceId = invoice.Id,
                            Action = "Payment Received",
                            Description = $"Payment of {payment.PaymentAmount:C} received via {payment.PaymentMethod}",
                            ActionDate = DateTime.UtcNow,
                            PerformedBy = payment.CreatedBy,
                            IpAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "",
                            UserAgent = HttpContext.Request.Headers["User-Agent"].ToString()
                        };
                        _context.InvoiceHistory.Add(historyEntry);
                    }

                    await _context.SaveChangesAsync();

                    // Send notification
                    if (currentUser != null)
                    {
                        await _notificationService.CreateAndSendNotification(
                            ModelType.Payment,
                            ProcessType.Add,
                            payment.Id,
                            currentUser);
                    }

                    TempData["Success"] = "Payment recorded successfully.";
                    return RedirectToAction(nameof(Details), new { id = payment.Id });
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error creating payment");
                    TempData["Error"] = "An error occurred while recording the payment.";
                }
            }

            var invoicesQuery = _context.Invoices
                .Where(i => i.Status == InvoiceStatus.Sent || i.Status == InvoiceStatus.PartiallyPaid)
                .Include(i => i.Client)
                .Include(i => i.Company);
            ViewData["InvoiceId"] = new SelectList(invoicesQuery, "Id", "InvoiceNumber", payment.InvoiceId);
            return View(payment);
        }

        // POST: Payments/Refund/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        [Authorize(Permissions.Payments.Edit)]
        public async Task<IActionResult> Refund(int id, decimal refundAmount, string refundReason)
        {
            try
            {
                var payment = await _context.Payments
                    .Include(p => p.Invoice)
                    .FirstOrDefaultAsync(p => p.Id == id);

                if (payment == null)
                {
                    return NotFound();
                }

                if (payment.Status != PaymentStatus.Completed)
                {
                    TempData["Error"] = "Only completed payments can be refunded.";
                    return RedirectToAction(nameof(Details), new { id });
                }

                if (refundAmount <= 0 || refundAmount > payment.PaymentAmount)
                {
                    TempData["Error"] = "Invalid refund amount.";
                    return RedirectToAction(nameof(Details), new { id });
                }

                payment.Status = PaymentStatus.Refunded;
                payment.RefundAmount = refundAmount;
                payment.RefundDate = DateTime.UtcNow;
                payment.RefundReason = refundReason;

                var currentUser = await _userManager.GetUserAsync(User);
                payment.ProcessedBy = currentUser?.UserName ?? "System";
                payment.UpdatedAt = DateTime.UtcNow;

                // Update invoice payment status
                if (payment.Invoice != null)
                {
                    payment.Invoice.AmountPaid -= refundAmount;

                    if (payment.Invoice.AmountPaid <= 0)
                    {
                        payment.Invoice.Status = InvoiceStatus.Sent;
                        payment.Invoice.PaidDate = null;
                    }
                    else if (payment.Invoice.AmountPaid < payment.Invoice.TotalAmount)
                    {
                        payment.Invoice.Status = InvoiceStatus.PartiallyPaid;
                    }

                    payment.Invoice.UpdatedAt = DateTime.UtcNow;
                    payment.Invoice.UpdatedBy = payment.ProcessedBy;

                    // Create invoice history entry
                    var historyEntry = new InvoiceHistory
                    {
                        InvoiceId = payment.Invoice.Id,
                        Action = "Payment Refunded",
                        Description = $"Payment refund of {refundAmount:C}. Reason: {refundReason}",
                        ActionDate = DateTime.UtcNow,
                        PerformedBy = payment.ProcessedBy,
                        IpAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "",
                        UserAgent = HttpContext.Request.Headers["User-Agent"].ToString()
                    };
                    _context.InvoiceHistory.Add(historyEntry);
                }

                await _context.SaveChangesAsync();

                TempData["Success"] = "Payment refunded successfully.";
                return RedirectToAction(nameof(Details), new { id });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing refund");
                TempData["Error"] = "An error occurred while processing the refund.";
                return RedirectToAction(nameof(Details), new { id });
            }
        }

        // API method to get invoice details for payment
        [HttpGet]
        public async Task<IActionResult> GetInvoiceDetails(int invoiceId)
        {
            try
            {
                var invoice = await _context.Invoices
                    .Include(i => i.Client)
                    .Include(i => i.Company)
                    .FirstOrDefaultAsync(i => i.Id == invoiceId);

                if (invoice == null)
                {
                    return Json(new { success = false, message = "Invoice not found" });
                }

                var remainingAmount = invoice.TotalAmount - invoice.AmountPaid;

                return Json(new {
                    success = true,
                    invoiceNumber = invoice.InvoiceNumber,
                    totalAmount = invoice.TotalAmount,
                    amountPaid = invoice.AmountPaid,
                    remainingAmount = remainingAmount,
                    currency = invoice.Currency,
                    customerName = invoice.Client?.Name ?? invoice.Company?.Name ?? "Unknown"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting invoice details");
                return Json(new { success = false, message = "Error retrieving invoice details" });
            }
        }

        // Helper method to generate payment number
        private async Task<string> GeneratePaymentNumber()
        {
            var today = DateTime.Today;
            var prefix = $"PAY{today:yyyyMM}";

            var lastPayment = await _context.Payments
                .Where(p => p.PaymentNumber.StartsWith(prefix))
                .OrderByDescending(p => p.PaymentNumber)
                .FirstOrDefaultAsync();

            int nextNumber = 1;
            if (lastPayment != null)
            {
                var numberPart = lastPayment.PaymentNumber.Substring(prefix.Length);
                if (int.TryParse(numberPart, out int lastNumber))
                {
                    nextNumber = lastNumber + 1;
                }
            }

            return $"{prefix}{nextNumber:D4}";
        }
    }
}
