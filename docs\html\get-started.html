<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="utf-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
		<meta name="author" content="Bootstrap-ecommerce by Vosidiy" />
		<title>Duralux - Get Started</title>
		<!-- ========== Start Favicon ========== -->
		<link type="image/x-icon" rel="shortcut icon" href="./../assets/images/favicon.ico" />
		<!-- ========== End Favicon ========== -->
		<!-- ========== Start CSS Framework ========== -->
		<link type="text/css" rel="stylesheet" href="./../assets/css/app.min.css" />
		<link type="text/css" rel="stylesheet" href="./../assets/css/bootstrap.min.css" />
		<!-- ========== End CSS Framework ========== -->
		<!-- ========== Start Vandors CSS ========== -->
		<link type="text/css" rel="stylesheet" href="./../assets/css/ui.css" />
		<link type="text/css" rel="stylesheet" href="./../assets/css/rainbow.css" />
		<link type="text/css" rel="stylesheet" href="./../assets/css/perfectScroll.css" />
		<link type="text/css" rel="stylesheet" href="./../assets/fontawesome/css/all.min.css" />
		<link type="text/css" rel="stylesheet" href="./../assets/css/bootstrap-icons.min.css " />
		<link type="text/css" rel="stylesheet" href="./../assets/plugins/jstree/themes/default/style.min.css" />
		<!-- ========== End Vandors CSS ========== -->
		<!-- ========== Start Custom CSS ========== -->
		<link type="text/css" rel="stylesheet" href="./../assets/css/style.css" />
		<link type="text/css" rel="stylesheet" href="./../assets/css/responsive.css" media="only screen and (max-width: 1200px)" />
		<!-- ========== End Custom CSS ========== -->
	</head>
	<body>
		<div class="wrapper vh-100">
			<div class="row g-0">
				<!-- ========== Start Main Header ========== -->
				<div class="col-12">
					<header class="aside-header d-flex justify-content-between">
						<a href="./../documentations.html"><img class="title" src="./../assets/images/main-logo.png" alt="" /></a>
						<div class="hstack gap-3">
							<div class="d-lg-none">
								<a href="javascript:void(0);" class="wd-40 ht-40 bg-gray-100 d-flex justify-content-center align-items-center rounded-circle border lh-0 tx-dark" data-sidebar-toggler="sidebar-toggle">
									<i class="bi bi-list tx-18"></i>
								</a>
							</div>
							<div class="dropdown d-none d-sm-block">
								<a href="javascript:void(0);" class="wd-40 ht-40 bg-gray-100 d-flex justify-content-center align-items-center rounded-circle border lh-0 tx-dark" data-bs-toggle="dropdown" data-bs-offset="0,20">
									<i class="bi bi-lightbulb tx-15"></i>
								</a>
								<div class="dropdown-menu">
									<a class="dropdown-item" href="javascript:void(0);">
										<i class="tx-20 fa-brands fa-bootstrap me-3"></i>
										<span>Bootstrap</span>
									</a>
									<a class="dropdown-item disabled" href="javascript:void(0);">
										<i class="tx-20 fa-brands fa-react me-3"></i>
										<span>React <small class="tx-11">( coming soon )</small></span>
									</a>
									<a class="dropdown-item disabled" href="javascript:void(0);">
										<i class="tx-20 fa-brands fa-vuejs me-3"></i>
										<span>Vue <small class="tx-11">( coming soon )</small></span>
									</a>
									<a class="dropdown-item disabled" href="javascript:void(0);">
										<i class="tx-20 fa-brands fa-laravel me-3"></i>
										<span>Laravel <small class="tx-11">( coming soon )</small></span>
									</a>
									<a class="dropdown-item disabled" href="javascript:void(0);">
										<i class="tx-20 fa-brands fa-angular me-3"></i>
										<span>Angular <small class="tx-11">( coming soon )</small></span>
									</a>
									<div class="dropdown-divider"></div>
									<a class="dropdown-item" href="javascript:void(0);">
										<i class="fa-regular fa-life-ring me-3 tx-12"></i>
										<span class="tx-12 tx-bolder tx-uppercase">V_1.0.0</span>
									</a>
								</div>
							</div>
							<div class="hstack gap-1">
								<a href="http://www.getDuralux.link" target="_blank" class="btn btn-danger">Live Preview</a>
								<a href="https://themeforest.net/user/theme_ocean/" target="_blank" class="btn btn-primary d-none d-sm-flex">Get Support</a>
							</div>
						</div>
					</header>
				</div>
				<!-- ========== End Main Header ========== -->
				<!-- ========== Start Main Container ========== -->
				<div class="col-12">
					<div class="d-flex position-relative">
						<!-- ========== Start Aside ========== -->
						<aside class="aside-wrap wd-450">
							<div class="aside-content" id="navbar_aside">
								<div class="d-lg-none p-4 sidebar-toggler-header hstack justify-content-between">
									<h3 class="mb-0">Documentations</h3>
									<a href="javascript:void(0);" class="wd-40 ht-40 bg-gray-100 d-flex justify-content-center align-items-center rounded-circle lh-0 tx-dark float-end" data-sidebar-toggler="sidebar-toggle">
										<i class="bi bi-x tx-18"></i>
									</a>
								</div>
								<nav class="menu-wrap" id="sidebarMenuScroll">
									<ul class="menu-aside">
										<li class="tx-11 tx-dark tx-uppercase tx-bold manu-label">Getting Started &rarr;</li>
										<li><a class="page-scroll nav-link" href="#introduction">Introduction</a></li>
										<li><a class="page-scroll nav-link" href="#features">Features</a></li>
										<li><a class="page-scroll nav-link" href="#compatibility">Compatibility</a></li>
										<li><a class="page-scroll nav-link" href="#license">License</a></li>
									</ul>
								</nav>
								<hr class="tx-gray-400" />
								<div class="hstack flex-column pd-30 bg-white">
									<div class="w-100 h-100">
										<a href="./../documentation.html" class="p-4 bg-gray-100 bd bd-gray-2 rounded mb-4 tx-12 tx-dark fw-bold d-block w-100" style="background: #f3f3f5">Back Home &rarr;</a>
										<a href="installation.html" class="p-4 bg-gray-100 bd bd-gray-2 rounded mb-4 tx-12 tx-dark fw-bold d-block w-100" style="background: #f3f3f5">Installation &rarr;</a>
										<a href="folder-structure.html" class="p-4 bg-gray-100 bd bd-gray-2 rounded mb-4 tx-12 tx-dark fw-bold d-block w-100" style="background: #f3f3f5">Folder Structure &rarr;</a>
										<a href="configuration.html" class="p-4 bg-gray-100 bd bd-gray-2 rounded mb-4 tx-12 tx-dark fw-bold d-block w-100" style="background: #f3f3f5">Configuration &rarr;</a>
										<a href="layouts.html" class="p-4 bg-gray-100 bd bd-gray-2 rounded mb-4 tx-12 tx-dark fw-bold d-block w-100" style="background: #f3f3f5">Layouts &rarr;</a>
										<a href="credit-resource.html" class="p-4 bg-gray-100 bd bd-gray-2 rounded mb-4 tx-12 tx-dark fw-bold d-block w-100" style="background: #f3f3f5">Credit & Resource &rarr;</a>
										<a href="changelog.html" class="p-4 bg-gray-100 bd bd-gray-2 rounded mb-4 tx-12 tx-dark fw-bold d-block w-100" style="background: #f3f3f5">Changelogs &rarr;</a>
									</div>
									<div class="pd-t-30 w-100">
										<a href="https://themeforest.net/user/theme_ocean/" target="_blank" class="btn btn-primary w-100">Download Now</a>
									</div>
								</div>
							</div>
						</aside>
						<!-- ========== End Aside ========== -->
						<!-- ========== Start Main Wrapper ========== -->
						<main class="main-wrap w-100">
							<div class="main-content container pb-4">
								<!-- ========== Start Introduction ========== -->
								<section id="introduction">
									<div class="card mb-4">
										<div class="card-header">
											<h5 class="tx-bold tx-dark mb-0">Introduction</h5>
										</div>
										<div class="card-body">
											<p class="mb-5">Duralux is a clean bootstrap crm admin dashboard and webapps template built with bootstrap latest and modern technology's. It is a fully featured dashboard and admin template comes with tones of well designed UI elements, components, widgets and applications. The template has a simple and elegant design with multi-purpose usages. Duralux is well crafted, with all the components neatly and carefully designed and arranged within most developer friendly & highly customizable template of!</p>
											<p class="mb-5">Duralux can be used for most type of dashboard and app templates like ecommerce, analytics, crm, cms, backend website and time line as well as desktop or mobile applications. Amazing flexibility and reusability. Your apps will be completely responsive, ensuring they’ll look stunning and function flawlessly on desktops, tablets and mobile devices.</p>
											<p class="mb-5">The Duralux Admin template is a responsive web application built with Boostrap latest. It includes highly customizable UI kit, Components, Widgets, Modules, Charts and Applications for you to design interfaces and powerful web applications. The well structured code will allow easy customization and helps to build a modern web application with great speed.</p>
											<h6 class="tx-bold tx-dark mg-t-30 mg-b-10">Highlights:</h6>
											<ul class="font-size-14">
												<li>Fully Responsive Design</li>
												<li>Clean and Valid Code</li>
												<li>Built with bootstrap latest.x.x</li>
												<li>Multiple navigation layouts</li>
												<li>Lightweight and Super Fast</li>
												<li>Easy customizations</li>
												<li>Developer friendly code</li>
												<li>Cross Browser Compatibility</li>
												<li>Lifetime Free Updates</li>
											</ul>
											<h6 class="tx-bold tx-dark mg-t-30 mg-b-10">Major Dependencies:</h6>
											<ul>
												<li><a href="https://getbootstrap.com/" target="_blank">Bootstrap</a></li>
												<li><a href="http://sass-lang.com/" target="_blank">SASS</a></li>
												<li><a href="http://gulpjs.com/" target="_blank">Gulp</a></li>
												<li><a href="https://jquery.com/" target="_blank">jQuery</a></li>
												<li><a href="https://npmjs.com/" target="_blank">NPM</a></li>
												<li><a href="https://code.visualstudio.com/" target="_blank">VS Code</a></li>
											</ul>
											<h6 class="tx-bold tx-dark mg-t-30 mg-b-10">Support does include:</h6>
											<ul>
												<li>Answering your questions or problems regarding the template.</li>
												<li>Giving solution to the Bugs reported.</li>
											</ul>
											<h6 class="tx-bold tx-dark mg-t-30 mg-b-10">Support does not include:</h6>
											<ul>
												<li>Custmaization Work</li>
												<li>Any Installation Work</li>
												<li>Support for any Third Party Plugins / Software</li>
												<li>Solve bug in your implemented template</li>
												<li>Support or Guide for How to integrate with any technologies (like, PHP, .net, Java etc)</li>
											</ul>
										</div>
									</div>
								</section>
								<!-- ========== End Introduction ========== -->
								<!-- ========== Start Features ========== -->
								<section id="features">
									<div class="card mb-4">
										<div class="card-header">
											<h5 class="tx-bold tx-dark mb-0">Features</h5>
										</div>
										<div class="card-body">
											<p class="mb-5">Create your web application amazing and more professional with this super user-friendly dashboard template. Duralux comes with all essential features that always you or your developers looking for. Build premium quality applications with mortification free UI!</p>
											<div class="row mg-b-30">
												<div class="col-lg-4">
													<!-- Card -->
													<div class="card single-featured mb-4">
														<div class="card-image mb-4">
															<i class="bi bi-code fs-1"></i>
														</div>
														<div class="body">
															<h5 class="card-title">Quality & Clean Code</h5>
															<p>Beautifully crafted, clean & minimal designed admin theme with different demos</p>
														</div>
													</div>
													<!-- end of card -->
												</div>
												<!-- end of col -->
												<div class="col-lg-4">
													<!-- Card -->
													<div class="card single-featured mb-4">
														<div class="card-image mb-4">
															<i class="bi bi-laptop fs-1"></i>
														</div>
														<div class="body">
															<h5 class="card-title">Fully Responsive</h5>
															<p>Fully responsive with all devices layout using bootstrap's latest version.</p>
														</div>
													</div>
													<!-- end of card -->
												</div>
												<!-- end of col -->
												<div class="col-lg-4">
													<!-- Card -->
													<div class="card single-featured mb-4">
														<div class="card-image mb-4">
															<i class="bi bi-bootstrap fs-1"></i>
														</div>
														<div class="body">
															<h5 class="card-title">Built with Bootstrap</h5>
															<p>Duralux has the pure Bootstrap native look and feels with responsive design.</p>
														</div>
													</div>
													<!-- end of card -->
												</div>
												<!-- end of col -->
												<div class="col-lg-4">
													<!-- Card -->
													<div class="card single-featured mb-4">
														<div class="card-image mb-4">
															<i class="bi bi-layout-sidebar fs-1"></i>
														</div>
														<div class="body">
															<h5 class="card-title">Multiple Layouts</h5>
															<p>Ready to use theme and content layouts for building apps and pages.</p>
														</div>
													</div>
													<!-- end of card -->
												</div>
												<!-- end of col -->
												<div class="col-lg-4">
													<!-- Card -->
													<div class="card single-featured mb-4">
														<div class="card-image mb-4">
															<i class="bi bi-lamp fs-1"></i>
														</div>
														<div class="body">
															<h5 class="card-title">Limitless Components</h5>
															<p>A huge collection of components to power your application with the latest UI/UX trends</p>
														</div>
													</div>
													<!-- end of card -->
												</div>
												<!-- end of col -->
												<div class="col-lg-4">
													<!-- Card -->
													<div class="card single-featured mb-4">
														<div class="card-image mb-4">
															<i class="bi bi-layout-text-window fs-1"></i>
														</div>
														<div class="body">
															<h5 class="card-title">Flexible Structure</h5>
															<p>Ability to set different template layouts for different template bundles.</p>
														</div>
													</div>
													<!-- end of card -->
												</div>
												<!-- end of col -->
												<div class="col-lg-4">
													<!-- Card -->
													<div class="card single-featured mb-4">
														<div class="card-image mb-4">
															<i class="bi bi-trophy fs-1"></i>
														</div>
														<div class="body">
															<h5 class="card-title">Rich Components</h5>
															<p>Built-in apps and ready to use pages to cut the development time.</p>
														</div>
													</div>
													<!-- end of card -->
												</div>
												<!-- end of col -->
												<div class="col-lg-4">
													<!-- Card -->
													<div class="card single-featured mb-4">
														<div class="card-image mb-4">
															<i class="bi bi-window-stack fs-1"></i>
														</div>
														<div class="body">
															<h5 class="card-title">Lots of Widgets</h5>
															<p>Wide range of Widgets are available with Duralux admin dashboard package.</p>
														</div>
													</div>
													<!-- end of card -->
												</div>
												<!-- end of col -->
												<div class="col-lg-4">
													<!-- Card -->
													<div class="card single-featured mb-4">
														<div class="card-image mb-4">
															<i class="bi bi-gear-wide fs-1"></i>
														</div>
														<div class="body">
															<h5 class="card-title">Easy to Customize</h5>
															<p>Customization will be easy as we understand your pain and kick start your dream projects.</p>
														</div>
													</div>
													<!-- end of card -->
												</div>
												<!-- end of col -->
												<div class="col-lg-4">
													<!-- Card -->
													<div class="card single-featured mb-4">
														<div class="card-image mb-4">
															<i class="bi bi-menu-down fs-1"></i>
														</div>
														<div class="body">
															<h5 class="card-title">Multiple Menus</h5>
															<p>Insert any element and create your unique multiple menu with unique styles.</p>
														</div>
													</div>
													<!-- end of card -->
												</div>
												<!-- end of col -->
												<div class="col-lg-4">
													<!-- Card -->
													<div class="card single-featured mb-4">
														<div class="card-image mb-4">
															<i class="bi bi-speedometer fs-1"></i>
														</div>
														<div class="body">
															<h5 class="card-title">SEO Optimized</h5>
															<p>We provide the best design and coding for best SEO practices to achieve higher rankings.</p>
														</div>
													</div>
													<!-- end of card -->
												</div>
												<!-- end of col -->
												<div class="col-lg-4">
													<!-- Card -->
													<div class="card single-featured mb-4">
														<div class="card-image mb-4">
															<i class="bi bi-box fs-1"></i>
														</div>
														<div class="body">
															<h5 class="card-title">Wide / Boxed Layouts</h5>
															<p>Choose between boxed and wide and wide layout to customize the layout of your website.</p>
														</div>
													</div>
													<!-- end of card -->
												</div>
												<!-- end of col -->
												<div class="col-lg-4">
													<!-- Card -->
													<div class="card single-featured mb-4">
														<div class="card-image mb-4">
															<i class="bi bi-ubuntu fs-1"></i>
														</div>
														<div class="body">
															<h5 class="card-title">Exclusive Plugins Collection</h5>
															<p>Duralux comes with lot of exclusive plugins collection for all modern design</p>
														</div>
													</div>
													<!-- end of card -->
												</div>
												<!-- end of col -->
												<div class="col-lg-4">
													<!-- Card -->
													<div class="card single-featured mb-4">
														<div class="card-image mb-4">
															<i class="bi bi-book fs-1"></i>
														</div>
														<div class="body">
															<h5 class="card-title">Detailed Documentation</h5>
															<p>We have made detailed documentation, so it will easy to use and customizable for all users.</p>
														</div>
													</div>
													<!-- end of card -->
												</div>
												<!-- end of col -->
												<div class="col-lg-4">
													<!-- Card -->
													<div class="card single-featured mb-4">
														<div class="card-image mb-4">
															<i class="bi bi-sun fs-1"></i>
														</div>
														<div class="body">
															<h5 class="card-title">Lifetime Fee Updates</h5>
															<p>We are constantly updating our pack with new features, design and technology's.</p>
														</div>
													</div>
													<!-- end of card -->
												</div>
												<!-- end of col -->
											</div>
										</div>
									</div>
								</section>
								<!-- ========== End Features ========== -->
								<!-- ========== Start Compatibility ========== -->
								<section id="compatibility">
									<div class="card mb-4">
										<div class="card-header">
											<h5 class="tx-bold tx-dark mb-0">Technology &amp; Compatibility</h5>
										</div>
										<div class="card-body">
											<p class="mb-5">The term browser compatibility refers to the ability of a certain website to appear fully functional on different browsers that are available in the market. This means that the website's HTML coding, as well as the scripts on that website, should be compatible to run on the browsers.</p>
											<h6 class="tx-bold tx-dark mg-t-30 mb-4">Compatibility:</h6>
											<div class="hstack gap-2 mb-5">
												<div class="wd-50 ht-50 hstack aling-items-center justify-content-center p-3 border bd-gray-2 rounded-2" data-bs-toggle="tooltip" data-bs-trigger="hover" title="Chrome">
													<i class="tx-20 fa-brands fa-chrome"></i>
												</div>
												<div class="wd-50 ht-50 hstack aling-items-center justify-content-center p-3 border bd-gray-2 rounded-2" data-bs-toggle="tooltip" data-bs-trigger="hover" title="Firefox">
													<i class="tx-20 fa-brands fa-firefox-browser"></i>
												</div>
												<div class="wd-50 ht-50 hstack aling-items-center justify-content-center p-3 border bd-gray-2 rounded-2" data-bs-toggle="tooltip" data-bs-trigger="hover" title="EDGE">
													<i class="tx-20 fa-brands fa-edge"></i>
												</div>
												<div class="wd-50 ht-50 hstack aling-items-center justify-content-center p-3 border bd-gray-2 rounded-2" data-bs-toggle="tooltip" data-bs-trigger="hover" title="Safari">
													<i class="tx-20 fa-brands fa-safari"></i>
												</div>
												<div class="wd-50 ht-50 hstack aling-items-center justify-content-center p-3 border bd-gray-2 rounded-2" data-bs-toggle="tooltip" data-bs-trigger="hover" title="Opera">
													<i class="tx-20 fa-brands fa-opera"></i>
												</div>
												<div class="wd-50 ht-50 hstack aling-items-center justify-content-center p-3 border bd-gray-2 rounded-2" data-bs-toggle="tooltip" data-bs-trigger="hover" title="EDGE-Legacy">
													<i class="tx-20 fa-brands fa-edge-legacy"></i>
												</div>
											</div>
											<h6 class="tx-bold tx-dark mg-t-30 mb-4">Technology:</h6>
											<div class="hstack gap-2 mb-5">
												<div class="wd-50 ht-50 hstack aling-items-center justify-content-center p-3 border bd-gray-2 rounded-2" data-bs-toggle="tooltip" data-bs-trigger="hover" title="HTML5">
													<i class="tx-20 fa-brands fa-html5"></i>
												</div>
												<div class="wd-50 ht-50 hstack aling-items-center justify-content-center p-3 border bd-gray-2 rounded-2" data-bs-toggle="tooltip" data-bs-trigger="hover" title="CSS3">
													<i class="tx-20 fa-brands fa-css3-alt"></i>
												</div>
												<div class="wd-50 ht-50 hstack aling-items-center justify-content-center p-3 border bd-gray-2 rounded-2" data-bs-toggle="tooltip" data-bs-trigger="hover" title="Bootstrap">
													<i class="tx-20 fa-brands fa-bootstrap"></i>
												</div>
												<div class="wd-50 ht-50 hstack aling-items-center justify-content-center p-3 border bd-gray-2 rounded-2" data-bs-toggle="tooltip" data-bs-trigger="hover" title="SASS">
													<i class="tx-20 fa-brands fa-sass"></i>
												</div>
												<div class="wd-50 ht-50 hstack aling-items-center justify-content-center p-3 border bd-gray-2 rounded-2" data-bs-toggle="tooltip" data-bs-trigger="hover" title="jQuery">
													<i class="tx-20 fa-brands fa-node-js"></i>
												</div>
												<div class="wd-50 ht-50 hstack aling-items-center justify-content-center p-3 border bd-gray-2 rounded-2" data-bs-toggle="tooltip" data-bs-trigger="hover" title="Gulp">
													<i class="tx-20 fa-brands fa-gulp"></i>
												</div>
												<div class="wd-50 ht-50 hstack aling-items-center justify-content-center p-3 border bd-gray-2 rounded-2" data-bs-toggle="tooltip" data-bs-trigger="hover" title="NPM">
													<i class="tx-20 fa-brands fa-npm"></i>
												</div>
												<div class="wd-50 ht-50 hstack aling-items-center justify-content-center p-3 border bd-gray-2 rounded-2" data-bs-toggle="tooltip" data-bs-trigger="hover" title="Nodejs">
													<i class="tx-20 fa-brands fa-node"></i>
												</div>
												<div class="wd-50 ht-50 hstack aling-items-center justify-content-center p-3 border bd-gray-2 rounded-2" data-bs-toggle="tooltip" data-bs-trigger="hover" title="W3C Validate">
													<i class="fa-regular fa-font-awesome"></i>
												</div>
											</div>
											<h6 class="tx-bold tx-dark mg-t-30 mb-4">Upcomming:</h6>
											<div class="hstack gap-2 mb-5">
												<div class="wd-50 ht-50 hstack aling-items-center justify-content-center p-3 border bd-gray-2 rounded-2" data-bs-toggle="tooltip" data-bs-trigger="hover" title="Comming Soon">
													<i class="tx-20 fa-brands fa-laravel"></i>
												</div>
												<div class="wd-50 ht-50 hstack aling-items-center justify-content-center p-3 border bd-gray-2 rounded-2" data-bs-toggle="tooltip" data-bs-trigger="hover" title="Comming Soon">
													<i class="tx-20 fa-brands fa-react"></i>
												</div>
												<div class="wd-50 ht-50 hstack aling-items-center justify-content-center p-3 border bd-gray-2 rounded-2" data-bs-toggle="tooltip" data-bs-trigger="hover" title="Comming Soon">
													<i class="tx-20 fa-brands fa-vuejs"></i>
												</div>
												<div class="wd-50 ht-50 hstack aling-items-center justify-content-center p-3 border bd-gray-2 rounded-2" data-bs-toggle="tooltip" data-bs-trigger="hover" title="Comming Soon">
													<i class="tx-20 fa-brands fa-angular"></i>
												</div>
												<div class="wd-50 ht-50 hstack aling-items-center justify-content-center p-3 border bd-gray-2 rounded-2" data-bs-toggle="tooltip" data-bs-trigger="hover" title="Comming Soon">
													<i class="tx-20 fa-brands fa-free-code-camp"></i>
												</div>
											</div>
										</div>
									</div>
								</section>
								<!-- ========== End Compatibility ========== -->
								<!-- ========== Start License ========== -->
								<section id="license">
									<div class="card mb-4">
										<div class="card-header">
											<h5 class="tx-bold tx-dark mb-0">License</h5>
										</div>
										<div class="card-body">
											<p>Use of an item is bound by the license you purchase. A license grants you a non-exclusive and non-transferable right to use and incorporate the item in your personal or commercial projects. There are several licenses available:</p>
											<p><strong class="tx-dark">Single License</strong> Use of an item is bound by the license you purchase. A license grants you a non-exclusive and non-transferable right to use and incorporate the item in your personal or commercial projects. There are several licenses available:</p>
											<p><strong class="tx-dark">Multiple License</strong> Use of an item is bound by the license you purchase. A license grants you a non-exclusive and non-transferable right to use and incorporate the item in your personal or commercial projects. There are several licenses available:</p>
											<p><strong class="tx-dark">Extended License</strong> Use of an item is bound by the license you purchase. A license grants you a non-exclusive and non-transferable right to use and incorporate the item in your personal or commercial projects. There are several licenses available:</p>
										</div>
									</div>
								</section>
								<!-- ========== End License ========== -->
								<hr class="bd-gray-400 mg-y-50" />
								<!-- ========== Start Quick Navigation ========== -->
								<div class="d-flex gap-5 justify-content-between text-justify">
									<a href="./../documentation.html" class="p-5 bg-gray-100 bd bd-gray-2 rounded mb-4 tx-12 tx-dark fw-bold d-block w-100" style="background: #f3f3f5"> &larr; Back Home</a>
									<a href="installation.html" class="p-5 bg-gray-100 bd bd-gray-2 rounded mb-4 tx-12 tx-dark fw-bold d-block w-100 text-end" style="background: #f3f3f5">Installation &rarr;</a>
								</div>
								<!-- ========== End Quick Navigation ========== -->
							</div>
							<!-- ========== Start Footer ========== -->
							<footer class="copyright">
								<p class="tx-12 tx-spacing-1">
									<span>Copyright &copy;</span>
									<script>
										document.write(new Date().getFullYear());
									</script>
									<span class="tx-gray-300 mx-2">|</span>
									<span>By: <a href="https://themeforest.net/user/theme_ocean">theme_ocean</a></span>
								</p>
							</footer>
							<!-- ========== End Footer ========== -->
						</main>
						<!-- ========== End Main Wrapper ========== -->
					</div>
				</div>
				<!-- ========== End Main Container ========== -->
			</div>
		</div>
		<!-- ========== Start Footer Script ========== -->
		<script src="./../assets/js/jquery.min.js"></script>
		<script src="./../assets/js/bootstrap.min.js"></script>
		<script src="./../assets/js/highlight.pack.js"></script>
		<script src="./../assets/js/perfectScroll.js"></script>
		<script src="./../assets/plugins/jstree/jstree.min.js"></script>
		<script src="./../assets/js/script.js"></script>
		<!-- ========== End Footer Script ========== -->
	</body>
</html>
