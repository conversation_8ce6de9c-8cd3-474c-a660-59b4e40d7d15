using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using ColorOasisSystemWeb.Enums;

namespace ColorOasisSystemWeb.Models.Purchasing
{
    public class Supplier
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        [Display(Name = "Supplier Code")]
        [MaxLength(20)]
        public string Code { get; set; }
        
        [Required]
        [Display(Name = "Supplier Name")]
        [MaxLength(100)]
        public string Name { get; set; }
        
        [Display(Name = "Name (English)")]
        [MaxLength(100)]
        public string NameEn { get; set; }
        
        [Display(Name = "Contact Person")]
        [MaxLength(100)]
        public string ContactPerson { get; set; }
        
        [Required]
        [EmailAddress]
        [Display(Name = "Email")]
        [MaxLength(100)]
        public string Email { get; set; }
        
        [Display(Name = "Phone")]
        [MaxLength(20)]
        public string Phone { get; set; }
        
        [Display(Name = "Mobile")]
        [MaxLength(20)]
        public string Mobile { get; set; }
        
        [Display(Name = "Fax")]
        [MaxLength(20)]
        public string Fax { get; set; }
        
        [Display(Name = "Website")]
        [Url]
        [MaxLength(100)]
        public string Website { get; set; }
        
        [Display(Name = "Address")]
        [MaxLength(200)]
        public string Address { get; set; }
        
        [Display(Name = "City")]
        [MaxLength(50)]
        public string City { get; set; }
        
        [Display(Name = "Country")]
        [MaxLength(50)]
        public string Country { get; set; }
        
        [Display(Name = "Postal Code")]
        [MaxLength(20)]
        public string PostalCode { get; set; }
        
        [Display(Name = "Tax Registration Number")]
        [MaxLength(50)]
        public string TaxRegistrationNumber { get; set; }
        
        [Display(Name = "Commercial Registration")]
        [MaxLength(50)]
        public string CommercialRegistration { get; set; }
        
        [Display(Name = "Bank Name")]
        [MaxLength(100)]
        public string BankName { get; set; }
        
        [Display(Name = "Bank Account")]
        [MaxLength(50)]
        public string BankAccount { get; set; }
        
        [Display(Name = "IBAN")]
        [MaxLength(50)]
        public string IBAN { get; set; }
        
        [Display(Name = "SWIFT Code")]
        [MaxLength(20)]
        public string SwiftCode { get; set; }
        
        [Display(Name = "Supplier Status")]
        public SupplierStatus Status { get; set; } = SupplierStatus.Active;
        
        [Display(Name = "Supplier Type")]
        public SupplierType Type { get; set; } = SupplierType.Vendor;
        
        [Display(Name = "Payment Terms (Days)")]
        public int PaymentTermsDays { get; set; } = 30;
        
        [Display(Name = "Credit Limit")]
        [NumericOnly]
        public decimal CreditLimit { get; set; } = 0;
        
        [Display(Name = "Current Balance")]
        [NumericOnly]
        public decimal CurrentBalance { get; set; } = 0;
        
        [Display(Name = "Performance Score")]
        [Range(0, 100)]
        public decimal PerformanceScore { get; set; } = 0;
        
        [Display(Name = "Quality Rating")]
        [Range(1, 5)]
        public int QualityRating { get; set; } = 3;
        
        [Display(Name = "Delivery Rating")]
        [Range(1, 5)]
        public int DeliveryRating { get; set; } = 3;
        
        [Display(Name = "Service Rating")]
        [Range(1, 5)]
        public int ServiceRating { get; set; } = 3;
        
        [Display(Name = "Lead Time (Days)")]
        public int LeadTimeDays { get; set; } = 7;
        
        [Display(Name = "Minimum Order Amount")]
        [NumericOnly]
        public decimal MinimumOrderAmount { get; set; } = 0;
        
        [Display(Name = "Currency")]
        [MaxLength(3)]
        public string Currency { get; set; } = "AED";
        
        [Display(Name = "Notes")]
        [MaxLength(1000)]
        public string Notes { get; set; }
        
        [Display(Name = "Created Date")]
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
        
        [Display(Name = "Created By")]
        public string CreatedBy { get; set; }
        
        [Display(Name = "Last Modified Date")]
        public DateTime LastModifiedDate { get; set; } = DateTime.UtcNow;
        
        [Display(Name = "Last Modified By")]
        public string LastModifiedBy { get; set; }
        
        [Display(Name = "Is Active")]
        public bool IsActive { get; set; } = true;
        
        [Display(Name = "Is Deleted")]
        public bool IsDeleted { get; set; } = false;
        
        // Navigation Properties
        public virtual ICollection<SupplierContract> Contracts { get; set; } = new List<SupplierContract>();
        public virtual ICollection<PurchaseOrder> PurchaseOrders { get; set; } = new List<PurchaseOrder>();
        public virtual ICollection<SupplierEvaluation> Evaluations { get; set; } = new List<SupplierEvaluation>();
        public virtual ICollection<SupplierContact> Contacts { get; set; } = new List<SupplierContact>();
    }

    public class SupplierContract
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        [ForeignKey("Supplier")]
        public int SupplierId { get; set; }
        public virtual Supplier Supplier { get; set; }
        
        [Required]
        [Display(Name = "Contract Number")]
        [MaxLength(50)]
        public string ContractNumber { get; set; }
        
        [Required]
        [Display(Name = "Contract Title")]
        [MaxLength(200)]
        public string Title { get; set; }
        
        [Display(Name = "Description")]
        [MaxLength(1000)]
        public string Description { get; set; }
        
        [Display(Name = "Start Date")]
        public DateTime StartDate { get; set; }
        
        [Display(Name = "End Date")]
        public DateTime EndDate { get; set; }
        
        [Display(Name = "Contract Value")]
        [NumericOnly]
        public decimal ContractValue { get; set; } = 0;
        
        [Display(Name = "Currency")]
        [MaxLength(3)]
        public string Currency { get; set; } = "AED";
        
        [Display(Name = "Payment Terms")]
        [MaxLength(200)]
        public string PaymentTerms { get; set; }
        
        [Display(Name = "Delivery Terms")]
        [MaxLength(200)]
        public string DeliveryTerms { get; set; }
        
        [Display(Name = "Contract Status")]
        public ContractStatus Status { get; set; } = ContractStatus.Draft;
        
        [Display(Name = "Auto Renewal")]
        public bool AutoRenewal { get; set; } = false;
        
        [Display(Name = "Renewal Period (Months)")]
        public int RenewalPeriodMonths { get; set; } = 12;
        
        [Display(Name = "Contract File Path")]
        [MaxLength(500)]
        public string ContractFilePath { get; set; }
        
        [Display(Name = "Signed Date")]
        public DateTime? SignedDate { get; set; }
        
        [Display(Name = "Signed By Supplier")]
        [MaxLength(100)]
        public string SignedBySupplier { get; set; }
        
        [Display(Name = "Signed By Company")]
        [MaxLength(100)]
        public string SignedByCompany { get; set; }
        
        [Display(Name = "Created Date")]
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
        
        [Display(Name = "Created By")]
        public string CreatedBy { get; set; }
        
        [Display(Name = "Is Active")]
        public bool IsActive { get; set; } = true;
    }

    public class SupplierContact
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        [ForeignKey("Supplier")]
        public int SupplierId { get; set; }
        public virtual Supplier Supplier { get; set; }
        
        [Required]
        [Display(Name = "Contact Name")]
        [MaxLength(100)]
        public string Name { get; set; }
        
        [Display(Name = "Job Title")]
        [MaxLength(100)]
        public string JobTitle { get; set; }
        
        [Display(Name = "Department")]
        [MaxLength(100)]
        public string Department { get; set; }
        
        [EmailAddress]
        [Display(Name = "Email")]
        [MaxLength(100)]
        public string Email { get; set; }
        
        [Display(Name = "Phone")]
        [MaxLength(20)]
        public string Phone { get; set; }
        
        [Display(Name = "Mobile")]
        [MaxLength(20)]
        public string Mobile { get; set; }
        
        [Display(Name = "Is Primary Contact")]
        public bool IsPrimaryContact { get; set; } = false;
        
        [Display(Name = "Is Active")]
        public bool IsActive { get; set; } = true;
        
        [Display(Name = "Notes")]
        [MaxLength(500)]
        public string Notes { get; set; }
    }

    public class SupplierEvaluation
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        [ForeignKey("Supplier")]
        public int SupplierId { get; set; }
        public virtual Supplier Supplier { get; set; }
        
        [Display(Name = "Evaluation Date")]
        public DateTime EvaluationDate { get; set; } = DateTime.UtcNow;
        
        [Display(Name = "Evaluation Period From")]
        public DateTime PeriodFrom { get; set; }
        
        [Display(Name = "Evaluation Period To")]
        public DateTime PeriodTo { get; set; }
        
        [Display(Name = "Quality Score")]
        [Range(1, 100)]
        public int QualityScore { get; set; }
        
        [Display(Name = "Delivery Score")]
        [Range(1, 100)]
        public int DeliveryScore { get; set; }
        
        [Display(Name = "Service Score")]
        [Range(1, 100)]
        public int ServiceScore { get; set; }
        
        [Display(Name = "Price Competitiveness")]
        [Range(1, 100)]
        public int PriceScore { get; set; }
        
        [Display(Name = "Overall Score")]
        [Range(1, 100)]
        public int OverallScore { get; set; }
        
        [Display(Name = "Comments")]
        [MaxLength(1000)]
        public string Comments { get; set; }
        
        [Display(Name = "Recommendations")]
        [MaxLength(1000)]
        public string Recommendations { get; set; }
        
        [Display(Name = "Evaluated By")]
        public string EvaluatedBy { get; set; }
        
        [Display(Name = "Approved By")]
        public string ApprovedBy { get; set; }
        
        [Display(Name = "Is Approved")]
        public bool IsApproved { get; set; } = false;
        
        [Display(Name = "Approval Date")]
        public DateTime? ApprovalDate { get; set; }
    }
}
