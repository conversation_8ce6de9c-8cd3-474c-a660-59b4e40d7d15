@model ColorOasisSystemWeb.Models.Accounting.Invoice

@{
    ViewData["Title"] = "Invoice Details";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="nxl-content">
    <!-- [ page-header ] start -->
    <div class="page-header">
        <div class="page-header-left d-flex align-items-center">
            <div class="page-header-title">
                <h5 class="m-b-10">Invoice Details</h5>
            </div>
            <ul class="breadcrumb">
                <li class="breadcrumb-item"><a href="@Url.Action("Index", "Home")">Home</a></li>
                <li class="breadcrumb-item">ERP</li>
                <li class="breadcrumb-item">Accounting</li>
                <li class="breadcrumb-item"><a href="@Url.Action("Index")">Invoices</a></li>
                <li class="breadcrumb-item">Details</li>
            </ul>
        </div>
        <div class="page-header-right ms-auto">
            <div class="page-header-right-items">
                <div class="d-flex align-items-center gap-2 page-header-right-items-wrapper">
                    @if (Model.Status == ColorOasisSystemWeb.Enums.InvoiceStatus.Draft)
                    {
                        <a href="@Url.Action("Edit", new { id = Model.Id })" class="btn btn-primary">
                            <i class="feather-edit-3 me-2"></i>
                            <span>Edit Invoice</span>
                        </a>
                    }
                    <div class="dropdown">
                        <a href="javascript:void(0);" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="feather-more-horizontal me-2"></i>Actions
                        </a>
                        <div class="dropdown-menu dropdown-menu-end">
                            <a class="dropdown-item" href="#">
                                <i class="feather-printer me-3"></i>Print Invoice
                            </a>
                            <a class="dropdown-item" href="#">
                                <i class="feather-download me-3"></i>Export PDF
                            </a>
                            <a class="dropdown-item" href="#">
                                <i class="feather-mail me-3"></i>Email Invoice
                            </a>
                            <div class="dropdown-divider"></div>
                            @if (Model.Status == ColorOasisSystemWeb.Enums.InvoiceStatus.Draft)
                            {
                                <a class="dropdown-item" href="#">
                                    <i class="feather-send me-3"></i>Send Invoice
                                </a>
                            }
                            @if (Model.Status != ColorOasisSystemWeb.Enums.InvoiceStatus.Paid && Model.Status != ColorOasisSystemWeb.Enums.InvoiceStatus.Cancelled)
                            {
                                <a class="dropdown-item" href="#">
                                    <i class="feather-dollar-sign me-3"></i>Record Payment
                                </a>
                            }
                        </div>
                    </div>
                    <a href="@Url.Action("Index")" class="btn btn-outline-secondary">
                        <i class="feather-arrow-left me-2"></i>
                        <span>Back to List</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
    <!-- [ page-header ] end -->

    <!-- [ Main Content ] start -->
    <div class="main-content">
        <div class="row">
            <!-- Invoice Header -->
            <div class="col-lg-8">
                <div class="card stretch stretch-full">
                    <div class="card-header">
                        <h5 class="card-title">Invoice Information</h5>
                        <div class="card-header-action">
                            @switch (Model.Status)
                            {
                                case ColorOasisSystemWeb.Enums.InvoiceStatus.Draft:
                                    <span class="badge bg-soft-secondary text-secondary">Draft</span>
                                    break;
                                case ColorOasisSystemWeb.Enums.InvoiceStatus.Sent:
                                    <span class="badge bg-soft-primary text-primary">Sent</span>
                                    break;
                                case ColorOasisSystemWeb.Enums.InvoiceStatus.Viewed:
                                    <span class="badge bg-soft-info text-info">Viewed</span>
                                    break;
                                case ColorOasisSystemWeb.Enums.InvoiceStatus.PartiallyPaid:
                                    <span class="badge bg-soft-warning text-warning">Partially Paid</span>
                                    break;
                                case ColorOasisSystemWeb.Enums.InvoiceStatus.Paid:
                                    <span class="badge bg-soft-success text-success">Paid</span>
                                    break;
                                case ColorOasisSystemWeb.Enums.InvoiceStatus.Overdue:
                                    <span class="badge bg-soft-danger text-danger">Overdue</span>
                                    break;
                                case ColorOasisSystemWeb.Enums.InvoiceStatus.Cancelled:
                                    <span class="badge bg-soft-dark text-dark">Cancelled</span>
                                    break;
                                case ColorOasisSystemWeb.Enums.InvoiceStatus.Refunded:
                                    <span class="badge bg-soft-info text-info">Refunded</span>
                                    break;
                            }
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Invoice Number</label>
                                    <div class="fw-bold">@Model.InvoiceNumber</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Invoice Date</label>
                                    <div>@Model.InvoiceDate.ToString("MMM dd, yyyy")</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Customer</label>
                                    <div class="fw-bold">@Model.Client?.Name</div>
                                    @if (!string.IsNullOrEmpty(Model.Client?.Email))
                                    {
                                        <div class="text-muted">@Model.Client.Email</div>
                                    }
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Due Date</label>
                                    <div>
                                        @Model.DueDate.ToString("MMM dd, yyyy")
                                        @if (Model.DueDate < DateTime.Now && Model.Status != ColorOasisSystemWeb.Enums.InvoiceStatus.Paid)
                                        {
                                            <span class="badge bg-soft-danger text-danger ms-2">Overdue</span>
                                        }
                                    </div>
                                </div>
                            </div>
                            @if (Model.SalesOrderId.HasValue)
                            {
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label text-muted">Sales Order</label>
                                        <div>
                                            <a href="@Url.Action("Details", "SalesOrders", new { id = Model.SalesOrderId })">
                                                @Model.SalesOrder?.OrderNumber
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            }
                            @if (!string.IsNullOrEmpty(Model.PONumber))
                            {
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label text-muted">PO Number</label>
                                        <div>@Model.PONumber</div>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                </div>

                <!-- Invoice Items -->
                <div class="card stretch stretch-full">
                    <div class="card-header">
                        <h5 class="card-title">Invoice Items</h5>
                    </div>
                    <div class="card-body">
                        @if (Model.Items?.Any() == true)
                        {
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Description</th>
                                            <th>Quantity</th>
                                            <th>Unit Price</th>
                                            <th>Discount</th>
                                            <th>Total</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var item in Model.Items)
                                        {
                                            <tr>
                                                <td>
                                                    <div class="fw-bold">@item.Description</div>
                                                    @if (!string.IsNullOrEmpty(item.Notes))
                                                    {
                                                        <small class="text-muted">@item.Notes</small>
                                                    }
                                                </td>
                                                <td>@item.Quantity @item.UnitOfMeasure</td>
                                                <td>@item.UnitPrice.ToString("C")</td>
                                                <td>
                                                    @if (item.DiscountAmount > 0)
                                                    {
                                                        <span class="text-success">-@item.DiscountAmount.ToString("C")</span>
                                                    }
                                                    else
                                                    {
                                                        <span class="text-muted">-</span>
                                                    }
                                                </td>
                                                <td class="fw-bold">@item.LineTotal.ToString("C")</td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                        }
                        else
                        {
                            <div class="text-center py-4">
                                <i class="feather-file-text" style="font-size: 3rem; color: #6c757d;"></i>
                                <h6 class="mt-3 text-muted">No items in this invoice</h6>
                            </div>
                        }
                    </div>
                </div>
            </div>

            <!-- Invoice Summary & Payment Info -->
            <div class="col-lg-4">
                <div class="card stretch stretch-full">
                    <div class="card-header">
                        <h5 class="card-title">Invoice Summary</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between mb-2">
                            <span>Subtotal:</span>
                            <span>@Model.Subtotal.ToString("C")</span>
                        </div>
                        @if (Model.DiscountAmount > 0)
                        {
                            <div class="d-flex justify-content-between mb-2 text-success">
                                <span>Discount:</span>
                                <span>-@Model.DiscountAmount.ToString("C")</span>
                            </div>
                        }
                        <div class="d-flex justify-content-between mb-2">
                            <span>Tax (@Model.TaxRate%):</span>
                            <span>@Model.TaxAmount.ToString("C")</span>
                        </div>
                        <hr>
                        <div class="d-flex justify-content-between fw-bold fs-5">
                            <span>Total:</span>
                            <span class="text-primary">@Model.TotalAmount.ToString("C")</span>
                        </div>
                        <div class="text-muted mt-2">
                            Currency: @Model.Currency
                        </div>
                    </div>
                </div>

                <!-- Payment Information -->
                <div class="card stretch stretch-full">
                    <div class="card-header">
                        <h5 class="card-title">Payment Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label text-muted">Amount Paid</label>
                            <div class="fw-bold text-success fs-5">@Model.AmountPaid.ToString("C")</div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-muted">Balance Due</label>
                            @{
                                var balanceDue = Model.TotalAmount - Model.AmountPaid;
                                var balanceClass = balanceDue > 0 ? "text-danger" : "text-success";
                            }
                            <div class="fw-bold @balanceClass fs-5">@balanceDue.ToString("C")</div>
                        </div>
                        @if (!string.IsNullOrEmpty(Model.PaymentTerms))
                        {
                            <div class="mb-3">
                                <label class="form-label text-muted">Payment Terms</label>
                                <div>@Model.PaymentTerms</div>
                            </div>
                        }
                        @if (Model.LastPaymentDate.HasValue)
                        {
                            <div class="mb-3">
                                <label class="form-label text-muted">Last Payment</label>
                                <div>@Model.LastPaymentDate.Value.ToString("MMM dd, yyyy")</div>
                            </div>
                        }
                    </div>
                </div>

                <!-- Payment History -->
                @if (Model.Payments?.Any() == true)
                {
                    <div class="card stretch stretch-full">
                        <div class="card-header">
                            <h5 class="card-title">Payment History</h5>
                        </div>
                        <div class="card-body">
                            @foreach (var payment in Model.Payments.OrderByDescending(p => p.PaymentDate))
                            {
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <div>
                                        <div class="fw-bold">@payment.PaymentAmount.ToString("C")</div>
                                        <small class="text-muted">@payment.PaymentDate.ToString("MMM dd, yyyy")</small>
                                    </div>
                                    <div>
                                        @switch (payment.Status)
                                        {
                                            case ColorOasisSystemWeb.Enums.PaymentStatus.Completed:
                                                <span class="badge bg-soft-success text-success">Completed</span>
                                                break;
                                            case ColorOasisSystemWeb.Enums.PaymentStatus.Pending:
                                                <span class="badge bg-soft-warning text-warning">Pending</span>
                                                break;
                                            case ColorOasisSystemWeb.Enums.PaymentStatus.Failed:
                                                <span class="badge bg-soft-danger text-danger">Failed</span>
                                                break;
                                        }
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                }

                <!-- Invoice Timeline -->
                <div class="card stretch stretch-full">
                    <div class="card-header">
                        <h5 class="card-title">Invoice Timeline</h5>
                    </div>
                    <div class="card-body">
                        <div class="timeline">
                            <div class="timeline-item">
                                <div class="timeline-marker bg-success"></div>
                                <div class="timeline-content">
                                    <h6 class="timeline-title">Invoice Created</h6>
                                    <p class="timeline-text">@Model.InvoiceDate.ToString("MMM dd, yyyy HH:mm")</p>
                                </div>
                            </div>
                            @if (Model.Status != ColorOasisSystemWeb.Enums.InvoiceStatus.Draft)
                            {
                                <div class="timeline-item">
                                    <div class="timeline-marker bg-success"></div>
                                    <div class="timeline-content">
                                        <h6 class="timeline-title">Invoice Sent</h6>
                                        <p class="timeline-text">@Model.SentDate?.ToString("MMM dd, yyyy HH:mm")</p>
                                    </div>
                                </div>
                            }
                            @if (Model.Status == ColorOasisSystemWeb.Enums.InvoiceStatus.Paid)
                            {
                                <div class="timeline-item">
                                    <div class="timeline-marker bg-success"></div>
                                    <div class="timeline-content">
                                        <h6 class="timeline-title">Fully Paid</h6>
                                        <p class="timeline-text">@Model.LastPaymentDate?.ToString("MMM dd, yyyy HH:mm")</p>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Notes & Terms -->
        @if (!string.IsNullOrEmpty(Model.Notes) || !string.IsNullOrEmpty(Model.TermsAndConditions))
        {
            <div class="row">
                <div class="col-12">
                    <div class="card stretch stretch-full">
                        <div class="card-header">
                            <h5 class="card-title">Additional Information</h5>
                        </div>
                        <div class="card-body">
                            @if (!string.IsNullOrEmpty(Model.Notes))
                            {
                                <div class="mb-3">
                                    <h6>Notes</h6>
                                    <p>@Model.Notes</p>
                                </div>
                            }
                            @if (!string.IsNullOrEmpty(Model.TermsAndConditions))
                            {
                                <div class="mb-3">
                                    <h6>Terms and Conditions</h6>
                                    <p>@Model.TermsAndConditions</p>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        }

        <!-- Audit Information -->
        <div class="row">
            <div class="col-12">
                <div class="card stretch stretch-full">
                    <div class="card-header">
                        <h5 class="card-title">Audit Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Created Date</label>
                                    <div>@Model.CreatedAt.ToString("MMM dd, yyyy HH:mm")</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Created By</label>
                                    <div>@Model.CreatedBy</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Last Updated</label>
                                    <div>@Model.UpdatedAt.ToString("MMM dd, yyyy HH:mm")</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Updated By</label>
                                    <div>@Model.UpdatedBy</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- [ Main Content ] end -->
</div>

@section Styles {
    <style>
        .timeline {
            position: relative;
            padding-left: 30px;
        }
        .timeline::before {
            content: '';
            position: absolute;
            left: 10px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #e9ecef;
        }
        .timeline-item {
            position: relative;
            margin-bottom: 20px;
        }
        .timeline-marker {
            position: absolute;
            left: -25px;
            top: 5px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 2px solid #fff;
            box-shadow: 0 0 0 2px #e9ecef;
        }
        .timeline-title {
            font-size: 14px;
            margin-bottom: 5px;
        }
        .timeline-text {
            font-size: 12px;
            color: #6c757d;
            margin: 0;
        }
    </style>
}
