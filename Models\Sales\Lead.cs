using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using ColorOasisSystemWeb.Enums;

namespace ColorOasisSystemWeb.Models.Sales
{
    public class Lead
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        [Display(Name = "Lead Number")]
        [MaxLength(50)]
        public string LeadNumber { get; set; }
        
        [Required]
        [Display(Name = "Company/Person Name")]
        [MaxLength(200)]
        public string Name { get; set; }
        
        [Display(Name = "Contact Person")]
        [MaxLength(100)]
        public string ContactPerson { get; set; }
        
        [Required]
        [EmailAddress]
        [Display(Name = "Email")]
        [MaxLength(100)]
        public string Email { get; set; }
        
        [Display(Name = "Phone")]
        [MaxLength(20)]
        public string Phone { get; set; }
        
        [Display(Name = "Mobile")]
        [MaxLength(20)]
        public string Mobile { get; set; }
        
        [Display(Name = "Website")]
        [Url]
        [MaxLength(100)]
        public string Website { get; set; }
        
        [Display(Name = "Address")]
        [MaxLength(500)]
        public string Address { get; set; }
        
        [Display(Name = "City")]
        [MaxLength(50)]
        public string City { get; set; }
        
        [Display(Name = "Country")]
        [MaxLength(50)]
        public string Country { get; set; }
        
        [Display(Name = "Industry")]
        [MaxLength(100)]
        public string Industry { get; set; }
        
        [Display(Name = "Company Size")]
        [MaxLength(50)]
        public string CompanySize { get; set; }
        
        [Display(Name = "Annual Revenue")]
        [NumericOnly]
        public decimal? AnnualRevenue { get; set; }
        
        [Display(Name = "Lead Source")]
        public LeadSource Source { get; set; } = LeadSource.Website;
        
        [Display(Name = "Lead Status")]
        public LeadStatus Status { get; set; } = LeadStatus.New;
        
        [Display(Name = "Estimated Value")]
        [NumericOnly]
        public decimal EstimatedValue { get; set; } = 0;
        
        [Display(Name = "Probability (%)")]
        [Range(0, 100)]
        public int Probability { get; set; } = 10;
        
        [Display(Name = "Expected Close Date")]
        public DateTime? ExpectedCloseDate { get; set; }
        
        [Display(Name = "Created Date")]
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
        
        [Display(Name = "Assigned To")]
        public string AssignedTo { get; set; }
        
        [Display(Name = "Last Contact Date")]
        public DateTime? LastContactDate { get; set; }
        
        [Display(Name = "Next Follow-up Date")]
        public DateTime? NextFollowUpDate { get; set; }
        
        [Display(Name = "Lead Score")]
        [Range(0, 100)]
        public int LeadScore { get; set; } = 0;
        
        [Display(Name = "Campaign")]
        [MaxLength(100)]
        public string Campaign { get; set; }
        
        [Display(Name = "Referral Source")]
        [MaxLength(100)]
        public string ReferralSource { get; set; }
        
        [Display(Name = "Notes")]
        [MaxLength(2000)]
        public string Notes { get; set; }
        
        [Display(Name = "Qualification Notes")]
        [MaxLength(1000)]
        public string QualificationNotes { get; set; }
        
        [Display(Name = "Budget Range")]
        [MaxLength(50)]
        public string BudgetRange { get; set; }
        
        [Display(Name = "Decision Timeframe")]
        [MaxLength(50)]
        public string DecisionTimeframe { get; set; }
        
        [Display(Name = "Decision Maker")]
        [MaxLength(100)]
        public string DecisionMaker { get; set; }
        
        [Display(Name = "Pain Points")]
        [MaxLength(1000)]
        public string PainPoints { get; set; }
        
        [Display(Name = "Current Solution")]
        [MaxLength(500)]
        public string CurrentSolution { get; set; }
        
        [Display(Name = "Competitors")]
        [MaxLength(500)]
        public string Competitors { get; set; }
        
        [Display(Name = "Is Qualified")]
        public bool IsQualified { get; set; } = false;
        
        [Display(Name = "Qualified Date")]
        public DateTime? QualifiedDate { get; set; }
        
        [Display(Name = "Qualified By")]
        public string QualifiedBy { get; set; }
        
        [Display(Name = "Converted To Opportunity")]
        public bool IsConverted { get; set; } = false;
        
        [Display(Name = "Converted Date")]
        public DateTime? ConvertedDate { get; set; }
        
        [Display(Name = "Opportunity Id")]
        [ForeignKey("Opportunity")]
        public int? OpportunityId { get; set; }
        public virtual Opportunity Opportunity { get; set; }
        
        [Display(Name = "Created By")]
        public string CreatedBy { get; set; }
        
        [Display(Name = "Last Modified Date")]
        public DateTime LastModifiedDate { get; set; } = DateTime.UtcNow;
        
        [Display(Name = "Last Modified By")]
        public string LastModifiedBy { get; set; }
        
        [Display(Name = "Is Active")]
        public bool IsActive { get; set; } = true;
        
        // Navigation Properties
        public virtual ICollection<LeadActivity> Activities { get; set; } = new List<LeadActivity>();
        public virtual ICollection<LeadNote> LeadNotes { get; set; } = new List<LeadNote>();
    }

    public class LeadActivity
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        [ForeignKey("Lead")]
        public int LeadId { get; set; }
        public virtual Lead Lead { get; set; }
        
        [Display(Name = "Activity Type")]
        [MaxLength(50)]
        public string ActivityType { get; set; } // Call, Email, Meeting, Demo, etc.
        
        [Display(Name = "Subject")]
        [MaxLength(200)]
        public string Subject { get; set; }
        
        [Display(Name = "Description")]
        [MaxLength(1000)]
        public string Description { get; set; }
        
        [Display(Name = "Activity Date")]
        public DateTime ActivityDate { get; set; } = DateTime.UtcNow;
        
        [Display(Name = "Duration (Minutes)")]
        public int DurationMinutes { get; set; } = 0;
        
        [Display(Name = "Outcome")]
        [MaxLength(500)]
        public string Outcome { get; set; }
        
        [Display(Name = "Next Action")]
        [MaxLength(500)]
        public string NextAction { get; set; }
        
        [Display(Name = "Next Action Date")]
        public DateTime? NextActionDate { get; set; }
        
        [Display(Name = "Created By")]
        public string CreatedBy { get; set; }
        
        [Display(Name = "Created Date")]
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
    }

    public class LeadNote
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        [ForeignKey("Lead")]
        public int LeadId { get; set; }
        public virtual Lead Lead { get; set; }
        
        [Required]
        [Display(Name = "Note")]
        [MaxLength(2000)]
        public string Note { get; set; }
        
        [Display(Name = "Is Private")]
        public bool IsPrivate { get; set; } = false;
        
        [Display(Name = "Created By")]
        public string CreatedBy { get; set; }
        
        [Display(Name = "Created Date")]
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
        
        [Display(Name = "Last Modified Date")]
        public DateTime LastModifiedDate { get; set; } = DateTime.UtcNow;
        
        [Display(Name = "Last Modified By")]
        public string LastModifiedBy { get; set; }
    }

    public class Opportunity
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        [Display(Name = "Opportunity Number")]
        [MaxLength(50)]
        public string OpportunityNumber { get; set; }
        
        [Required]
        [Display(Name = "Opportunity Name")]
        [MaxLength(200)]
        public string Name { get; set; }
        
        [Display(Name = "Description")]
        [MaxLength(1000)]
        public string Description { get; set; }
        
        [ForeignKey("Lead")]
        public int? LeadId { get; set; }
        public virtual Lead Lead { get; set; }
        
        [ForeignKey("Client")]
        public int? ClientId { get; set; }
        public virtual Client Client { get; set; }
        
        [ForeignKey("Company")]
        public int? CompanyId { get; set; }
        public virtual Company Company { get; set; }
        
        [Display(Name = "Stage")]
        public OpportunityStage Stage { get; set; } = OpportunityStage.Prospecting;
        
        [Display(Name = "Estimated Value")]
        [NumericOnly]
        public decimal EstimatedValue { get; set; } = 0;
        
        [Display(Name = "Probability (%)")]
        [Range(0, 100)]
        public int Probability { get; set; } = 10;
        
        [Display(Name = "Expected Close Date")]
        public DateTime? ExpectedCloseDate { get; set; }
        
        [Display(Name = "Actual Close Date")]
        public DateTime? ActualCloseDate { get; set; }
        
        [Display(Name = "Sales Cycle (Days)")]
        public int SalesCycleDays { get; set; } = 30;
        
        [Display(Name = "Assigned To")]
        public string AssignedTo { get; set; }
        
        [Display(Name = "Created Date")]
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
        
        [Display(Name = "Created By")]
        public string CreatedBy { get; set; }
        
        [Display(Name = "Last Modified Date")]
        public DateTime LastModifiedDate { get; set; } = DateTime.UtcNow;
        
        [Display(Name = "Last Modified By")]
        public string LastModifiedBy { get; set; }
        
        [Display(Name = "Is Won")]
        public bool IsWon { get; set; } = false;
        
        [Display(Name = "Is Lost")]
        public bool IsLost { get; set; } = false;
        
        [Display(Name = "Lost Reason")]
        [MaxLength(500)]
        public string LostReason { get; set; }
        
        [Display(Name = "Competitor")]
        [MaxLength(200)]
        public string Competitor { get; set; }
        
        [Display(Name = "Is Active")]
        public bool IsActive { get; set; } = true;
        
        // Navigation Properties
        public virtual ICollection<OpportunityActivity> Activities { get; set; } = new List<OpportunityActivity>();
        public virtual ICollection<OpportunityNote> OpportunityNotes { get; set; } = new List<OpportunityNote>();
        public virtual ICollection<SalesOrder> SalesOrders { get; set; } = new List<SalesOrder>();
    }

    public class OpportunityActivity
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        [ForeignKey("Opportunity")]
        public int OpportunityId { get; set; }
        public virtual Opportunity Opportunity { get; set; }
        
        [Display(Name = "Activity Type")]
        [MaxLength(50)]
        public string ActivityType { get; set; }
        
        [Display(Name = "Subject")]
        [MaxLength(200)]
        public string Subject { get; set; }
        
        [Display(Name = "Description")]
        [MaxLength(1000)]
        public string Description { get; set; }
        
        [Display(Name = "Activity Date")]
        public DateTime ActivityDate { get; set; } = DateTime.UtcNow;
        
        [Display(Name = "Duration (Minutes)")]
        public int DurationMinutes { get; set; } = 0;
        
        [Display(Name = "Outcome")]
        [MaxLength(500)]
        public string Outcome { get; set; }
        
        [Display(Name = "Created By")]
        public string CreatedBy { get; set; }
        
        [Display(Name = "Created Date")]
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
    }

    public class OpportunityNote
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        [ForeignKey("Opportunity")]
        public int OpportunityId { get; set; }
        public virtual Opportunity Opportunity { get; set; }
        
        [Required]
        [Display(Name = "Note")]
        [MaxLength(2000)]
        public string Note { get; set; }
        
        [Display(Name = "Is Private")]
        public bool IsPrivate { get; set; } = false;
        
        [Display(Name = "Created By")]
        public string CreatedBy { get; set; }
        
        [Display(Name = "Created Date")]
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
    }
}
