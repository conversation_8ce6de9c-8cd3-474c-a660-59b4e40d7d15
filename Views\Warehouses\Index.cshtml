@model IEnumerable<ColorOasisSystemWeb.Models.Inventory.Warehouse>

@{
    ViewData["Title"] = "Warehouses";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="card-title">@ViewData["Title"]</h4>
                    <div>
                        @if (User.HasClaim("Permission", "Warehouse.Create"))
                        {
                            <a asp-action="Create" class="btn btn-primary">
                                <i data-feather="plus" class="feather-icon me-2"></i>
                                Add New Warehouse
                            </a>
                        }
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="warehousesTable" class="table table-striped table-bordered">
                            <thead>
                                <tr>
                                    <th>Code</th>
                                    <th>Name</th>
                                    <th>Address</th>
                                    <th>Manager</th>
                                    <th>Contact</th>
                                    <th>Bin Locations</th>
                                    <th>Inventory Items</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in Model)
                                {
                                    <tr>
                                        <td>
                                            <strong>@Html.DisplayFor(modelItem => item.Code)</strong>
                                            @if (item.IsDefault)
                                            {
                                                <br><span class="badge bg-primary">Default</span>
                                            }
                                        </td>
                                        <td>
                                            <strong>@Html.DisplayFor(modelItem => item.Name)</strong>
                                        </td>
                                        <td>
                                            @if (!string.IsNullOrEmpty(item.Address))
                                            {
                                                @Html.DisplayFor(modelItem => item.Address)
                                            }
                                            else
                                            {
                                                <span class="text-muted">No address</span>
                                            }
                                        </td>
                                        <td>
                                            @if (!string.IsNullOrEmpty(item.Manager))
                                            {
                                                @Html.DisplayFor(modelItem => item.Manager)
                                            }
                                            else
                                            {
                                                <span class="text-muted">No manager</span>
                                            }
                                        </td>
                                        <td>
                                            @if (!string.IsNullOrEmpty(item.Phone))
                                            {
                                                <div><i data-feather="phone" class="feather-icon me-1"></i>@item.Phone</div>
                                            }
                                            @if (!string.IsNullOrEmpty(item.Email))
                                            {
                                                <div><i data-feather="mail" class="feather-icon me-1"></i>@item.Email</div>
                                            }
                                            @if (string.IsNullOrEmpty(item.Phone) && string.IsNullOrEmpty(item.Email))
                                            {
                                                <span class="text-muted">No contact</span>
                                            }
                                        </td>
                                        <td>
                                            <span class="badge bg-info">@(item.BinLocations?.Count() ?? 0)</span>
                                        </td>
                                        <td>
                                            @{
                                                var uniqueItems = item.InventoryLocations?.Select(il => il.InventoryItem).Distinct().Count() ?? 0;
                                                var totalQuantity = item.InventoryLocations?.Sum(il => il.QuantityOnHand) ?? 0;
                                            }
                                            <div>
                                                <span class="badge bg-success">@uniqueItems items</span>
                                            </div>
                                            @if (totalQuantity > 0)
                                            {
                                                <small class="text-muted">Total qty: @totalQuantity</small>
                                            }
                                        </td>
                                        <td>
                                            @if (item.IsActive)
                                            {
                                                <span class="badge bg-success">Active</span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-secondary">Inactive</span>
                                            }
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                @if (User.HasClaim("Permission", "Warehouse.View"))
                                                {
                                                    <a asp-action="Details" asp-route-id="@item.Id" class="btn btn-sm btn-outline-info" title="View Details">
                                                        <i data-feather="eye" class="feather-icon"></i>
                                                    </a>
                                                }
                                                @if (User.HasClaim("Permission", "Warehouse.Edit"))
                                                {
                                                    <a asp-action="Edit" asp-route-id="@item.Id" class="btn btn-sm btn-outline-primary" title="Edit">
                                                        <i data-feather="edit" class="feather-icon"></i>
                                                    </a>
                                                }
                                                @if (User.HasClaim("Permission", "Warehouse.Delete"))
                                                {
                                                    <a asp-action="Delete" asp-route-id="@item.Id" class="btn btn-sm btn-outline-danger" title="Delete">
                                                        <i data-feather="trash-2" class="feather-icon"></i>
                                                    </a>
                                                }
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_DataTablesScriptsPartial");}
    <script>
        $(document).ready(function() {
            $('#warehousesTable').DataTable({
                "responsive": true,
                "pageLength": 25,
                "order": [[0, "asc"]],
                "columnDefs": [
                    { "orderable": false, "targets": [8] }
                ],
                "language": {
                    "search": "Search warehouses:",
                    "lengthMenu": "Show _MENU_ warehouses per page",
                    "info": "Showing _START_ to _END_ of _TOTAL_ warehouses",
                    "paginate": {
                        "first": "First",
                        "last": "Last",
                        "next": "Next",
                        "previous": "Previous"
                    }
                }
            });

            // Initialize Feather icons
            if (typeof feather !== 'undefined') {
                feather.replace();
            }
        });
    </script>
}
