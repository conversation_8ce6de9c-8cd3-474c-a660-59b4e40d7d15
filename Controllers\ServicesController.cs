using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using ColorOasisSystemWeb.Data;
using ColorOasisSystemWeb.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Hosting;
using ColorOasisSystemWeb.BL;
using Microsoft.AspNetCore.Authorization;
using ColorOasisSystemWeb.AdvancedNotification.Services;
using ColorOasisSystemWeb.Enums;
using ColorOasisSystemWeb.ViewModels.Service;
using ColorOasisSystemWeb.Models.Mappers;

namespace ColorOasisSystemWeb.Controllers
{
    [Authorize(Roles = "User, Admin")]
    public class ServicesController : Controller
    {
        private readonly AppDbContext _context;
        private readonly IWebHostEnvironment _webHostEnvironment;
        private readonly UserManager<User> _userManager;
        private readonly NotificationService _notificationService;

        public ServicesController(
            AppDbContext context, 
            IWebHostEnvironment webHostEnvironment,
            UserManager<User> userManager,
            NotificationService notificationService)
        {
            _context = context;
            _webHostEnvironment = webHostEnvironment;
            _userManager = userManager;
            _notificationService = notificationService;
        }

        // GET: Services
        public async Task<IActionResult> Index(string searchTerm = null, int pageNumber = 1, int pageSize = 10)
        {
            var servicesQuery = _context.Services
                .Include(s => s.ServiceCategory)
                .Include(s => s.ServiceType)
                .AsNoTracking();
                
            if (!string.IsNullOrEmpty(searchTerm))
            {
                servicesQuery = servicesQuery.Where(s => 
                    s.Name.Contains(searchTerm) || 
                    s.NameEn.Contains(searchTerm) || 
                    s.Code.Contains(searchTerm) ||
                    s.ServiceCategory.Name.Contains(searchTerm) ||
                    s.ServiceType.Name.Contains(searchTerm));
            }
                
            var pagedServices = await Helpers.PaginationHelper.CreatePagedListAsync(servicesQuery, pageNumber, pageSize);
            var viewModels = ServiceMapper.ToViewModelList(pagedServices);
            
            // Create a paged list with the mapped view models
            var pagedViewModels = Helpers.PaginationHelper.CreatePagedListFromMappedList(
                viewModels, pageNumber, pageSize, pagedServices.TotalCount);
            
            ViewBag.PagedList = pagedViewModels;
            ViewBag.SearchTerm = searchTerm;
                
            return View(pagedViewModels);
        }

        // GET: Services/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var service = await _context.Services
                .Include(s => s.ServiceCategory)
                .Include(s => s.ServiceType)
                .FirstOrDefaultAsync(m => m.Id == id);
            if (service == null)
            {
                return NotFound();
            }

            var viewModel = ServiceMapper.ToViewModel(service);
            return View(viewModel);
        }

        // GET: Services/Create
        public IActionResult Create()
        {
            string datePart = DateTime.Now.ToString("ddMMyyyy");

            // Get the latest client code that matches today's date
            var lastClient = _context.Services
                .Where(c => c.Code.Contains(datePart))
                .OrderByDescending(c => c.Code)
                .FirstOrDefaultAsync().Result;

            // Initialize the sequence number
            int sequenceNumber = 1;

            if (lastClient != null)
            {
                // Extract the numeric part and increment it
                string lastSequence = lastClient.Code.Substring(13);
                sequenceNumber = int.Parse(lastSequence) + 1;
            }

            // Generate the new client code
            string newClientCode = $"SRVC-{datePart}{sequenceNumber.ToString("D4")}";
            ViewBag.Code = newClientCode;
            ViewData["ServiceCategoryId"] = new SelectList(_context.ServiceCategories, "Id", "Name");
            ViewData["ServiceTypeId"] = new SelectList(_context.ServiceTypes, "Id", "Name");
            var viewModel = new ServiceViewModel();
            return View(viewModel);
        }

        // POST: Services/Create
        // To protect from overposting attacks, enable the specific properties you want to bind to.
        // For more details, see http://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("Id,Code,Name,NameEn,MinimumPrice,Price,MaximumPrice,Barcode,Discount,ServiceTypeId,ServiceCategoryId,IsDeleted")] ServiceViewModel viewModel, IFormFile? servicePic)
        {
            string wwwRootPath = _webHostEnvironment.WebRootPath;
            if (servicePic != null)
            {
                string fileName = Guid.NewGuid().ToString() + Path.GetExtension(servicePic.FileName);
                string productPath = Path.Combine(wwwRootPath, @"images/services");
                if (!Directory.Exists(productPath))
                {
                    Directory.CreateDirectory(productPath);
                }
                await ImageUtilities.CompressAndSaveImageAsync(servicePic, (Path.Combine(productPath, fileName)), 30);
                viewModel.Photo = @"/images/services/" + fileName;
            }
            else
            {
                // Code to handle when no file is uploaded
                string defaultFileName = "logo-abbr.png"; // Replace with your default image file name
                viewModel.Photo = @"/Images/" + defaultFileName; // The leading slash indicates the root of the wwwroot folder
            }
            if (ModelState.IsValid)
            {
                var service = ServiceMapper.FromViewModel(viewModel);
                _context.Add(service);
                await _context.SaveChangesAsync();
                
                // Send notification for new service
                var currentUser = await _userManager.GetUserAsync(User);
                if (currentUser != null)
                {
                    await _notificationService.CreateAndSendNotification(
                        ModelType.Service,
                        ProcessType.Add,
                        service.Id,
                        currentUser);
                }
                
                return RedirectToAction(nameof(Index));
            }
            ViewData["ServiceCategoryId"] = new SelectList(_context.ServiceCategories, "Id", "Name", viewModel.ServiceCategoryId);
            ViewData["ServiceTypeId"] = new SelectList(_context.ServiceTypes, "Id", "Name", viewModel.ServiceTypeId);
            return View(viewModel);
        }

        // GET: Services/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var service = await _context.Services.FindAsync(id);
            if (service == null)
            {
                return NotFound();
            }
            var viewModel = ServiceMapper.ToViewModel(service);
            ViewData["ServiceCategoryId"] = new SelectList(_context.ServiceCategories, "Id", "Name", service.ServiceCategoryId);
            ViewData["ServiceTypeId"] = new SelectList(_context.ServiceTypes, "Id", "Name", service.ServiceTypeId);
            return View(viewModel);
        }

        // POST: Services/Edit/5
        // To protect from overposting attacks, enable the specific properties you want to bind to.
        // For more details, see http://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("Id,Code,Photo,Name,NameEn,MinimumPrice,MaximumPrice,Price,Barcode,Discount,ServiceTypeId,ServiceCategoryId,IsDeleted")] ServiceViewModel viewModel)
        {
            if (id != viewModel.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    var service = ServiceMapper.FromViewModel(viewModel);
                    _context.Update(service);
                    await _context.SaveChangesAsync();
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!ServiceExists(viewModel.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
            }
            ViewData["ServiceCategoryId"] = new SelectList(_context.ServiceCategories, "Id", "Name", viewModel.ServiceCategoryId);
            ViewData["ServiceTypeId"] = new SelectList(_context.ServiceTypes, "Id", "Name", viewModel.ServiceTypeId);
            return View(viewModel);
        }

        // GET: Services/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var service = await _context.Services
                .Include(s => s.ServiceCategory)
                .Include(s => s.ServiceType)
                .FirstOrDefaultAsync(m => m.Id == id);
            if (service == null)
            {
                return NotFound();
            }

            var viewModel = ServiceMapper.ToViewModel(service);
            return View(viewModel);
        }

        // POST: Services/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var service = await _context.Services.FindAsync(id);
            if (service != null)
            {
                _context.Services.Remove(service);
            }

            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
        }

        private bool ServiceExists(int id)
        {
            return _context.Services.Any(e => e.Id == id);
        }
    }
}
