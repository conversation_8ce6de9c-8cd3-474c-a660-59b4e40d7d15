using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using ColorOasisSystemWeb.Data;
using ColorOasisSystemWeb.Models.Inventory;
using ColorOasisSystemWeb.Authorization;
using Microsoft.AspNetCore.Identity;
using ColorOasisSystemWeb.Models;
using ColorOasisSystemWeb.Enums;
using Microsoft.AspNetCore.Mvc.Rendering;

namespace ColorOasisSystemWeb.Controllers
{
    [Authorize]
    public class InventoryTransactionsController : Controller
    {
        private readonly AppDbContext _context;
        private readonly UserManager<User> _userManager;
        private readonly ILogger<InventoryTransactionsController> _logger;

        public InventoryTransactionsController(
            AppDbContext context,
            UserManager<User> userManager,
            ILogger<InventoryTransactionsController> logger)
        {
            _context = context;
            _userManager = userManager;
            _logger = logger;
        }

        // GET: InventoryTransactions
        [Authorize(Permissions.Inventory.View)]
        public async Task<IActionResult> Index(int? itemId, int? warehouseId, TransactionType? type, DateTime? fromDate, DateTime? toDate)
        {
            try
            {
                var query = _context.InventoryTransactions
                    .Include(t => t.InventoryItem)
                    .Include(t => t.Warehouse)
                    .AsQueryable();

                // Apply filters
                if (itemId.HasValue)
                    query = query.Where(t => t.ItemId == itemId.Value);

                if (warehouseId.HasValue)
                    query = query.Where(t => t.WarehouseId == warehouseId.Value);

                if (type.HasValue)
                    query = query.Where(t => t.Type == type.Value);

                if (fromDate.HasValue)
                    query = query.Where(t => t.TransactionDate >= fromDate.Value);

                if (toDate.HasValue)
                    query = query.Where(t => t.TransactionDate <= toDate.Value.AddDays(1));

                var transactions = await query
                    .OrderByDescending(t => t.TransactionDate)
                    .Take(1000) // Limit to prevent performance issues
                    .ToListAsync();

                // Populate filter dropdowns
                ViewBag.ItemId = new SelectList(_context.InventoryItems.Where(i => !i.IsDeleted), "Id", "Name", itemId);
                ViewBag.WarehouseId = new SelectList(_context.Warehouses.Where(w => w.IsActive), "Id", "Name", warehouseId);
                ViewBag.Type = new SelectList(Enum.GetValues(typeof(TransactionType)).Cast<TransactionType>()
                    .Select(t => new { Value = (int)t, Text = t.ToString() }), "Value", "Text", (int?)type);

                ViewBag.FromDate = fromDate?.ToString("yyyy-MM-dd");
                ViewBag.ToDate = toDate?.ToString("yyyy-MM-dd");

                return View(transactions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving inventory transactions");
                TempData["Error"] = "An error occurred while retrieving inventory transactions.";
                return View(new List<InventoryTransaction>());
            }
        }

        // GET: InventoryTransactions/Details/5
        [Authorize(Permissions.Inventory.View)]
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var transaction = await _context.InventoryTransactions
                .Include(t => t.InventoryItem)
                .Include(t => t.Warehouse)
                .FirstOrDefaultAsync(m => m.Id == id);

            if (transaction == null)
            {
                return NotFound();
            }

            return View(transaction);
        }

        // GET: InventoryTransactions/Transfer
        [Authorize(Permissions.Inventory.Transfer)]
        public IActionResult Transfer()
        {
            ViewData["ItemId"] = new SelectList(_context.InventoryItems.Where(i => !i.IsDeleted), "Id", "Name");
            ViewData["FromWarehouseId"] = new SelectList(_context.Warehouses.Where(w => w.IsActive), "Id", "Name");
            ViewData["ToWarehouseId"] = new SelectList(_context.Warehouses.Where(w => w.IsActive), "Id", "Name");
            return View();
        }

        // POST: InventoryTransactions/Transfer
        [HttpPost]
        [ValidateAntiForgeryToken]
        [Authorize(Permissions.Inventory.Transfer)]
        public async Task<IActionResult> Transfer(int itemId, int fromWarehouseId, int toWarehouseId, decimal quantity, string notes)
        {
            if (fromWarehouseId == toWarehouseId)
            {
                ModelState.AddModelError("", "Source and destination warehouses cannot be the same.");
            }

            if (quantity <= 0)
            {
                ModelState.AddModelError("", "Transfer quantity must be greater than zero.");
            }

            if (ModelState.IsValid)
            {
                try
                {
                    var currentUser = await _userManager.GetUserAsync(User);
                    var userName = currentUser?.UserName ?? "System";

                    // Check available quantity in source warehouse
                    var sourceLocation = await _context.InventoryLocations
                        .FirstOrDefaultAsync(l => l.ItemId == itemId && l.WarehouseId == fromWarehouseId);

                    if (sourceLocation == null || sourceLocation.QuantityAvailable < quantity)
                    {
                        TempData["Error"] = "Insufficient quantity available for transfer.";
                        ViewData["ItemId"] = new SelectList(_context.InventoryItems.Where(i => !i.IsDeleted), "Id", "Name", itemId);
                        ViewData["FromWarehouseId"] = new SelectList(_context.Warehouses.Where(w => w.IsActive), "Id", "Name", fromWarehouseId);
                        ViewData["ToWarehouseId"] = new SelectList(_context.Warehouses.Where(w => w.IsActive), "Id", "Name", toWarehouseId);
                        return View();
                    }

                    var transferRef = await GenerateTransferReference();

                    // Create outbound transaction
                    var outTransaction = new InventoryTransaction
                    {
                        ItemId = itemId,
                        WarehouseId = fromWarehouseId,
                        Type = TransactionType.Transfer,
                        Quantity = -quantity,
                        TransactionDate = DateTime.UtcNow,
                        Reference = transferRef,
                        UnitCost = sourceLocation.LastCost,
                        Notes = $"Transfer out to warehouse. {notes}",
                        CreatedBy = userName
                    };

                    // Create inbound transaction
                    var inTransaction = new InventoryTransaction
                    {
                        ItemId = itemId,
                        WarehouseId = toWarehouseId,
                        Type = TransactionType.Transfer,
                        Quantity = quantity,
                        TransactionDate = DateTime.UtcNow,
                        Reference = transferRef,
                        UnitCost = sourceLocation.LastCost,
                        Notes = $"Transfer in from warehouse. {notes}",
                        CreatedBy = userName
                    };

                    _context.InventoryTransactions.AddRange(outTransaction, inTransaction);

                    // Update source location
                    sourceLocation.QuantityOnHand -= quantity;
                    sourceLocation.UpdatedAt = DateTime.UtcNow;
                    sourceLocation.UpdatedBy = userName;

                    // Update or create destination location
                    var destLocation = await _context.InventoryLocations
                        .FirstOrDefaultAsync(l => l.ItemId == itemId && l.WarehouseId == toWarehouseId);

                    if (destLocation == null)
                    {
                        destLocation = new InventoryLocation
                        {
                            ItemId = itemId,
                            WarehouseId = toWarehouseId,
                            BinLocation = "MAIN",
                            QuantityOnHand = quantity,
                            QuantityReserved = 0,
                            LastCost = sourceLocation.LastCost,
                            CreatedAt = DateTime.UtcNow,
                            CreatedBy = userName,
                            UpdatedAt = DateTime.UtcNow,
                            UpdatedBy = userName
                        };
                        _context.InventoryLocations.Add(destLocation);
                    }
                    else
                    {
                        destLocation.QuantityOnHand += quantity;
                        destLocation.UpdatedAt = DateTime.UtcNow;
                        destLocation.UpdatedBy = userName;
                    }

                    await _context.SaveChangesAsync();

                    TempData["Success"] = "Inventory transfer completed successfully.";
                    return RedirectToAction(nameof(Index));
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing inventory transfer");
                    TempData["Error"] = "An error occurred while processing the transfer.";
                }
            }

            ViewData["ItemId"] = new SelectList(_context.InventoryItems.Where(i => !i.IsDeleted), "Id", "Name", itemId);
            ViewData["FromWarehouseId"] = new SelectList(_context.Warehouses.Where(w => w.IsActive), "Id", "Name", fromWarehouseId);
            ViewData["ToWarehouseId"] = new SelectList(_context.Warehouses.Where(w => w.IsActive), "Id", "Name", toWarehouseId);
            return View();
        }

        // Helper method to generate transfer reference
        private async Task<string> GenerateTransferReference()
        {
            var today = DateTime.Today;
            var prefix = $"TRF{today:yyyyMMdd}";

            var lastTransfer = await _context.InventoryTransactions
                .Where(t => t.Type == TransactionType.Transfer && t.Reference.StartsWith(prefix))
                .OrderByDescending(t => t.Reference)
                .FirstOrDefaultAsync();

            int nextNumber = 1;
            if (lastTransfer != null)
            {
                var numberPart = lastTransfer.Reference.Substring(prefix.Length);
                if (int.TryParse(numberPart, out int lastNumber))
                {
                    nextNumber = lastNumber + 1;
                }
            }

            return $"{prefix}{nextNumber:D3}";
        }

        // API method to get available stock for transfer
        [HttpGet]
        public async Task<IActionResult> GetAvailableStock(int itemId, int warehouseId)
        {
            try
            {
                var location = await _context.InventoryLocations
                    .FirstOrDefaultAsync(l => l.ItemId == itemId && l.WarehouseId == warehouseId);

                var item = await _context.InventoryItems.FindAsync(itemId);

                return Json(new {
                    success = true,
                    availableStock = location?.QuantityAvailable ?? 0,
                    unitOfMeasure = item?.UnitOfMeasure ?? "PCS",
                    binLocation = location?.BinLocation ?? ""
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting available stock");
                return Json(new { success = false, message = "Error retrieving available stock" });
            }
        }
    }
}
