@model ColorOasisSystemWeb.Models.Inventory.InventoryItem

@{
    ViewData["Title"] = "Create Inventory Item";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="card-title">@ViewData["Title"]</h4>
                    <a asp-action="Index" class="btn btn-secondary">
                        <i data-feather="arrow-left" class="feather-icon me-2"></i>
                        Back to List
                    </a>
                </div>
                <div class="card-body">
                    <form asp-action="Create" method="post" enctype="multipart/form-data">
                        <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                        
                        <div class="row">
                            <!-- Basic Information -->
                            <div class="col-md-6">
                                <h5 class="mb-3">Basic Information</h5>
                                
                                <div class="mb-3">
                                    <label asp-for="Code" class="form-label"></label>
                                    <input asp-for="Code" class="form-control" />
                                    <span asp-validation-for="Code" class="text-danger"></span>
                                </div>

                                <div class="mb-3">
                                    <label asp-for="Name" class="form-label"></label>
                                    <input asp-for="Name" class="form-control" />
                                    <span asp-validation-for="Name" class="text-danger"></span>
                                </div>

                                <div class="mb-3">
                                    <label asp-for="NameEn" class="form-label"></label>
                                    <input asp-for="NameEn" class="form-control" />
                                    <span asp-validation-for="NameEn" class="text-danger"></span>
                                </div>

                                <div class="mb-3">
                                    <label asp-for="ServiceCategoryId" class="form-label"></label>
                                    <select asp-for="ServiceCategoryId" class="form-select" asp-items="ViewBag.ServiceCategoryId">
                                        <option value="">-- Select Category --</option>
                                    </select>
                                    <span asp-validation-for="ServiceCategoryId" class="text-danger"></span>
                                </div>

                                <div class="mb-3">
                                    <label asp-for="ServiceTypeId" class="form-label"></label>
                                    <select asp-for="ServiceTypeId" class="form-select" asp-items="ViewBag.ServiceTypeId">
                                        <option value="">-- Select Type --</option>
                                    </select>
                                    <span asp-validation-for="ServiceTypeId" class="text-danger"></span>
                                </div>

                                <div class="mb-3">
                                    <label asp-for="Type" class="form-label"></label>
                                    <select asp-for="Type" class="form-select">
                                        <option value="Physical">Physical</option>
                                        <option value="Service">Service</option>
                                        <option value="Digital">Digital</option>
                                    </select>
                                    <span asp-validation-for="Type" class="text-danger"></span>
                                </div>
                            </div>

                            <!-- Inventory Details -->
                            <div class="col-md-6">
                                <h5 class="mb-3">Inventory Details</h5>
                                
                                <div class="mb-3">
                                    <label asp-for="SKU" class="form-label"></label>
                                    <input asp-for="SKU" class="form-control" placeholder="Auto-generated if empty" />
                                    <span asp-validation-for="SKU" class="text-danger"></span>
                                </div>

                                <div class="mb-3">
                                    <label asp-for="UnitOfMeasure" class="form-label"></label>
                                    <input asp-for="UnitOfMeasure" class="form-control" value="PCS" />
                                    <span asp-validation-for="UnitOfMeasure" class="text-danger"></span>
                                </div>

                                <div class="mb-3">
                                    <label asp-for="ReorderPoint" class="form-label"></label>
                                    <input asp-for="ReorderPoint" class="form-control" type="number" step="0.01" />
                                    <span asp-validation-for="ReorderPoint" class="text-danger"></span>
                                </div>

                                <div class="mb-3">
                                    <label asp-for="MaxStockLevel" class="form-label"></label>
                                    <input asp-for="MaxStockLevel" class="form-control" type="number" step="0.01" />
                                    <span asp-validation-for="MaxStockLevel" class="text-danger"></span>
                                </div>

                                <div class="mb-3">
                                    <label asp-for="SafetyStock" class="form-label"></label>
                                    <input asp-for="SafetyStock" class="form-control" type="number" step="0.01" />
                                    <span asp-validation-for="SafetyStock" class="text-danger"></span>
                                </div>

                                <div class="mb-3">
                                    <label asp-for="LeadTimeDays" class="form-label"></label>
                                    <input asp-for="LeadTimeDays" class="form-control" type="number" />
                                    <span asp-validation-for="LeadTimeDays" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Pricing Information -->
                            <div class="col-md-6">
                                <h5 class="mb-3">Pricing Information</h5>
                                
                                <div class="mb-3">
                                    <label asp-for="Price" class="form-label"></label>
                                    <input asp-for="Price" class="form-control" type="number" step="0.01" />
                                    <span asp-validation-for="Price" class="text-danger"></span>
                                </div>

                                <div class="mb-3">
                                    <label asp-for="MinimumPrice" class="form-label"></label>
                                    <input asp-for="MinimumPrice" class="form-control" type="number" step="0.01" />
                                    <span asp-validation-for="MinimumPrice" class="text-danger"></span>
                                </div>

                                <div class="mb-3">
                                    <label asp-for="MaximumPrice" class="form-label"></label>
                                    <input asp-for="MaximumPrice" class="form-control" type="number" step="0.01" />
                                    <span asp-validation-for="MaximumPrice" class="text-danger"></span>
                                </div>

                                <div class="mb-3">
                                    <label asp-for="StandardCost" class="form-label"></label>
                                    <input asp-for="StandardCost" class="form-control" type="number" step="0.01" />
                                    <span asp-validation-for="StandardCost" class="text-danger"></span>
                                </div>
                            </div>

                            <!-- Additional Settings -->
                            <div class="col-md-6">
                                <h5 class="mb-3">Additional Settings</h5>
                                
                                <div class="mb-3">
                                    <label asp-for="PreferredSupplier" class="form-label"></label>
                                    <input asp-for="PreferredSupplier" class="form-control" />
                                    <span asp-validation-for="PreferredSupplier" class="text-danger"></span>
                                </div>

                                <div class="mb-3">
                                    <label asp-for="Barcode" class="form-label"></label>
                                    <input asp-for="Barcode" class="form-control" />
                                    <span asp-validation-for="Barcode" class="text-danger"></span>
                                </div>

                                <div class="mb-3">
                                    <label asp-for="ABCClass" class="form-label"></label>
                                    <select asp-for="ABCClass" class="form-select">
                                        <option value="A">A - High Value</option>
                                        <option value="B">B - Medium Value</option>
                                        <option value="C" selected>C - Low Value</option>
                                    </select>
                                    <span asp-validation-for="ABCClass" class="text-danger"></span>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check">
                                        <input asp-for="IsSerialTracked" class="form-check-input" type="checkbox" />
                                        <label asp-for="IsSerialTracked" class="form-check-label"></label>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check">
                                        <input asp-for="IsLotTracked" class="form-check-input" type="checkbox" />
                                        <label asp-for="IsLotTracked" class="form-check-label"></label>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check">
                                        <input asp-for="IsDeleted" class="form-check-input" type="checkbox" />
                                        <label asp-for="IsDeleted" class="form-check-label">Is Inactive</label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-end gap-2">
                                    <a asp-action="Index" class="btn btn-secondary">Cancel</a>
                                    <button type="submit" class="btn btn-primary">
                                        <i data-feather="save" class="feather-icon me-2"></i>
                                        Create Item
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    <script>
        // Initialize Feather icons
        if (typeof feather !== 'undefined') {
            feather.replace();
        }
    </script>
}
