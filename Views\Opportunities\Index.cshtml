@{
    ViewData["Title"] = "Opportunities";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="nxl-content">
    <!-- [ page-header ] start -->
    <div class="page-header">
        <div class="page-header-left d-flex align-items-center">
            <div class="page-header-title">
                <h5 class="m-b-10">Opportunities</h5>
            </div>
            <ul class="breadcrumb">
                <li class="breadcrumb-item"><a href="@Url.Action("Index", "Home")">Home</a></li>
                <li class="breadcrumb-item">ERP</li>
                <li class="breadcrumb-item">Sales</li>
                <li class="breadcrumb-item">Opportunities</li>
            </ul>
        </div>
    </div>
    <!-- [ page-header ] end -->

    <!-- [ Main Content ] start -->
    <div class="main-content">
        <div class="row">
            <div class="col-lg-12">
                <div class="card stretch stretch-full">
                    <div class="card-body">
                        <div class="text-center py-5">
                            <div class="mb-4">
                                <i class="feather-target" style="font-size: 4rem; color: #6c757d;"></i>
                            </div>
                            <h4 class="mb-3">Opportunities Management</h4>
                            <p class="text-muted mb-4">
                                The Opportunities module is coming soon! This will include opportunity tracking, forecasting, and deal management.
                            </p>
                            <div class="alert alert-info">
                                <i class="feather-info me-2"></i>
                                <strong>Coming Soon:</strong> Deal pipeline, probability tracking, revenue forecasting, and opportunity analytics.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- [ Main Content ] end -->
</div>
