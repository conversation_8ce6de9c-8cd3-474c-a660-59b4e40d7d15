using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using ColorOasisSystemWeb.Authorization;

namespace ColorOasisSystemWeb.Controllers
{
    [Authorize]
    public class AccountingController : Controller
    {
        [Authorize(Permissions.Accounting.View)]
        public IActionResult Index()
        {
            ViewBag.Title = "Accounting Dashboard";
            ViewBag.Message = "View accounting overview, financial summaries, and key metrics.";
            return View("~/Views/Shared/ComingSoon.cshtml");
        }
    }
}
