using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using ColorOasisSystemWeb.Authorization;

namespace ColorOasisSystemWeb.Controllers
{
    [Authorize]
    public class StockAdjustmentsController : Controller
    {
        [Authorize(Permissions.Inventory.StockAdjustment)]
        public IActionResult Index()
        {
            ViewBag.Title = "Stock Adjustments";
            ViewBag.Message = "Manage inventory adjustments, stock corrections, and quantity updates.";
            return View("~/Views/Shared/ComingSoon.cshtml");
        }
    }
}
