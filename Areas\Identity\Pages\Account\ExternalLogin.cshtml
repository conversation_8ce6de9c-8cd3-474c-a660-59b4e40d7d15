@page
@model ExternalLoginModel
@{
    ViewData["Title"] = @SharedLocalizer["Identity.Account.Register"];
    Layout = null; // This page won't use the layout
}

<h1>@ViewData["Title"]</h1>
<h2 id="external-login-title">@SharedLocalizer["Identity.Account.AssociateAccount"] @Model.ProviderDisplayName</h2>
<hr />

<p id="external-login-description" class="text-info">
    @SharedLocalizer["Identity.Account.ExternalLoginSuccess"] <strong>@Model.ProviderDisplayName</strong>.
    @SharedLocalizer["Identity.Account.EnterEmailToFinish"]
</p>

<div class="row">
    <div class="col-md-4">
        <form asp-page-handler="Confirmation" asp-route-returnUrl="@Model.ReturnUrl" method="post">
            <div asp-validation-summary="ModelOnly" class="text-danger" role="alert"></div>
            <div class="form-floating mb-3">
                <input asp-for="Input.Email" class="form-control" autocomplete="email" placeholder="Please enter your email."/>
                <label asp-for="Input.Email" class="form-label"></label>
                <span asp-validation-for="Input.Email" class="text-danger"></span>
            </div>
            <button type="submit" class="w-100 btn btn-lg btn-primary">@SharedLocalizer["Identity.Account.Register"]</button>
        </form>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
