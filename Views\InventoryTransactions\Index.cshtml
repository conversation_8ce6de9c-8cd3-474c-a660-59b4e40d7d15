@model IEnumerable<ColorOasisSystemWeb.Models.Inventory.InventoryTransaction>

@{
    ViewData["Title"] = "Inventory Transactions";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="nxl-content">
    <!-- [ page-header ] start -->
    <div class="page-header">
        <div class="page-header-left d-flex align-items-center">
            <div class="page-header-title">
                <h5 class="m-b-10">Inventory Transactions</h5>
            </div>
            <ul class="breadcrumb">
                <li class="breadcrumb-item"><a href="@Url.Action("Index", "Home")">Home</a></li>
                <li class="breadcrumb-item">ERP</li>
                <li class="breadcrumb-item">Inventory</li>
                <li class="breadcrumb-item">Transactions</li>
            </ul>
        </div>
        <div class="page-header-right ms-auto">
            <div class="page-header-right-items">
                <div class="d-flex d-md-none">
                    <a href="javascript:void(0)" class="page-header-right-close-toggle">
                        <i class="feather-arrow-left me-2"></i>
                        <span>Back</span>
                    </a>
                </div>
                <div class="d-flex align-items-center gap-2 page-header-right-items-wrapper">
                    <a href="@Url.Action("Transfer")" class="btn btn-primary">
                        <i class="feather-shuffle me-2"></i>
                        <span>Transfer Stock</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
    <!-- [ page-header ] end -->

    <!-- [ Main Content ] start -->
    <div class="main-content">
        <div class="row">
            <div class="col-lg-12">
                <div class="card stretch stretch-full">
                    <div class="card-header">
                        <h5 class="card-title">Inventory Transactions</h5>
                        <div class="card-header-action">
                            <div class="card-header-btn">
                                <div data-bs-toggle="tooltip" title="Refresh">
                                    <a href="javascript:void(0);" class="avatar-text avatar-md" onclick="location.reload();">
                                        <i class="feather-refresh-cw"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body custom-card-action p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0" id="transactionsTable">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Item</th>
                                        <th>Type</th>
                                        <th>Warehouse</th>
                                        <th>Quantity</th>
                                        <th>Value</th>
                                        <th>Reference</th>
                                        <th class="text-end">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var transaction in Model)
                                    {
                                        <tr>
                                            <td>
                                                <div class="fw-bold text-dark">@transaction.TransactionDate.ToString("MMM dd, yyyy")</div>
                                                <div class="fs-12 text-muted">@transaction.TransactionDate.ToString("HH:mm")</div>
                                            </td>
                                            <td>
                                                <div class="fw-bold text-dark">@transaction.InventoryItem?.Name</div>
                                                <div class="fs-12 text-muted">SKU: @transaction.InventoryItem?.SKU</div>
                                            </td>
                                            <td>
                                                @{
                                                    var typeClass = "";
                                                    var typeIcon = "";
                                                    switch (transaction.Type)
                                                    {
                                                        case ColorOasisSystemWeb.Models.Inventory.TransactionType.Receipt:
                                                            typeClass = "bg-soft-success text-success";
                                                            typeIcon = "feather-arrow-down-left";
                                                            break;
                                                        case ColorOasisSystemWeb.Models.Inventory.TransactionType.Issue:
                                                            typeClass = "bg-soft-danger text-danger";
                                                            typeIcon = "feather-arrow-up-right";
                                                            break;
                                                        case ColorOasisSystemWeb.Models.Inventory.TransactionType.Transfer:
                                                            typeClass = "bg-soft-info text-info";
                                                            typeIcon = "feather-shuffle";
                                                            break;
                                                        case ColorOasisSystemWeb.Models.Inventory.TransactionType.Adjustment:
                                                            typeClass = "bg-soft-warning text-warning";
                                                            typeIcon = "feather-edit-3";
                                                            break;
                                                    }
                                                }
                                                <span class="badge @typeClass">
                                                    <i class="@typeIcon me-1"></i>@transaction.Type
                                                </span>
                                            </td>
                                            <td>
                                                <div class="fs-12">@transaction.Warehouse?.Name</div>
                                            </td>
                                            <td>
                                                @{
                                                    var qtyClass = transaction.Type == ColorOasisSystemWeb.Models.Inventory.TransactionType.Receipt ? "text-success" : 
                                                                 transaction.Type == ColorOasisSystemWeb.Models.Inventory.TransactionType.Issue ? "text-danger" : "text-info";
                                                    var qtySign = transaction.Type == ColorOasisSystemWeb.Models.Inventory.TransactionType.Receipt ? "+" : 
                                                                transaction.Type == ColorOasisSystemWeb.Models.Inventory.TransactionType.Issue ? "-" : "";
                                                }
                                                <div class="@qtyClass fw-bold">
                                                    @<EMAIL> @transaction.InventoryItem?.UnitOfMeasure
                                                </div>
                                            </td>
                                            <td>
                                                <div class="fw-bold">@transaction.TotalCost.ToString("C")</div>
                                                @if (transaction.UnitCost > 0)
                                                {
                                                    <div class="fs-12 text-muted">@transaction.UnitCost.ToString("C") per unit</div>
                                                }
                                            </td>
                                            <td>
                                                @if (!string.IsNullOrEmpty(transaction.Reference))
                                                {
                                                    <div class="fs-12">@transaction.Reference</div>
                                                }
                                                @if (!string.IsNullOrEmpty(transaction.BatchLotNumber))
                                                {
                                                    <div class="fs-12 text-muted">Batch: @transaction.BatchLotNumber</div>
                                                }
                                            </td>
                                            <td>
                                                <div class="hstack gap-2 justify-content-end">
                                                    <a href="@Url.Action("Details", new { id = transaction.Id })" class="avatar-text avatar-md" data-bs-toggle="tooltip" title="View Details">
                                                        <i class="feather-eye"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- [ Main Content ] end -->
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            $('#transactionsTable').DataTable({
                responsive: true,
                pageLength: 25,
                order: [[0, 'desc']],
                columnDefs: [
                    { orderable: false, targets: [7] }
                ]
            });
        });
    </script>
}
