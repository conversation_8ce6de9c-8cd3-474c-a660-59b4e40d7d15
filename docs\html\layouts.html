<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="utf-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
		<meta name="author" content="Bootstrap-ecommerce by Vosidiy" />
		<title>Duralux - Layouts</title>
		<!-- ========== Start Favicon ========== -->
		<link type="image/x-icon" rel="shortcut icon" href="../assets/images/favicon.ico" />
		<!-- ========== End Favicon ========== -->
		<!-- ========== Start CSS Framework ========== -->
		<link type="text/css" rel="stylesheet" href="../assets/css/app.min.css" />
		<link type="text/css" rel="stylesheet" href="../assets/css/bootstrap.min.css" />
		<!-- ========== End CSS Framework ========== -->
		<!-- ========== Start Vandors CSS ========== -->
		<link type="text/css" rel="stylesheet" href="../assets/css/ui.css" />
		<link type="text/css" rel="stylesheet" href="../assets/css/rainbow.css" />
		<link type="text/css" rel="stylesheet" href="../assets/css/perfectScroll.css" />
		<link type="text/css" rel="stylesheet" href="../assets/fontawesome/css/all.min.css" />
		<link type="text/css" rel="stylesheet" href="../assets/css/bootstrap-icons.min.css " />
		<link type="text/css" rel="stylesheet" href="../assets/plugins/jstree/themes/default/style.min.css" />
		<!-- ========== End Vandors CSS ========== -->
		<!-- ========== Start Custom CSS ========== -->
		<link type="text/css" rel="stylesheet" href="../assets/css/style.css" />
		<link type="text/css" rel="stylesheet" href="../assets/css/responsive.css" media="only screen and (max-width: 1200px)" />
		<!-- ========== End Custom CSS ========== -->
	</head>
	<body>
		<div class="wrapper vh-100">
			<div class="row g-0">
				<!-- ========== Start Main Header ========== -->
				<div class="col-12">
					<header class="aside-header d-flex justify-content-between">
						<a href="./../documentations.html"><img class="title" src="./../assets/images/main-logo.png" alt="" /></a>
						<div class="hstack gap-3">
							<div class="d-lg-none">
								<a href="javascript:void(0);" class="wd-40 ht-40 bg-gray-100 d-flex justify-content-center align-items-center rounded-circle border lh-0 tx-dark" data-sidebar-toggler="sidebar-toggle">
									<i class="bi bi-list tx-18"></i>
								</a>
							</div>
							<div class="dropdown d-none d-sm-block">
								<a href="javascript:void(0);" class="wd-40 ht-40 bg-gray-100 d-flex justify-content-center align-items-center rounded-circle border lh-0 tx-dark" data-bs-toggle="dropdown" data-bs-offset="0,20">
									<i class="bi bi-lightbulb tx-15"></i>
								</a>
								<div class="dropdown-menu">
									<a class="dropdown-item" href="javascript:void(0);">
										<i class="tx-20 fa-brands fa-bootstrap me-3"></i>
										<span>Bootstrap</span>
									</a>
									<a class="dropdown-item disabled" href="javascript:void(0);">
										<i class="tx-20 fa-brands fa-react me-3"></i>
										<span>React <small class="tx-11">( coming soon )</small></span>
									</a>
									<a class="dropdown-item disabled" href="javascript:void(0);">
										<i class="tx-20 fa-brands fa-vuejs me-3"></i>
										<span>Vue <small class="tx-11">( coming soon )</small></span>
									</a>
									<a class="dropdown-item disabled" href="javascript:void(0);">
										<i class="tx-20 fa-brands fa-laravel me-3"></i>
										<span>Laravel <small class="tx-11">( coming soon )</small></span>
									</a>
									<a class="dropdown-item disabled" href="javascript:void(0);">
										<i class="tx-20 fa-brands fa-angular me-3"></i>
										<span>Angular <small class="tx-11">( coming soon )</small></span>
									</a>
									<div class="dropdown-divider"></div>
									<a class="dropdown-item" href="javascript:void(0);">
										<i class="fa-regular fa-life-ring me-3 tx-12"></i>
										<span class="tx-12 tx-bolder tx-uppercase">V_1.0.0</span>
									</a>
								</div>
							</div>
							<div class="hstack gap-1">
								<a href="../../crm/dist/index.html" target="_blank" class="btn btn-danger">Live Preview</a>
								<a href="https://themeforest.net/user/theme_ocean/" target="_blank" class="btn btn-primary d-none d-sm-flex">Get Support</a>
							</div>
						</div>
					</header>
				</div>
				<!-- ========== End Main Header ========== -->
				<!-- ========== Start Main Container ========== -->
				<div class="col-12">
					<div class="d-flex position-relative">
						<!-- ========== Start Aside ========== -->
						<aside class="aside-wrap wd-450">
							<div class="aside-content" id="navbar_aside">
								<div class="d-lg-none p-4 sidebar-toggler-header hstack justify-content-between">
									<h3 class="mb-0">Documentations</h3>
									<a href="javascript:void(0);" class="wd-40 ht-40 bg-gray-100 d-flex justify-content-center align-items-center rounded-circle lh-0 tx-dark float-end" data-sidebar-toggler="sidebar-toggle">
										<i class="bi bi-x tx-18"></i>
									</a>
								</div>
								<nav class="menu-wrap" id="sidebarMenuScroll">
									<ul class="menu-aside">
										<li class="tx-11 tx-dark tx-uppercase tx-bold manu-label">Layouts &rarr;</li>
										<li><a class="page-scroll nav-link" href="#navigation">Navigation</a></li>
										<li><a class="page-scroll nav-link" href="#header">Main Header</a></li>
										<li><a class="page-scroll nav-link" href="#sidebar">Content Sidebar</a></li>
										<li><a class="page-scroll nav-link" href="#subheader">Content Header</a></li>
										<li><a class="page-scroll nav-link" href="#maincontent">Main Content</a></li>
										<li><a class="page-scroll nav-link" href="#footer">Main Footer</a></li>
									</ul>
								</nav>
								<hr class="tx-gray-400" />
								<div class="hstack flex-column pd-30 bg-white">
									<div class="w-100 h-100">
										<a href="../documentation.html" class="p-4 bg-gray-100 bd bd-gray-2 rounded mb-4 tx-12 tx-dark fw-bold d-block w-100" style="background: #f3f3f5">Back Home &rarr;</a>
										<a href="get-started.html" class="p-4 bg-gray-100 bd bd-gray-2 rounded mb-4 tx-12 tx-dark fw-bold d-block w-100" style="background: #f3f3f5">Get Started &rarr;</a>
										<a href="installation.html" class="p-4 bg-gray-100 bd bd-gray-2 rounded mb-4 tx-12 tx-dark fw-bold d-block w-100" style="background: #f3f3f5">Installation &rarr;</a>
										<a href="folder-structure.html" class="p-4 bg-gray-100 bd bd-gray-2 rounded mb-4 tx-12 tx-dark fw-bold d-block w-100" style="background: #f3f3f5">Folder Structure &rarr;</a>
										<a href="configuration.html" class="p-4 bg-gray-100 bd bd-gray-2 rounded mb-4 tx-12 tx-dark fw-bold d-block w-100" style="background: #f3f3f5">Configuration &rarr;</a>
										<a href="credit-resource.html" class="p-4 bg-gray-100 bd bd-gray-2 rounded mb-4 tx-12 tx-dark fw-bold d-block w-100" style="background: #f3f3f5">Credit & Resource &rarr;</a>
										<a href="changelog.html" class="p-4 bg-gray-100 bd bd-gray-2 rounded mb-4 tx-12 tx-dark fw-bold d-block w-100" style="background: #f3f3f5">Changelogs &rarr;</a>
									</div>
									<div class="pd-t-30 w-100">
										<a href="https://themeforest.net/user/theme_ocean/" target="_blank" class="btn btn-primary w-100">Download Now</a>
									</div>
								</div>
							</div>
						</aside>
						<!-- ========== End Aside ========== -->
						<!-- ========== Start Main Wrapper ========== -->
						<main class="main-wrap w-100">
							<div class="main-content container pb-4">
								<!-- ========== Start Navigation ========== -->
								<section id="navigation">
									<div class="card mb-4">
										<div class="card-header">
											<h5 class="tx-bold tx-dark mb-0">Navigation</h5>
										</div>
										<div class="card-body">
											<pre><code class="language-html">&lt;!--! ================================================================ !--&gt;
&lt;!--! [Start] Navigation Manu !--&gt;
&lt;!--! ================================================================ !--&gt;
&lt;nav class="nxl-sidebar"&gt;
	&lt;div class="navbar-wrapper"&gt;

	&lt;!--! Navigation Menu !--&gt;

	&lt;/div"&gt;
&lt;/nav&gt;
&lt;!--! ================================================================ !--&gt;
&lt;!--! [End]  Navigation Manu !--&gt;
&lt;!--! ================================================================ !--&gt;</code></pre>
										</div>
									</div>
								</section>
								<!-- ========== End Navigation ========== -->
								<!-- ========== Start Header ========== -->
								<section id="header">
									<div class="card mb-4">
										<div class="card-header">
											<h5 class="tx-bold tx-dark mb-0">Main Header</h5>
										</div>
										<div class="card-body">
											<pre><code class="language-html">&lt;!--! ================================================================ !--&gt;
&lt;!--! [Start] Header !--&gt;
&lt;!--! ================================================================ !--&gt;
&lt;header class="nxl-header"&gt;
	&lt;div class="header-wrapper"&gt;

	&lt;!--!  Main Header !--&gt;

	&lt;/div&gt;
&lt;/header&gt;
&lt;!--! ================================================================ !--&gt;
&lt;!--! [End] Header !--&gt;
&lt;!--! ================================================================ !--&gt;</code></pre>
										</div>
									</div>
								</section>
								<!-- ========== End Header ========== -->
								<!-- ========== Start Sidebar ========== -->
								<section id="sidebar">
									<div class="card mb-4">
										<div class="card-header">
											<h5 class="tx-bold tx-dark mb-0">Content Sidebar</h5>
										</div>
										<div class="card-body">
											<pre><code class="language-html">&lt;!-- [ Content Sidebar ] start --&gt;
&lt;div class="content-sidebar" data-scrollbar-target="#psScrollbarInit"&gt;

&lt;!--!  Content Sidebar !--&gt;

&lt;/div&gt;
&lt;!-- [ Content Sidebar  ] end --&gt;</code></pre>
										</div>
									</div>
								</section>
								<!-- ========== End Sidebar ========== -->
								<!-- ========== Start Content Header ========== -->
								<section id="subheader">
									<div class="card mb-4">
										<div class="card-header">
											<h5 class="tx-bold tx-dark mb-0">Content Header</h5>
										</div>
										<div class="card-body">
											<pre><code class="language-html">&lt;!--! ================================================================ !--&gt;
&lt;!--! [Start] Content Header  !--&gt;
&lt;!--! ================================================================ !--&gt;
&lt;div class="content-area-header "&gt;

&lt;!--!  Content Header !--&gt;

&lt;/div&gt;
&lt;!--! ================================================================ !--&gt;
&lt;!--! [End] Content Header !--&gt;
&lt;!--! ================================================================ !--&gt;</code></pre>
										</div>
									</div>
								</section>
								<!-- ========== End Content Header ========== -->
								<!-- ========== Start Main Content ========== -->
								<section id="maincontent">
									<div class="card mb-4">
										<div class="card-header">
											<h5 class="tx-bold tx-dark mb-0">Main Content</h5>
										</div>
										<div class="card-body">
											<pre><code class="language-html">&lt;!--! ================================================================ !--&gt;
&lt;!--! [Start] Main Content !--&gt;
&lt;!--! ================================================================ !--&gt;
&lt;main class="nxl-container apps-container apps-chat"&gt;
	&lt;div class="nxl-content without-header nxl-full-content"&gt;
		&lt;!-- [ Main Content ] start --&gt;
		&lt;div&gt; class="main-content"&gt;

		&lt;!--!  Main Content !--&gt;
			
		&lt;/div&gt;
		&lt;!-- [ Main Content ] end --&gt;
	&lt;/div&gt;
&lt;/main&gt;
&lt;!--! ================================================================ !--&gt;
&lt;!--! [End] Main Content !--&gt;
&lt;!--! ================================================================ !--&gt;</code></pre>
										</div>
									</div>
								</section>
								<!-- ========== End Main Content ========== -->
								<!-- ========== Start Footer ========== -->
								<section id="footer">
									<div class="card mb-4">
										<div class="card-header">
											<h5 class="tx-bold tx-dark mb-0">Main Footer</h5>
										</div>
										<div class="card-body">
											<pre><code class="language-html">&lt;!-- [ Footer ] start --&gt;
&lt;footer class="footer"&gt;
	&lt;p class="fs-11 text-muted font-medium text-uppercase mb-0 copyright"&gt;
		&lt;span&gt;Copyright ©&lt;/span&gt;
		&lt;script&gt;
			document.write(new Date().getFullYear());
		&lt;/script&gt;
	&lt;/p&gt;
	&lt;div class="d-flex align-items-center gap-4"&gt;
		&lt;a href="javascript:void(0);" class="fs-11 font-semibold text-uppercase"&gt;Help&lt;/a&gt;
		&lt;a href="javascript:void(0);" class="fs-11 font-semibold text-uppercase"&gt;Terms&lt;/a&gt;
		&lt;a href="javascript:void(0);" class="fs-11 font-semibold text-uppercase"&gt;Privacy&lt;/a&gt;
	&lt;/div&gt;
&lt;/footer&gt;
&lt;!-- [ Footer ] end --&gt;</code></pre>
										</div>
									</div>
								</section>
								<!-- ========== End Footer ========== -->
								<hr class="bd-gray-400 mg-y-50" />
								<!-- ========== Start Quick Navigation ========== -->
								<div class="d-flex gap-5 justify-content-between text-justify">
									<a href="configuration.html" class="p-5 bg-gray-100 bd bd-gray-2 rounded mb-4 tx-12 tx-dark fw-bold d-block w-100" style="background: #f3f3f5"> &larr; Configuration</a>
									<a href="credit-resource.html" class="p-5 bg-gray-100 bd bd-gray-2 rounded mb-4 tx-12 tx-dark fw-bold d-block w-100 text-end" style="background: #f3f3f5">Creadit & Resource &rarr;</a>
								</div>
								<!-- ========== End Quick Navigation ========== -->
							</div>
							<!-- ========== Start Footer ========== -->
							<footer class="copyright">
								<p class="tx-12 tx-spacing-1">
									<span>Copyright &copy;</span>
									<script>
										document.write(new Date().getFullYear());
									</script>
									<span class="tx-gray-300 mx-2">|</span>
									<span>By: <a href="https://themeforest.net/user/theme_ocean">theme_ocean</a></span>
								</p>
							</footer>
							<!-- ========== End Footer ========== -->
						</main>
						<!-- ========== End Main Wrapper ========== -->
					</div>
				</div>
				<!-- ========== End Main Container ========== -->
			</div>
		</div>
		<!-- ========== Start Footer Script ========== -->
		<script src="../assets/js/jquery.min.js"></script>
		<script src="../assets/js/bootstrap.min.js"></script>
		<script src="../assets/js/highlight.pack.js"></script>
		<script src="../assets/js/perfectScroll.js"></script>
		<script src="../assets/plugins/jstree/jstree.min.js"></script>
		<script src="../assets/js/script.js"></script>
		<!-- ========== End Footer Script ========== -->
	</body>
</html>
