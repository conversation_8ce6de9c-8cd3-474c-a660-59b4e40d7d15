using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using ColorOasisSystemWeb.Authorization;

namespace ColorOasisSystemWeb.Controllers
{
    [Authorize]
    public class ShipmentsController : Controller
    {
        [Authorize(Permissions.Sales.Ship)]
        public IActionResult Index()
        {
            ViewBag.Title = "Shipments";
            ViewBag.Message = "Manage shipments, delivery tracking, and logistics coordination.";
            return View("~/Views/Shared/ComingSoon.cshtml");
        }
    }
}
