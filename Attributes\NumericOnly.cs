﻿using System.ComponentModel.DataAnnotations;
using System.Text.RegularExpressions;
using Microsoft.Extensions.Localization;

namespace ColorOasisSystemWeb
{
    public class NumericOnlyAttribute : ValidationAttribute
    {
        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            // Get the localizer from the service provider
            var localizerFactory = (IStringLocalizerFactory)validationContext.GetService(typeof(IStringLocalizerFactory));
            var localizer = localizerFactory?.Create(typeof(SharedResource));

            // Adjust regex based on whether you want to allow leading zeros
            string pattern = @"^\d+(\.\d+)?(\/\d+)?$";

            if (value == null || !Regex.IsMatch(value.ToString(), pattern))
            {
                // Return a localized error message or fall back to the provided ErrorMessage or a default
                string errorMessage = ErrorMessage;
                if (string.IsNullOrEmpty(errorMessage) && localizer != null)
                {
                    errorMessage = localizer["Validation.NumericOnly"];
                }
                return new ValidationResult(errorMessage ?? "Please enter a valid number or fraction.");
            }
            return ValidationResult.Success;
        }
    }
}
