using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using ColorOasisSystemWeb.Data;
using ColorOasisSystemWeb.Models.Accounting;
using ColorOasisSystemWeb.Authorization;
using Microsoft.AspNetCore.Identity;
using ColorOasisSystemWeb.Models;
using ColorOasisSystemWeb.AdvancedNotification.Services;
using ColorOasisSystemWeb.Enums;
using Microsoft.AspNetCore.Mvc.Rendering;

namespace ColorOasisSystemWeb.Controllers
{
    [Authorize]
    public class InvoicesController : Controller
    {
        private readonly AppDbContext _context;
        private readonly UserManager<User> _userManager;
        private readonly NotificationService _notificationService;
        private readonly ILogger<InvoicesController> _logger;

        public InvoicesController(
            AppDbContext context,
            UserManager<User> userManager,
            NotificationService notificationService,
            ILogger<InvoicesController> logger)
        {
            _context = context;
            _userManager = userManager;
            _notificationService = notificationService;
            _logger = logger;
        }

        // GET: Invoices
        [Authorize(Permissions.Invoices.View)]
        public async Task<IActionResult> Index()
        {
            try
            {
                var invoices = await _context.Invoices
                    .Include(i => i.Client)
                    .Include(i => i.Company)
                    .Include(i => i.SalesOrder)
                    .Include(i => i.Items)
                    .OrderByDescending(i => i.InvoiceDate)
                    .ToListAsync();

                return View(invoices);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving invoices");
                TempData["Error"] = "An error occurred while retrieving invoices.";
                return View(new List<Invoice>());
            }
        }

        // GET: Invoices/Details/5
        [Authorize(Permissions.Invoices.View)]
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var invoice = await _context.Invoices
                .Include(i => i.Client)
                .Include(i => i.Company)
                .Include(i => i.SalesOrder)
                .Include(i => i.Quotation)
                .Include(i => i.Items)
                .ThenInclude(item => item.Service)
                .Include(i => i.Payments)
                .Include(i => i.History)
                .FirstOrDefaultAsync(m => m.Id == id);

            if (invoice == null)
            {
                return NotFound();
            }

            return View(invoice);
        }

        // GET: Invoices/Create
        [Authorize(Permissions.Invoices.Create)]
        public IActionResult Create()
        {
            ViewData["ClientId"] = new SelectList(_context.Clients.Where(c => !c.IsDeleted), "Id", "Name");
            ViewData["CompanyId"] = new SelectList(_context.Companies.Where(c => !c.IsDeleted), "Id", "Name");
            ViewData["SalesOrderId"] = new SelectList(_context.SalesOrders.Where(s => s.Status == SalesOrderStatus.Confirmed), "Id", "OrderNumber");
            ViewData["QuotationId"] = new SelectList(_context.Quotations.Where(q => !q.IsDeleted), "Id", "Code");
            return View();
        }

        // POST: Invoices/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        [Authorize(Permissions.Invoices.Create)]
        public async Task<IActionResult> Create([Bind("ClientId,CompanyId,SalesOrderId,QuotationId,DueDate,PaymentTerms,Notes,TaxRate,DiscountAmount,Currency,ExchangeRate")] Invoice invoice)
        {
            if (ModelState.IsValid)
            {
                try
                {
                    // Generate invoice number
                    invoice.InvoiceNumber = await GenerateInvoiceNumber();
                    invoice.InvoiceDate = DateTime.UtcNow;
                    invoice.Status = InvoiceStatus.Draft;

                    var currentUser = await _userManager.GetUserAsync(User);
                    invoice.CreatedBy = currentUser?.UserName ?? "System";
                    invoice.CreatedAt = DateTime.UtcNow;
                    invoice.UpdatedAt = DateTime.UtcNow;

                    _context.Add(invoice);
                    await _context.SaveChangesAsync();

                    // Create invoice history entry
                    var historyEntry = new InvoiceHistory
                    {
                        InvoiceId = invoice.Id,
                        Action = "Created",
                        Description = "Invoice created",
                        ActionDate = DateTime.UtcNow,
                        PerformedBy = invoice.CreatedBy,
                        IpAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "",
                        UserAgent = HttpContext.Request.Headers["User-Agent"].ToString()
                    };
                    _context.InvoiceHistory.Add(historyEntry);
                    await _context.SaveChangesAsync();

                    // Send notification
                    if (currentUser != null)
                    {
                        await _notificationService.CreateAndSendNotification(
                            ModelType.Invoice,
                            ProcessType.Add,
                            invoice.Id,
                            currentUser);
                    }

                    TempData["Success"] = "Invoice created successfully.";
                    return RedirectToAction(nameof(Details), new { id = invoice.Id });
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error creating invoice");
                    TempData["Error"] = "An error occurred while creating the invoice.";
                }
            }

            ViewData["ClientId"] = new SelectList(_context.Clients.Where(c => !c.IsDeleted), "Id", "Name", invoice.ClientId);
            ViewData["CompanyId"] = new SelectList(_context.Companies.Where(c => !c.IsDeleted), "Id", "Name", invoice.CompanyId);
            ViewData["SalesOrderId"] = new SelectList(_context.SalesOrders.Where(s => s.Status == SalesOrderStatus.Confirmed), "Id", "OrderNumber", invoice.SalesOrderId);
            ViewData["QuotationId"] = new SelectList(_context.Quotations.Where(q => !q.IsDeleted), "Id", "Code", invoice.QuotationId);
            return View(invoice);
        }

        // GET: Invoices/Edit/5
        [Authorize(Permissions.Invoices.Edit)]
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var invoice = await _context.Invoices.FindAsync(id);
            if (invoice == null)
            {
                return NotFound();
            }

            // Only allow editing of draft invoices
            if (invoice.Status != InvoiceStatus.Draft)
            {
                TempData["Error"] = "Only draft invoices can be edited.";
                return RedirectToAction(nameof(Details), new { id });
            }

            ViewData["ClientId"] = new SelectList(_context.Clients.Where(c => !c.IsDeleted), "Id", "Name", invoice.ClientId);
            ViewData["CompanyId"] = new SelectList(_context.Companies.Where(c => !c.IsDeleted), "Id", "Name", invoice.CompanyId);
            ViewData["SalesOrderId"] = new SelectList(_context.SalesOrders.Where(s => s.Status == SalesOrderStatus.Confirmed), "Id", "OrderNumber", invoice.SalesOrderId);
            ViewData["QuotationId"] = new SelectList(_context.Quotations.Where(q => !q.IsDeleted), "Id", "Code", invoice.QuotationId);
            return View(invoice);
        }

        // POST: Invoices/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        [Authorize(Permissions.Invoices.Edit)]
        public async Task<IActionResult> Edit(int id, [Bind("Id,InvoiceNumber,ClientId,CompanyId,SalesOrderId,QuotationId,DueDate,PaymentTerms,Notes,TaxRate,DiscountAmount,Currency,ExchangeRate,Status,InvoiceDate,CreatedBy,CreatedAt")] Invoice invoice)
        {
            if (id != invoice.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    invoice.UpdatedAt = DateTime.UtcNow;
                    var currentUser = await _userManager.GetUserAsync(User);
                    invoice.UpdatedBy = currentUser?.UserName ?? "System";

                    _context.Update(invoice);

                    // Create invoice history entry
                    var historyEntry = new InvoiceHistory
                    {
                        InvoiceId = invoice.Id,
                        Action = "Updated",
                        Description = "Invoice updated",
                        ActionDate = DateTime.UtcNow,
                        PerformedBy = invoice.UpdatedBy,
                        IpAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "",
                        UserAgent = HttpContext.Request.Headers["User-Agent"].ToString()
                    };
                    _context.InvoiceHistory.Add(historyEntry);

                    await _context.SaveChangesAsync();

                    // Send notification
                    if (currentUser != null)
                    {
                        await _notificationService.CreateAndSendNotification(
                            ModelType.Invoice,
                            ProcessType.Edit,
                            invoice.Id,
                            currentUser);
                    }

                    TempData["Success"] = "Invoice updated successfully.";
                    return RedirectToAction(nameof(Details), new { id });
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!InvoiceExists(invoice.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error updating invoice");
                    TempData["Error"] = "An error occurred while updating the invoice.";
                }
            }

            ViewData["ClientId"] = new SelectList(_context.Clients.Where(c => !c.IsDeleted), "Id", "Name", invoice.ClientId);
            ViewData["CompanyId"] = new SelectList(_context.Companies.Where(c => !c.IsDeleted), "Id", "Name", invoice.CompanyId);
            ViewData["SalesOrderId"] = new SelectList(_context.SalesOrders.Where(s => s.Status == SalesOrderStatus.Confirmed), "Id", "OrderNumber", invoice.SalesOrderId);
            ViewData["QuotationId"] = new SelectList(_context.Quotations.Where(q => !q.IsDeleted), "Id", "Code", invoice.QuotationId);
            return View(invoice);
        }

        // POST: Invoices/Send/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        [Authorize(Permissions.Invoices.Edit)]
        public async Task<IActionResult> Send(int id)
        {
            try
            {
                var invoice = await _context.Invoices
                    .Include(i => i.Items)
                    .FirstOrDefaultAsync(i => i.Id == id);

                if (invoice == null)
                {
                    return NotFound();
                }

                if (invoice.Status != InvoiceStatus.Draft)
                {
                    TempData["Error"] = "Only draft invoices can be sent.";
                    return RedirectToAction(nameof(Details), new { id });
                }

                if (!invoice.Items.Any())
                {
                    TempData["Error"] = "Cannot send invoice without items.";
                    return RedirectToAction(nameof(Details), new { id });
                }

                invoice.Status = InvoiceStatus.Sent;
                invoice.SentDate = DateTime.UtcNow;

                var currentUser = await _userManager.GetUserAsync(User);
                invoice.SentBy = currentUser?.UserName ?? "System";
                invoice.UpdatedAt = DateTime.UtcNow;
                invoice.UpdatedBy = currentUser?.UserName ?? "System";

                // Calculate totals
                invoice.Subtotal = invoice.Items.Sum(i => i.Quantity * i.UnitPrice);
                invoice.TaxAmount = invoice.Subtotal * (invoice.TaxRate / 100);
                invoice.TotalAmount = invoice.Subtotal + invoice.TaxAmount - invoice.DiscountAmount;

                // Create invoice history entry
                var historyEntry = new InvoiceHistory
                {
                    InvoiceId = invoice.Id,
                    Action = "Sent",
                    Description = "Invoice sent to customer",
                    ActionDate = DateTime.UtcNow,
                    PerformedBy = invoice.SentBy,
                    IpAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "",
                    UserAgent = HttpContext.Request.Headers["User-Agent"].ToString()
                };
                _context.InvoiceHistory.Add(historyEntry);

                await _context.SaveChangesAsync();

                TempData["Success"] = "Invoice sent successfully.";
                return RedirectToAction(nameof(Details), new { id });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending invoice");
                TempData["Error"] = "An error occurred while sending the invoice.";
                return RedirectToAction(nameof(Details), new { id });
            }
        }

        // Helper method to generate invoice number
        private async Task<string> GenerateInvoiceNumber()
        {
            var today = DateTime.Today;
            var prefix = $"INV{today:yyyyMM}";

            var lastInvoice = await _context.Invoices
                .Where(i => i.InvoiceNumber.StartsWith(prefix))
                .OrderByDescending(i => i.InvoiceNumber)
                .FirstOrDefaultAsync();

            int nextNumber = 1;
            if (lastInvoice != null)
            {
                var numberPart = lastInvoice.InvoiceNumber.Substring(prefix.Length);
                if (int.TryParse(numberPart, out int lastNumber))
                {
                    nextNumber = lastNumber + 1;
                }
            }

            return $"{prefix}{nextNumber:D4}";
        }

        private bool InvoiceExists(int id)
        {
            return _context.Invoices.Any(e => e.Id == id);
        }
    }
}
