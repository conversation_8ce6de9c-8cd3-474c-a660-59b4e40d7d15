@model IEnumerable<ColorOasisSystemWeb.Models.Inventory.StockAdjustment>

@{
    ViewData["Title"] = "Stock Adjustments";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="nxl-content">
    <!-- [ page-header ] start -->
    <div class="page-header">
        <div class="page-header-left d-flex align-items-center">
            <div class="page-header-title">
                <h5 class="m-b-10">Stock Adjustments</h5>
            </div>
            <ul class="breadcrumb">
                <li class="breadcrumb-item"><a href="@Url.Action("Index", "Home")">Home</a></li>
                <li class="breadcrumb-item">ERP</li>
                <li class="breadcrumb-item">Inventory</li>
                <li class="breadcrumb-item">Stock Adjustments</li>
            </ul>
        </div>
        <div class="page-header-right ms-auto">
            <div class="page-header-right-items">
                <div class="d-flex d-md-none">
                    <a href="javascript:void(0)" class="page-header-right-close-toggle">
                        <i class="feather-arrow-left me-2"></i>
                        <span>Back</span>
                    </a>
                </div>
                <div class="d-flex align-items-center gap-2 page-header-right-items-wrapper">
                    <a href="@Url.Action("Create")" class="btn btn-primary">
                        <i class="feather-plus me-2"></i>
                        <span>New Adjustment</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
    <!-- [ page-header ] end -->

    <!-- [ Main Content ] start -->
    <div class="main-content">
        <div class="row">
            <div class="col-lg-12">
                <div class="card stretch stretch-full">
                    <div class="card-header">
                        <h5 class="card-title">Stock Adjustments</h5>
                        <div class="card-header-action">
                            <div class="card-header-btn">
                                <div data-bs-toggle="tooltip" title="Refresh">
                                    <a href="javascript:void(0);" class="avatar-text avatar-md" onclick="location.reload();">
                                        <i class="feather-refresh-cw"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body custom-card-action p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0" id="adjustmentsTable">
                                <thead>
                                    <tr>
                                        <th>Adjustment #</th>
                                        <th>Item</th>
                                        <th>Warehouse</th>
                                        <th>Reason</th>
                                        <th>Quantity Change</th>
                                        <th>Value Impact</th>
                                        <th>Status</th>
                                        <th class="text-end">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var adjustment in Model)
                                    {
                                        <tr>
                                            <td>
                                                <div class="fw-bold text-dark">@adjustment.AdjustmentNumber</div>
                                                <div class="fs-12 text-muted">@adjustment.AdjustmentDate.ToString("MMM dd, yyyy")</div>
                                            </td>
                                            <td>
                                                <div class="fw-bold text-dark">@adjustment.InventoryItem?.Name</div>
                                                <div class="fs-12 text-muted">SKU: @adjustment.InventoryItem?.SKU</div>
                                            </td>
                                            <td>
                                                <div class="fs-12">@adjustment.Warehouse?.Name</div>
                                            </td>
                                            <td>
                                                <span class="badge bg-soft-info text-info">@adjustment.Reason</span>
                                            </td>
                                            <td>
                                                @{
                                                    var qtyChange = adjustment.AdjustmentQuantity;
                                                    var qtyClass = qtyChange > 0 ? "text-success" : qtyChange < 0 ? "text-danger" : "text-muted";
                                                    var qtyIcon = qtyChange > 0 ? "feather-arrow-up" : qtyChange < 0 ? "feather-arrow-down" : "feather-minus";
                                                }
                                                <div class="@qtyClass">
                                                    <i class="@qtyIcon me-1"></i>
                                                    <span class="fw-bold">@Math.Abs(qtyChange)</span>
                                                </div>
                                                <div class="fs-12 text-muted">
                                                    @adjustment.QuantityBefore → @adjustment.QuantityAfter
                                                </div>
                                            </td>
                                            <td>
                                                @{
                                                    var valueImpact = adjustment.TotalValueImpact;
                                                    var valueClass = valueImpact > 0 ? "text-success" : valueImpact < 0 ? "text-danger" : "text-muted";
                                                }
                                                <div class="@valueClass fw-bold">
                                                    @valueImpact.ToString("C")
                                                </div>
                                            </td>
                                            <td>
                                                @if (adjustment.IsApproved)
                                                {
                                                    <span class="badge bg-soft-success text-success">Approved</span>
                                                    @if (adjustment.ApprovalDate.HasValue)
                                                    {
                                                        <div class="fs-12 text-muted">@adjustment.ApprovalDate.Value.ToString("MMM dd")</div>
                                                    }
                                                }
                                                else
                                                {
                                                    <span class="badge bg-soft-warning text-warning">Pending</span>
                                                }
                                            </td>
                                            <td>
                                                <div class="hstack gap-2 justify-content-end">
                                                    <a href="@Url.Action("Details", new { id = adjustment.Id })" class="avatar-text avatar-md" data-bs-toggle="tooltip" title="View Details">
                                                        <i class="feather-eye"></i>
                                                    </a>
                                                    @if (!adjustment.IsApproved)
                                                    {
                                                        <a href="@Url.Action("Edit", new { id = adjustment.Id })" class="avatar-text avatar-md" data-bs-toggle="tooltip" title="Edit">
                                                            <i class="feather-edit-3"></i>
                                                        </a>
                                                        <a href="@Url.Action("Approve", new { id = adjustment.Id })" class="avatar-text avatar-md" data-bs-toggle="tooltip" title="Approve">
                                                            <i class="feather-check"></i>
                                                        </a>
                                                    }
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- [ Main Content ] end -->
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            $('#adjustmentsTable').DataTable({
                responsive: true,
                pageLength: 25,
                order: [[0, 'desc']],
                columnDefs: [
                    { orderable: false, targets: [7] }
                ]
            });
        });
    </script>
}
