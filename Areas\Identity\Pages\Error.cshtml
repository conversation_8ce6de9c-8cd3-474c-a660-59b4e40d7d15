@page
@model ErrorModel
@{
    ViewData["Title"] = "Error";
}

<h1 class="text-danger">@SharedLocalizer["Identity.Error.Error"]</h1>
<h2 class="text-danger">@SharedLocalizer["Identity.Error.ErrorOccurred"]</h2>

@if (Model.ShowRequestId)
{
    <p>
        <strong>Request ID:</strong> <code>@Model.RequestId</code>
    </p>
}

<h3>@SharedLocalizer["Identity.Error.DevelopmentMode"]</h3>
<p>
    @SharedLocalizer["Identity.Error.DetailedInfo"]
</p>
<p>
    <strong>@SharedLocalizer["Identity.Error.NotForProduction"]</strong>, as it can result in sensitive information from exceptions being displayed to end users. For local debugging, development environment can be enabled by setting the <strong>ASPNETCORE_ENVIRONMENT</strong> environment variable to <strong>Development</strong>, and restarting the application.
</p>
