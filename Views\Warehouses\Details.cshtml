@model ColorOasisSystemWeb.Models.Inventory.Warehouse

@{
    ViewData["Title"] = "Warehouse Details";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="nxl-content">
    <!-- [ page-header ] start -->
    <div class="page-header">
        <div class="page-header-left d-flex align-items-center">
            <div class="page-header-title">
                <h5 class="m-b-10">Warehouse Details</h5>
            </div>
            <ul class="breadcrumb">
                <li class="breadcrumb-item"><a href="@Url.Action("Index", "Home")">Home</a></li>
                <li class="breadcrumb-item">ERP</li>
                <li class="breadcrumb-item">Inventory</li>
                <li class="breadcrumb-item"><a href="@Url.Action("Index")">Warehouses</a></li>
                <li class="breadcrumb-item">Details</li>
            </ul>
        </div>
        <div class="page-header-right ms-auto">
            <div class="page-header-right-items">
                <div class="d-flex align-items-center gap-2 page-header-right-items-wrapper">
                    <a href="@Url.Action("Edit", new { id = Model.Id })" class="btn btn-primary">
                        <i class="feather-edit-3 me-2"></i>
                        <span>Edit Warehouse</span>
                    </a>
                    <a href="@Url.Action("Index")" class="btn btn-outline-primary">
                        <i class="feather-arrow-left me-2"></i>
                        <span>Back to List</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
    <!-- [ page-header ] end -->

    <!-- [ Main Content ] start -->
    <div class="main-content">
        <div class="row">
            <!-- Basic Information -->
            <div class="col-lg-8">
                <div class="card stretch stretch-full">
                    <div class="card-header">
                        <h5 class="card-title">Warehouse Information</h5>
                        <div class="card-header-action">
                            @if (Model.IsDefault)
                            {
                                <span class="badge bg-soft-primary text-primary me-2">Default</span>
                            }
                            @if (Model.IsActive)
                            {
                                <span class="badge bg-soft-success text-success">Active</span>
                            }
                            else
                            {
                                <span class="badge bg-soft-danger text-danger">Inactive</span>
                            }
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Warehouse Code</label>
                                    <div class="fw-bold">@Model.Code</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Warehouse Name</label>
                                    <div class="fw-bold">@Model.Name</div>
                                </div>
                            </div>
                            @if (!string.IsNullOrEmpty(Model.Address))
                            {
                                <div class="col-12">
                                    <div class="mb-3">
                                        <label class="form-label text-muted">Address</label>
                                        <div>@Model.Address</div>
                                    </div>
                                </div>
                            }
                            @if (!string.IsNullOrEmpty(Model.Manager))
                            {
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label text-muted">Manager</label>
                                        <div>@Model.Manager</div>
                                    </div>
                                </div>
                            }
                            @if (!string.IsNullOrEmpty(Model.Phone))
                            {
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label text-muted">Phone</label>
                                        <div><a href="tel:@Model.Phone">@Model.Phone</a></div>
                                    </div>
                                </div>
                            }
                            @if (!string.IsNullOrEmpty(Model.Email))
                            {
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label text-muted">Email</label>
                                        <div><a href="mailto:@Model.Email">@Model.Email</a></div>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                </div>

                <!-- Inventory Summary -->
                <div class="card stretch stretch-full">
                    <div class="card-header">
                        <h5 class="card-title">Inventory Summary</h5>
                    </div>
                    <div class="card-body">
                        @if (Model.InventoryLocations?.Any() == true)
                        {
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <div class="fs-4 fw-bold text-primary">@Model.InventoryLocations.Count()</div>
                                        <div class="text-muted">Items Stored</div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <div class="fs-4 fw-bold text-success">@Model.InventoryLocations.Sum(l => l.QuantityOnHand)</div>
                                        <div class="text-muted">Total Quantity</div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <div class="fs-4 fw-bold text-warning">@Model.InventoryLocations.Count(l => l.QuantityOnHand <= l.InventoryItem.ReorderPoint)</div>
                                        <div class="text-muted">Low Stock Items</div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <div class="fs-4 fw-bold text-info">@Model.InventoryLocations.Sum(l => l.QuantityOnHand * l.InventoryItem.AverageCost).ToString("C")</div>
                                        <div class="text-muted">Total Value</div>
                                    </div>
                                </div>
                            </div>

                            <hr class="my-4">

                            <h6 class="mb-3">Recent Inventory Items</h6>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>SKU</th>
                                            <th>Item Name</th>
                                            <th>Bin Location</th>
                                            <th>Quantity</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var location in Model.InventoryLocations.Take(10))
                                        {
                                            <tr>
                                                <td>@location.InventoryItem?.SKU</td>
                                                <td>@location.InventoryItem?.Name</td>
                                                <td>@location.BinLocation</td>
                                                <td class="fw-bold">@location.QuantityOnHand @location.InventoryItem?.UnitOfMeasure</td>
                                                <td>
                                                    @if (location.QuantityOnHand <= location.InventoryItem?.ReorderPoint)
                                                    {
                                                        <span class="badge bg-soft-danger text-danger">Low Stock</span>
                                                    }
                                                    else if (location.QuantityOnHand <= location.InventoryItem?.SafetyStock)
                                                    {
                                                        <span class="badge bg-soft-warning text-warning">Warning</span>
                                                    }
                                                    else
                                                    {
                                                        <span class="badge bg-soft-success text-success">Normal</span>
                                                    }
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                            @if (Model.InventoryLocations.Count() > 10)
                            {
                                <div class="text-center mt-3">
                                    <a href="#" class="btn btn-outline-primary btn-sm">View All Items (@Model.InventoryLocations.Count())</a>
                                </div>
                            }
                        }
                        else
                        {
                            <div class="text-center py-4">
                                <i class="feather-package" style="font-size: 3rem; color: #6c757d;"></i>
                                <h6 class="mt-3 text-muted">No inventory items in this warehouse</h6>
                                <p class="text-muted">Items will appear here once they are assigned to this warehouse.</p>
                            </div>
                        }
                    </div>
                </div>
            </div>

            <!-- Quick Actions & Stats -->
            <div class="col-lg-4">
                <div class="card stretch stretch-full">
                    <div class="card-header">
                        <h5 class="card-title">Quick Actions</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="#" class="btn btn-outline-primary">
                                <i class="feather-plus me-2"></i>Add Inventory Item
                            </a>
                            <a href="#" class="btn btn-outline-info">
                                <i class="feather-shuffle me-2"></i>Transfer Stock
                            </a>
                            <a href="#" class="btn btn-outline-warning">
                                <i class="feather-edit-3 me-2"></i>Stock Adjustment
                            </a>
                            <a href="#" class="btn btn-outline-success">
                                <i class="feather-bar-chart-2 me-2"></i>Inventory Report
                            </a>
                        </div>
                    </div>
                </div>

                <div class="card stretch stretch-full">
                    <div class="card-header">
                        <h5 class="card-title">Warehouse Settings</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label text-muted">Status</label>
                            <div>
                                @if (Model.IsActive)
                                {
                                    <span class="badge bg-soft-success text-success">Active</span>
                                }
                                else
                                {
                                    <span class="badge bg-soft-danger text-danger">Inactive</span>
                                }
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-muted">Default Warehouse</label>
                            <div>
                                @if (Model.IsDefault)
                                {
                                    <span class="badge bg-soft-primary text-primary">Yes</span>
                                }
                                else
                                {
                                    <span class="badge bg-soft-secondary text-secondary">No</span>
                                }
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Audit Information -->
        <div class="row">
            <div class="col-12">
                <div class="card stretch stretch-full">
                    <div class="card-header">
                        <h5 class="card-title">Audit Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Created Date</label>
                                    <div>@Model.CreatedAt.ToString("MMM dd, yyyy HH:mm")</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Created By</label>
                                    <div>@Model.CreatedBy</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Last Updated</label>
                                    <div>@Model.LastUpdated.ToString("MMM dd, yyyy HH:mm")</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Updated By</label>
                                    <div>@Model.UpdatedBy</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- [ Main Content ] end -->
</div>
