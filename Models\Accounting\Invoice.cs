using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using ColorOasisSystemWeb.Enums;
using ColorOasisSystemWeb.Models.Sales;

namespace ColorOasisSystemWeb.Models.Accounting
{
    public class Invoice
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        [Display(Name = "Invoice Number")]
        [MaxLength(50)]
        public string InvoiceNumber { get; set; } = string.Empty;

        [ForeignKey("SalesOrder")]
        public int? SalesOrderId { get; set; }
        public virtual SalesOrder? SalesOrder { get; set; }

        [ForeignKey("Quotation")]
        public int? QuotationId { get; set; }
        public virtual Quotation? Quotation { get; set; }

        [ForeignKey("Client")]
        public int? ClientId { get; set; }
        public virtual Client? Client { get; set; }

        [ForeignKey("Company")]
        public int? CompanyId { get; set; }
        public virtual Company? Company { get; set; }
        
        [Display(Name = "Invoice Date")]
        public DateTime InvoiceDate { get; set; } = DateTime.UtcNow;
        
        [Display(Name = "Due Date")]
        public DateTime DueDate { get; set; }
        
        [Display(Name = "Invoice Status")]
        public InvoiceStatus Status { get; set; } = InvoiceStatus.Draft;
        
        [Display(Name = "Invoice Type")]
        [MaxLength(50)]
        public string InvoiceType { get; set; } = "Standard"; // Standard, Proforma, Credit Note, Debit Note
        
        [Display(Name = "Reference Number")]
        [MaxLength(50)]
        public string? ReferenceNumber { get; set; }

        [Display(Name = "Purchase Order Number")]
        [MaxLength(50)]
        public string? PurchaseOrderNumber { get; set; }

        [Display(Name = "Billing Address")]
        [MaxLength(500)]
        public string? BillingAddress { get; set; }

        [Display(Name = "Shipping Address")]
        [MaxLength(500)]
        public string? ShippingAddress { get; set; }

        [Display(Name = "Payment Terms")]
        [MaxLength(200)]
        public string? PaymentTerms { get; set; }
        
        [Display(Name = "Subtotal")]
        [NumericOnly]
        public decimal Subtotal { get; set; } = 0;
        
        [Display(Name = "Tax Amount")]
        [NumericOnly]
        public decimal TaxAmount { get; set; } = 0;
        
        [Display(Name = "Tax Rate")]
        [NumericOnly]
        public decimal TaxRate { get; set; } = 5; // Default 5% VAT
        
        [Display(Name = "Discount Amount")]
        [NumericOnly]
        public decimal DiscountAmount { get; set; } = 0;
        
        [Display(Name = "Total Amount")]
        [NumericOnly]
        public decimal TotalAmount { get; set; } = 0;
        
        [Display(Name = "Amount Paid")]
        [NumericOnly]
        public decimal AmountPaid { get; set; } = 0;
        
        [Display(Name = "Balance Due")]
        [NumericOnly]
        public decimal BalanceDue => TotalAmount - AmountPaid;
        
        [Display(Name = "Currency")]
        [MaxLength(3)]
        public string Currency { get; set; } = "AED";

        [Display(Name = "Exchange Rate")]
        [NumericOnly]
        public decimal ExchangeRate { get; set; } = 1;

        [Display(Name = "Terms and Conditions")]
        [MaxLength(2000)]
        public string? TermsAndConditions { get; set; }

        [Display(Name = "Notes")]
        [MaxLength(1000)]
        public string? Notes { get; set; }

        [Display(Name = "Internal Notes")]
        [MaxLength(1000)]
        public string? InternalNotes { get; set; }

        [Display(Name = "Created By")]
        public string? CreatedBy { get; set; }
        
        [Display(Name = "Created Date")]
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
        
        [Display(Name = "Sent Date")]
        public DateTime? SentDate { get; set; }
        
        [Display(Name = "Viewed Date")]
        public DateTime? ViewedDate { get; set; }
        
        [Display(Name = "Last Payment Date")]
        public DateTime? LastPaymentDate { get; set; }
        
        [Display(Name = "Last Modified Date")]
        public DateTime LastModifiedDate { get; set; } = DateTime.UtcNow;
        
        [Display(Name = "Last Modified By")]
        public string? LastModifiedBy { get; set; }

        [Display(Name = "Is Recurring")]
        public bool IsRecurring { get; set; } = false;

        [Display(Name = "Recurring Frequency")]
        [MaxLength(20)]
        public string? RecurringFrequency { get; set; } // Monthly, Quarterly, Annually
        
        [Display(Name = "Next Invoice Date")]
        public DateTime? NextInvoiceDate { get; set; }
        
        [Display(Name = "Is Paid")]
        public bool IsPaid => AmountPaid >= TotalAmount;
        
        [Display(Name = "Is Overdue")]
        public bool IsOverdue => !IsPaid && DueDate < DateTime.UtcNow;
        
        [Display(Name = "Days Overdue")]
        public int DaysOverdue => IsOverdue ? (DateTime.UtcNow - DueDate).Days : 0;
        
        // Navigation Properties
        public virtual ICollection<InvoiceItem> Items { get; set; } = new List<InvoiceItem>();
        public virtual ICollection<Payment> Payments { get; set; } = new List<Payment>();
        public virtual ICollection<InvoiceHistory> History { get; set; } = new List<InvoiceHistory>();
    }

    public class InvoiceItem
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        [ForeignKey("Invoice")]
        public int InvoiceId { get; set; }
        public virtual Invoice Invoice { get; set; }
        
        [ForeignKey("Service")]
        public int? ServiceId { get; set; }
        public virtual Service Service { get; set; }
        
        [Required]
        [Display(Name = "Item Description")]
        [MaxLength(200)]
        public string ItemDescription { get; set; }
        
        [Display(Name = "Quantity")]
        [NumericOnly]
        public decimal Quantity { get; set; }
        
        [Display(Name = "Unit of Measure")]
        [MaxLength(20)]
        public string UnitOfMeasure { get; set; } = "PCS";
        
        [Display(Name = "Unit Price")]
        [NumericOnly]
        public decimal UnitPrice { get; set; }
        
        [Display(Name = "Discount Percentage")]
        [NumericOnly]
        public decimal DiscountPercentage { get; set; } = 0;
        
        [Display(Name = "Discount Amount")]
        [NumericOnly]
        public decimal DiscountAmount { get; set; } = 0;
        
        [Display(Name = "Line Total")]
        [NumericOnly]
        public decimal LineTotal => (Quantity * UnitPrice) - DiscountAmount;
        
        [Display(Name = "Tax Rate")]
        [NumericOnly]
        public decimal TaxRate { get; set; } = 5;
        
        [Display(Name = "Tax Amount")]
        [NumericOnly]
        public decimal TaxAmount => LineTotal * (TaxRate / 100);
        
        [Display(Name = "Line Total with Tax")]
        [NumericOnly]
        public decimal LineTotalWithTax => LineTotal + TaxAmount;
        
        [Display(Name = "Notes")]
        [MaxLength(500)]
        public string Notes { get; set; }
        
        [Display(Name = "Line Number")]
        public int LineNumber { get; set; }
    }

    public class Payment
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        [Display(Name = "Payment Number")]
        [MaxLength(50)]
        public string PaymentNumber { get; set; }
        
        [ForeignKey("Invoice")]
        public int? InvoiceId { get; set; }
        public virtual Invoice Invoice { get; set; }
        
        [ForeignKey("Client")]
        public int? ClientId { get; set; }
        public virtual Client Client { get; set; }
        
        [ForeignKey("Company")]
        public int? CompanyId { get; set; }
        public virtual Company Company { get; set; }
        
        [Display(Name = "Payment Date")]
        public DateTime PaymentDate { get; set; } = DateTime.UtcNow;
        
        [Display(Name = "Payment Amount")]
        [NumericOnly]
        public decimal PaymentAmount { get; set; }
        
        [Display(Name = "Payment Method")]
        public PaymentMethod PaymentMethod { get; set; } = PaymentMethod.Cash;
        
        [Display(Name = "Payment Status")]
        public PaymentStatus Status { get; set; } = PaymentStatus.Pending;
        
        [Display(Name = "Reference Number")]
        [MaxLength(100)]
        public string ReferenceNumber { get; set; }
        
        [Display(Name = "Bank Name")]
        [MaxLength(100)]
        public string BankName { get; set; }
        
        [Display(Name = "Check Number")]
        [MaxLength(50)]
        public string CheckNumber { get; set; }
        
        [Display(Name = "Check Date")]
        public DateTime? CheckDate { get; set; }
        
        [Display(Name = "Transaction ID")]
        [MaxLength(100)]
        public string TransactionId { get; set; }
        
        [Display(Name = "Currency")]
        [MaxLength(3)]
        public string Currency { get; set; } = "AED";
        
        [Display(Name = "Exchange Rate")]
        [NumericOnly]
        public decimal ExchangeRate { get; set; } = 1;
        
        [Display(Name = "Notes")]
        [MaxLength(1000)]
        public string Notes { get; set; }
        
        [Display(Name = "Received By")]
        public string ReceivedBy { get; set; }
        
        [Display(Name = "Created Date")]
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
        
        [Display(Name = "Created By")]
        public string CreatedBy { get; set; }
        
        [Display(Name = "Processed Date")]
        public DateTime? ProcessedDate { get; set; }
        
        [Display(Name = "Processed By")]
        public string ProcessedBy { get; set; }
        
        [Display(Name = "Is Refunded")]
        public bool IsRefunded { get; set; } = false;
        
        [Display(Name = "Refund Amount")]
        [NumericOnly]
        public decimal RefundAmount { get; set; } = 0;
        
        [Display(Name = "Refund Date")]
        public DateTime? RefundDate { get; set; }
        
        [Display(Name = "Refund Reason")]
        [MaxLength(500)]
        public string RefundReason { get; set; }
    }

    public class InvoiceHistory
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        [ForeignKey("Invoice")]
        public int InvoiceId { get; set; }
        public virtual Invoice Invoice { get; set; }
        
        [Display(Name = "Action")]
        [MaxLength(50)]
        public string Action { get; set; } // Created, Sent, Viewed, Paid, etc.
        
        [Display(Name = "Description")]
        [MaxLength(500)]
        public string Description { get; set; }
        
        [Display(Name = "Action Date")]
        public DateTime ActionDate { get; set; } = DateTime.UtcNow;
        
        [Display(Name = "Performed By")]
        public string PerformedBy { get; set; }
        
        [Display(Name = "IP Address")]
        [MaxLength(45)]
        public string IpAddress { get; set; }
        
        [Display(Name = "User Agent")]
        [MaxLength(500)]
        public string UserAgent { get; set; }
        
        [Display(Name = "Additional Data")]
        [MaxLength(1000)]
        public string AdditionalData { get; set; }
    }
}
