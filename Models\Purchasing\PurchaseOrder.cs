using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using ColorOasisSystemWeb.Enums;

namespace ColorOasisSystemWeb.Models.Purchasing
{
    public class PurchaseRequisition
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        [Display(Name = "Requisition Number")]
        [MaxLength(50)]
        public string RequisitionNumber { get; set; }
        
        [Required]
        [Display(Name = "Requested By")]
        public string RequestedBy { get; set; }
        
        [Display(Name = "Request Date")]
        public DateTime RequestDate { get; set; } = DateTime.UtcNow;
        
        [Display(Name = "Required Date")]
        public DateTime RequiredDate { get; set; }
        
        [Display(Name = "Department")]
        [MaxLength(100)]
        public string Department { get; set; }
        
        [Display(Name = "Cost Center")]
        [MaxLength(50)]
        public string CostCenter { get; set; }
        
        [Display(Name = "Project Code")]
        [MaxLength(50)]
        public string ProjectCode { get; set; }
        
        [Display(Name = "Requisition Status")]
        public RequisitionStatus Status { get; set; } = RequisitionStatus.Draft;
        
        [Display(Name = "Priority")]
        public Priority Priority { get; set; } = Priority.Normal;
        
        [Display(Name = "Justification")]
        [MaxLength(1000)]
        public string Justification { get; set; }
        
        [Display(Name = "Total Estimated Amount")]
        [NumericOnly]
        public decimal TotalEstimatedAmount { get; set; } = 0;
        
        [Display(Name = "Currency")]
        [MaxLength(3)]
        public string Currency { get; set; } = "AED";
        
        [Display(Name = "Approval Workflow")]
        [MaxLength(200)]
        public string ApprovalWorkflow { get; set; }
        
        [Display(Name = "Current Approver")]
        public string CurrentApprover { get; set; }
        
        [Display(Name = "Approved Date")]
        public DateTime? ApprovedDate { get; set; }
        
        [Display(Name = "Approved By")]
        public string ApprovedBy { get; set; }
        
        [Display(Name = "Rejection Reason")]
        [MaxLength(500)]
        public string RejectionReason { get; set; }
        
        [Display(Name = "Notes")]
        [MaxLength(1000)]
        public string Notes { get; set; }
        
        [Display(Name = "Created Date")]
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
        
        [Display(Name = "Last Modified Date")]
        public DateTime LastModifiedDate { get; set; } = DateTime.UtcNow;
        
        [Display(Name = "Last Modified By")]
        public string LastModifiedBy { get; set; }
        
        // Navigation Properties
        public virtual ICollection<RequisitionItem> Items { get; set; } = new List<RequisitionItem>();
        public virtual ICollection<RequisitionApproval> Approvals { get; set; } = new List<RequisitionApproval>();
    }

    public class RequisitionItem
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        [ForeignKey("PurchaseRequisition")]
        public int RequisitionId { get; set; }
        public virtual PurchaseRequisition PurchaseRequisition { get; set; }
        
        [ForeignKey("Service")]
        public int? ServiceId { get; set; }
        public virtual Service Service { get; set; }
        
        [Required]
        [Display(Name = "Item Description")]
        [MaxLength(200)]
        public string ItemDescription { get; set; }
        
        [Display(Name = "Specifications")]
        [MaxLength(1000)]
        public string Specifications { get; set; }
        
        [Display(Name = "Quantity")]
        [NumericOnly]
        public decimal Quantity { get; set; }
        
        [Display(Name = "Unit of Measure")]
        [MaxLength(20)]
        public string UnitOfMeasure { get; set; } = "PCS";
        
        [Display(Name = "Estimated Unit Price")]
        [NumericOnly]
        public decimal EstimatedUnitPrice { get; set; } = 0;
        
        [Display(Name = "Estimated Total Price")]
        [NumericOnly]
        public decimal EstimatedTotalPrice => Quantity * EstimatedUnitPrice;
        
        [Display(Name = "Preferred Supplier")]
        [MaxLength(100)]
        public string PreferredSupplier { get; set; }
        
        [Display(Name = "Required Date")]
        public DateTime RequiredDate { get; set; }
        
        [Display(Name = "Notes")]
        [MaxLength(500)]
        public string Notes { get; set; }
        
        [Display(Name = "Line Number")]
        public int LineNumber { get; set; }
    }

    public class PurchaseOrder
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        [Display(Name = "Purchase Order Number")]
        [MaxLength(50)]
        public string PONumber { get; set; }
        
        [Required]
        [ForeignKey("Supplier")]
        public int SupplierId { get; set; }
        public virtual Supplier Supplier { get; set; }
        
        [ForeignKey("PurchaseRequisition")]
        public int? RequisitionId { get; set; }
        public virtual PurchaseRequisition PurchaseRequisition { get; set; }
        
        [Display(Name = "Order Date")]
        public DateTime OrderDate { get; set; } = DateTime.UtcNow;
        
        [Display(Name = "Expected Delivery Date")]
        public DateTime ExpectedDeliveryDate { get; set; }
        
        [Display(Name = "Delivery Address")]
        [MaxLength(500)]
        public string DeliveryAddress { get; set; }
        
        [Display(Name = "Purchase Order Status")]
        public POStatus Status { get; set; } = POStatus.Draft;
        
        [Display(Name = "Payment Terms")]
        [MaxLength(200)]
        public string PaymentTerms { get; set; }
        
        [Display(Name = "Delivery Terms")]
        [MaxLength(200)]
        public string DeliveryTerms { get; set; }
        
        [Display(Name = "Shipping Method")]
        [MaxLength(100)]
        public string ShippingMethod { get; set; }
        
        [Display(Name = "FOB Point")]
        [MaxLength(100)]
        public string FOBPoint { get; set; }
        
        [Display(Name = "Subtotal")]
        [NumericOnly]
        public decimal Subtotal { get; set; } = 0;
        
        [Display(Name = "Tax Amount")]
        [NumericOnly]
        public decimal TaxAmount { get; set; } = 0;
        
        [Display(Name = "Tax Rate")]
        [NumericOnly]
        public decimal TaxRate { get; set; } = 5; // Default 5% VAT
        
        [Display(Name = "Discount Amount")]
        [NumericOnly]
        public decimal DiscountAmount { get; set; } = 0;
        
        [Display(Name = "Shipping Cost")]
        [NumericOnly]
        public decimal ShippingCost { get; set; } = 0;
        
        [Display(Name = "Total Amount")]
        [NumericOnly]
        public decimal TotalAmount { get; set; } = 0;
        
        [Display(Name = "Currency")]
        [MaxLength(3)]
        public string Currency { get; set; } = "AED";
        
        [Display(Name = "Exchange Rate")]
        [NumericOnly]
        public decimal ExchangeRate { get; set; } = 1;
        
        [Display(Name = "Terms and Conditions")]
        [MaxLength(2000)]
        public string TermsAndConditions { get; set; }
        
        [Display(Name = "Internal Notes")]
        [MaxLength(1000)]
        public string InternalNotes { get; set; }
        
        [Display(Name = "Supplier Notes")]
        [MaxLength(1000)]
        public string SupplierNotes { get; set; }
        
        [Display(Name = "Created By")]
        public string CreatedBy { get; set; }
        
        [Display(Name = "Created Date")]
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
        
        [Display(Name = "Approved By")]
        public string ApprovedBy { get; set; }
        
        [Display(Name = "Approved Date")]
        public DateTime? ApprovedDate { get; set; }
        
        [Display(Name = "Sent Date")]
        public DateTime? SentDate { get; set; }
        
        [Display(Name = "Acknowledged Date")]
        public DateTime? AcknowledgedDate { get; set; }
        
        [Display(Name = "Last Modified Date")]
        public DateTime LastModifiedDate { get; set; } = DateTime.UtcNow;
        
        [Display(Name = "Last Modified By")]
        public string LastModifiedBy { get; set; }
        
        [Display(Name = "Is Closed")]
        public bool IsClosed { get; set; } = false;
        
        [Display(Name = "Closed Date")]
        public DateTime? ClosedDate { get; set; }
        
        [Display(Name = "Closed By")]
        public string ClosedBy { get; set; }
        
        // Navigation Properties
        public virtual ICollection<PurchaseOrderItem> Items { get; set; } = new List<PurchaseOrderItem>();
        public virtual ICollection<GoodsReceipt> GoodsReceipts { get; set; } = new List<GoodsReceipt>();
        public virtual ICollection<PurchaseOrderApproval> Approvals { get; set; } = new List<PurchaseOrderApproval>();
    }

    public class PurchaseOrderItem
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        [ForeignKey("PurchaseOrder")]
        public int PurchaseOrderId { get; set; }
        public virtual PurchaseOrder PurchaseOrder { get; set; }
        
        [ForeignKey("Service")]
        public int? ServiceId { get; set; }
        public virtual Service Service { get; set; }
        
        [ForeignKey("RequisitionItem")]
        public int? RequisitionItemId { get; set; }
        public virtual RequisitionItem RequisitionItem { get; set; }
        
        [Required]
        [Display(Name = "Item Description")]
        [MaxLength(200)]
        public string ItemDescription { get; set; }
        
        [Display(Name = "Specifications")]
        [MaxLength(1000)]
        public string Specifications { get; set; }
        
        [Display(Name = "Quantity Ordered")]
        [NumericOnly]
        public decimal QuantityOrdered { get; set; }
        
        [Display(Name = "Quantity Received")]
        [NumericOnly]
        public decimal QuantityReceived { get; set; } = 0;
        
        [Display(Name = "Quantity Outstanding")]
        [NumericOnly]
        public decimal QuantityOutstanding => QuantityOrdered - QuantityReceived;
        
        [Display(Name = "Unit of Measure")]
        [MaxLength(20)]
        public string UnitOfMeasure { get; set; } = "PCS";
        
        [Display(Name = "Unit Price")]
        [NumericOnly]
        public decimal UnitPrice { get; set; }
        
        [Display(Name = "Discount Percentage")]
        [NumericOnly]
        public decimal DiscountPercentage { get; set; } = 0;
        
        [Display(Name = "Discount Amount")]
        [NumericOnly]
        public decimal DiscountAmount { get; set; } = 0;
        
        [Display(Name = "Line Total")]
        [NumericOnly]
        public decimal LineTotal => (QuantityOrdered * UnitPrice) - DiscountAmount;
        
        [Display(Name = "Expected Delivery Date")]
        public DateTime ExpectedDeliveryDate { get; set; }
        
        [Display(Name = "Notes")]
        [MaxLength(500)]
        public string Notes { get; set; }
        
        [Display(Name = "Line Number")]
        public int LineNumber { get; set; }
        
        [Display(Name = "Is Received")]
        public bool IsReceived => QuantityReceived >= QuantityOrdered;
        
        [Display(Name = "Is Partially Received")]
        public bool IsPartiallyReceived => QuantityReceived > 0 && QuantityReceived < QuantityOrdered;
    }

    public class GoodsReceipt
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [Display(Name = "Receipt Number")]
        [MaxLength(50)]
        public string ReceiptNumber { get; set; }

        [Required]
        [ForeignKey("PurchaseOrder")]
        public int PurchaseOrderId { get; set; }
        public virtual PurchaseOrder PurchaseOrder { get; set; }

        [Display(Name = "Receipt Date")]
        public DateTime ReceiptDate { get; set; } = DateTime.UtcNow;

        [Display(Name = "Received By")]
        public string ReceivedBy { get; set; }

        [Display(Name = "Delivery Note Number")]
        [MaxLength(50)]
        public string DeliveryNoteNumber { get; set; }

        [Display(Name = "Vehicle Number")]
        [MaxLength(20)]
        public string VehicleNumber { get; set; }

        [Display(Name = "Driver Name")]
        [MaxLength(100)]
        public string DriverName { get; set; }

        [Display(Name = "Receipt Status")]
        public ReceiptStatus Status { get; set; } = ReceiptStatus.Draft;

        [Display(Name = "Quality Status")]
        public QualityStatus QualityCheck { get; set; } = QualityStatus.Pending;

        [Display(Name = "Quality Checked By")]
        public string QualityCheckedBy { get; set; }

        [Display(Name = "Quality Check Date")]
        public DateTime? QualityCheckDate { get; set; }

        [Display(Name = "Quality Notes")]
        [MaxLength(1000)]
        public string QualityNotes { get; set; }

        [Display(Name = "General Notes")]
        [MaxLength(1000)]
        public string Notes { get; set; }

        [Display(Name = "Created Date")]
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;

        [Display(Name = "Last Modified Date")]
        public DateTime LastModifiedDate { get; set; } = DateTime.UtcNow;

        [Display(Name = "Last Modified By")]
        public string LastModifiedBy { get; set; }

        // Navigation Properties
        public virtual ICollection<ReceiptItem> Items { get; set; } = new List<ReceiptItem>();
    }

    public class ReceiptItem
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [ForeignKey("GoodsReceipt")]
        public int ReceiptId { get; set; }
        public virtual GoodsReceipt GoodsReceipt { get; set; }

        [Required]
        [ForeignKey("PurchaseOrderItem")]
        public int PurchaseOrderItemId { get; set; }
        public virtual PurchaseOrderItem PurchaseOrderItem { get; set; }

        [Display(Name = "Quantity Received")]
        [NumericOnly]
        public decimal QuantityReceived { get; set; }

        [Display(Name = "Quantity Accepted")]
        [NumericOnly]
        public decimal QuantityAccepted { get; set; }

        [Display(Name = "Quantity Rejected")]
        [NumericOnly]
        public decimal QuantityRejected { get; set; }

        [Display(Name = "Unit Cost")]
        [NumericOnly]
        public decimal UnitCost { get; set; }

        [Display(Name = "Total Cost")]
        [NumericOnly]
        public decimal TotalCost => QuantityAccepted * UnitCost;

        [Display(Name = "Batch/Lot Number")]
        [MaxLength(50)]
        public string BatchLotNumber { get; set; }

        [Display(Name = "Serial Numbers")]
        [MaxLength(500)]
        public string SerialNumbers { get; set; }

        [Display(Name = "Expiry Date")]
        public DateTime? ExpiryDate { get; set; }

        [Display(Name = "Quality Status")]
        public QualityStatus QualityStatus { get; set; } = QualityStatus.Pending;

        [Display(Name = "Rejection Reason")]
        [MaxLength(500)]
        public string RejectionReason { get; set; }

        [Display(Name = "Notes")]
        [MaxLength(500)]
        public string Notes { get; set; }

        [Display(Name = "Line Number")]
        public int LineNumber { get; set; }
    }

    public class RequisitionApproval
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [ForeignKey("PurchaseRequisition")]
        public int RequisitionId { get; set; }
        public virtual PurchaseRequisition PurchaseRequisition { get; set; }

        [Display(Name = "Approver")]
        public string ApproverId { get; set; }

        [Display(Name = "Approval Level")]
        public int ApprovalLevel { get; set; }

        [Display(Name = "Approval Status")]
        public ApprovalStatus Status { get; set; } = ApprovalStatus.Pending;

        [Display(Name = "Approval Date")]
        public DateTime? ApprovalDate { get; set; }

        [Display(Name = "Comments")]
        [MaxLength(1000)]
        public string Comments { get; set; }

        [Display(Name = "Delegated To")]
        public string DelegatedTo { get; set; }

        [Display(Name = "Delegation Date")]
        public DateTime? DelegationDate { get; set; }
    }

    public class PurchaseOrderApproval
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [ForeignKey("PurchaseOrder")]
        public int PurchaseOrderId { get; set; }
        public virtual PurchaseOrder PurchaseOrder { get; set; }

        [Display(Name = "Approver")]
        public string ApproverId { get; set; }

        [Display(Name = "Approval Level")]
        public int ApprovalLevel { get; set; }

        [Display(Name = "Approval Status")]
        public ApprovalStatus Status { get; set; } = ApprovalStatus.Pending;

        [Display(Name = "Approval Date")]
        public DateTime? ApprovalDate { get; set; }

        [Display(Name = "Comments")]
        [MaxLength(1000)]
        public string Comments { get; set; }

        [Display(Name = "Delegated To")]
        public string DelegatedTo { get; set; }

        [Display(Name = "Delegation Date")]
        public DateTime? DelegationDate { get; set; }
    }
}
