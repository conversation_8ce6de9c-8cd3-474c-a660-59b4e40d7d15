@model IEnumerable<ColorOasisSystemWeb.Models.Purchasing.PurchaseOrder>

@{
    ViewData["Title"] = "Purchase Orders";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="nxl-content">
    <!-- [ page-header ] start -->
    <div class="page-header">
        <div class="page-header-left d-flex align-items-center">
            <div class="page-header-title">
                <h5 class="m-b-10">Purchase Orders</h5>
            </div>
            <ul class="breadcrumb">
                <li class="breadcrumb-item"><a href="@Url.Action("Index", "Home")">Home</a></li>
                <li class="breadcrumb-item">ERP</li>
                <li class="breadcrumb-item">Purchasing</li>
                <li class="breadcrumb-item">Purchase Orders</li>
            </ul>
        </div>
        <div class="page-header-right ms-auto">
            <div class="page-header-right-items">
                <div class="d-flex d-md-none">
                    <a href="javascript:void(0)" class="page-header-right-close-toggle">
                        <i class="feather-arrow-left me-2"></i>
                        <span>Back</span>
                    </a>
                </div>
                <div class="d-flex align-items-center gap-2 page-header-right-items-wrapper">
                    <a href="@Url.Action("Create")" class="btn btn-primary">
                        <i class="feather-plus me-2"></i>
                        <span>Create Purchase Order</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
    <!-- [ page-header ] end -->

    <!-- [ Main Content ] start -->
    <div class="main-content">
        <div class="row">
            <div class="col-lg-12">
                <div class="card stretch stretch-full">
                    <div class="card-header">
                        <h5 class="card-title">Purchase Orders</h5>
                        <div class="card-header-action">
                            <div class="card-header-btn">
                                <div data-bs-toggle="tooltip" title="Refresh">
                                    <a href="javascript:void(0);" class="avatar-text avatar-md" onclick="location.reload();">
                                        <i class="feather-refresh-cw"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body custom-card-action p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0" id="purchaseOrdersTable">
                                <thead>
                                    <tr>
                                        <th>PO Number</th>
                                        <th>Supplier</th>
                                        <th>Order Date</th>
                                        <th>Expected Delivery</th>
                                        <th>Total Amount</th>
                                        <th>Status</th>
                                        <th class="text-end">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var po in Model)
                                    {
                                        <tr>
                                            <td>
                                                <div class="fw-bold text-dark">@po.PONumber</div>
                                                <div class="fs-12 text-muted">@po.Items?.Count() item(s)</div>
                                            </td>
                                            <td>
                                                <div class="fw-bold text-dark">@po.Supplier?.CompanyName</div>
                                                <div class="fs-12 text-muted">@po.Supplier?.ContactPerson</div>
                                            </td>
                                            <td>
                                                <div class="fw-bold">@po.OrderDate.ToString("MMM dd, yyyy")</div>
                                            </td>
                                            <td>
                                                <div class="fw-bold">@po.ExpectedDeliveryDate.ToString("MMM dd, yyyy")</div>
                                                @{
                                                    var daysUntilDelivery = (po.ExpectedDeliveryDate - DateTime.Now).Days;
                                                    var deliveryClass = daysUntilDelivery < 0 ? "text-danger" : daysUntilDelivery <= 3 ? "text-warning" : "text-success";
                                                }
                                                <div class="fs-12 @deliveryClass">
                                                    @if (daysUntilDelivery < 0)
                                                    {
                                                        <span>@Math.Abs(daysUntilDelivery) days overdue</span>
                                                    }
                                                    else if (daysUntilDelivery == 0)
                                                    {
                                                        <span>Due today</span>
                                                    }
                                                    else
                                                    {
                                                        <span>@daysUntilDelivery days remaining</span>
                                                    }
                                                </div>
                                            </td>
                                            <td>
                                                <div class="fw-bold">@po.TotalAmount.ToString("C")</div>
                                                @if (po.TaxAmount > 0)
                                                {
                                                    <div class="fs-12 text-muted">+@po.TaxAmount.ToString("C") tax</div>
                                                }
                                            </td>
                                            <td>
                                                @switch (po.Status)
                                                {
                                                    case ColorOasisSystemWeb.Models.Purchasing.POStatus.Draft:
                                                        <span class="badge bg-soft-secondary text-secondary">Draft</span>
                                                        break;
                                                    case ColorOasisSystemWeb.Models.Purchasing.POStatus.Submitted:
                                                        <span class="badge bg-soft-warning text-warning">Submitted</span>
                                                        break;
                                                    case ColorOasisSystemWeb.Models.Purchasing.POStatus.Approved:
                                                        <span class="badge bg-soft-info text-info">Approved</span>
                                                        break;
                                                    case ColorOasisSystemWeb.Models.Purchasing.POStatus.Sent:
                                                        <span class="badge bg-soft-primary text-primary">Sent</span>
                                                        break;
                                                    case ColorOasisSystemWeb.Models.Purchasing.POStatus.PartiallyReceived:
                                                        <span class="badge bg-soft-warning text-warning">Partially Received</span>
                                                        break;
                                                    case ColorOasisSystemWeb.Models.Purchasing.POStatus.Received:
                                                        <span class="badge bg-soft-success text-success">Received</span>
                                                        break;
                                                    case ColorOasisSystemWeb.Models.Purchasing.POStatus.Cancelled:
                                                        <span class="badge bg-soft-danger text-danger">Cancelled</span>
                                                        break;
                                                    case ColorOasisSystemWeb.Models.Purchasing.POStatus.Closed:
                                                        <span class="badge bg-soft-dark text-dark">Closed</span>
                                                        break;
                                                }
                                            </td>
                                            <td>
                                                <div class="hstack gap-2 justify-content-end">
                                                    <a href="@Url.Action("Details", new { id = po.Id })" class="avatar-text avatar-md" data-bs-toggle="tooltip" title="View Details">
                                                        <i class="feather-eye"></i>
                                                    </a>
                                                    @if (po.Status == ColorOasisSystemWeb.Models.Purchasing.POStatus.Draft)
                                                    {
                                                        <a href="@Url.Action("Edit", new { id = po.Id })" class="avatar-text avatar-md" data-bs-toggle="tooltip" title="Edit">
                                                            <i class="feather-edit-3"></i>
                                                        </a>
                                                    }
                                                    <div class="dropdown">
                                                        <a href="javascript:void(0);" class="avatar-text avatar-md" data-bs-toggle="dropdown" data-bs-offset="0,10">
                                                            <i class="feather-more-horizontal"></i>
                                                        </a>
                                                        <div class="dropdown-menu dropdown-menu-end">
                                                            @if (po.Status == ColorOasisSystemWeb.Models.Purchasing.POStatus.Draft)
                                                            {
                                                                <a class="dropdown-item" href="@Url.Action("Submit", new { id = po.Id })">
                                                                    <i class="feather-send me-3"></i>
                                                                    <span>Submit for Approval</span>
                                                                </a>
                                                            }
                                                            @if (po.Status == ColorOasisSystemWeb.Models.Purchasing.POStatus.Submitted)
                                                            {
                                                                <a class="dropdown-item" href="@Url.Action("Approve", new { id = po.Id })">
                                                                    <i class="feather-check me-3"></i>
                                                                    <span>Approve</span>
                                                                </a>
                                                            }
                                                            @if (po.Status != ColorOasisSystemWeb.Models.Purchasing.POStatus.Cancelled && po.Status != ColorOasisSystemWeb.Models.Purchasing.POStatus.Closed)
                                                            {
                                                                <a class="dropdown-item text-danger" href="@Url.Action("Cancel", new { id = po.Id })">
                                                                    <i class="feather-x me-3"></i>
                                                                    <span>Cancel</span>
                                                                </a>
                                                            }
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- [ Main Content ] end -->
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            $('#purchaseOrdersTable').DataTable({
                responsive: true,
                pageLength: 25,
                order: [[0, 'desc']],
                columnDefs: [
                    { orderable: false, targets: [6] }
                ]
            });
        });
    </script>
}
