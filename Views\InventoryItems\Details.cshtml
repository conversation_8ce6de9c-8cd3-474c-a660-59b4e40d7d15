@model ColorOasisSystemWeb.Models.Inventory.InventoryItem

@{
    ViewData["Title"] = "Inventory Item Details";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="card-title">@ViewData["Title"]</h4>
                    <div>
                        @if (User.HasClaim("Permission", "Inventory.Edit"))
                        {
                            <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-primary me-2">
                                <i data-feather="edit" class="feather-icon me-2"></i>
                                Edit
                            </a>
                        }
                        <a asp-action="Index" class="btn btn-secondary">
                            <i data-feather="arrow-left" class="feather-icon me-2"></i>
                            Back to List
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Basic Information -->
                        <div class="col-md-6">
                            <h5 class="mb-3">Basic Information</h5>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>SKU:</strong></td>
                                    <td>@Html.DisplayFor(model => model.SKU)</td>
                                </tr>
                                <tr>
                                    <td><strong>Code:</strong></td>
                                    <td>@Html.DisplayFor(model => model.Code)</td>
                                </tr>
                                <tr>
                                    <td><strong>Name:</strong></td>
                                    <td>@Html.DisplayFor(model => model.Name)</td>
                                </tr>
                                @if (!string.IsNullOrEmpty(Model.NameEn))
                                {
                                    <tr>
                                        <td><strong>Name (English):</strong></td>
                                        <td>@Html.DisplayFor(model => model.NameEn)</td>
                                    </tr>
                                }
                                <tr>
                                    <td><strong>Category:</strong></td>
                                    <td>@Html.DisplayFor(model => model.ServiceCategory.Name)</td>
                                </tr>
                                <tr>
                                    <td><strong>Type:</strong></td>
                                    <td>
                                        <span class="badge bg-info">@Html.DisplayFor(model => model.Type)</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Status:</strong></td>
                                    <td>
                                        @if (!Model.IsDeleted)
                                        {
                                            <span class="badge bg-success">Active</span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-secondary">Inactive</span>
                                        }
                                    </td>
                                </tr>
                            </table>
                        </div>

                        <!-- Inventory Information -->
                        <div class="col-md-6">
                            <h5 class="mb-3">Inventory Information</h5>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Unit of Measure:</strong></td>
                                    <td>@Html.DisplayFor(model => model.UnitOfMeasure)</td>
                                </tr>
                                <tr>
                                    <td><strong>Reorder Point:</strong></td>
                                    <td>@Html.DisplayFor(model => model.ReorderPoint)</td>
                                </tr>
                                <tr>
                                    <td><strong>Max Stock Level:</strong></td>
                                    <td>@Html.DisplayFor(model => model.MaxStockLevel)</td>
                                </tr>
                                <tr>
                                    <td><strong>Safety Stock:</strong></td>
                                    <td>@Html.DisplayFor(model => model.SafetyStock)</td>
                                </tr>
                                <tr>
                                    <td><strong>Lead Time:</strong></td>
                                    <td>@Html.DisplayFor(model => model.LeadTimeDays) days</td>
                                </tr>
                                <tr>
                                    <td><strong>ABC Classification:</strong></td>
                                    <td>
                                        <span class="badge bg-warning">@Html.DisplayFor(model => model.ABCClass)</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Serial Tracked:</strong></td>
                                    <td>
                                        @if (Model.IsSerialTracked)
                                        {
                                            <span class="badge bg-success">Yes</span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-secondary">No</span>
                                        }
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Lot Tracked:</strong></td>
                                    <td>
                                        @if (Model.IsLotTracked)
                                        {
                                            <span class="badge bg-success">Yes</span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-secondary">No</span>
                                        }
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Pricing Information -->
                        <div class="col-md-6">
                            <h5 class="mb-3">Pricing Information</h5>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Standard Price:</strong></td>
                                    <td><strong>@Model.Price.ToString("C")</strong></td>
                                </tr>
                                <tr>
                                    <td><strong>Minimum Price:</strong></td>
                                    <td>@Model.MinimumPrice.ToString("C")</td>
                                </tr>
                                <tr>
                                    <td><strong>Maximum Price:</strong></td>
                                    <td>@Model.MaximumPrice.ToString("C")</td>
                                </tr>
                                <tr>
                                    <td><strong>Standard Cost:</strong></td>
                                    <td>@Model.StandardCost.ToString("C")</td>
                                </tr>
                                <tr>
                                    <td><strong>Average Cost:</strong></td>
                                    <td>@Model.AverageCost.ToString("C")</td>
                                </tr>
                                <tr>
                                    <td><strong>Last Cost:</strong></td>
                                    <td>@Model.LastCost.ToString("C")</td>
                                </tr>
                            </table>
                        </div>

                        <!-- Additional Information -->
                        <div class="col-md-6">
                            <h5 class="mb-3">Additional Information</h5>
                            <table class="table table-borderless">
                                @if (!string.IsNullOrEmpty(Model.PreferredSupplier))
                                {
                                    <tr>
                                        <td><strong>Preferred Supplier:</strong></td>
                                        <td>@Html.DisplayFor(model => model.PreferredSupplier)</td>
                                    </tr>
                                }
                                @if (!string.IsNullOrEmpty(Model.Barcode))
                                {
                                    <tr>
                                        <td><strong>Barcode:</strong></td>
                                        <td>@Html.DisplayFor(model => model.Barcode)</td>
                                    </tr>
                                }
                                <tr>
                                    <td><strong>Item ID:</strong></td>
                                    <td>@Model.Id</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <!-- Stock Locations -->
                    @if (Model.Locations?.Any() == true)
                    {
                        <div class="row">
                            <div class="col-12">
                                <h5 class="mb-3">Stock Locations</h5>
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>Warehouse</th>
                                                <th>Bin Location</th>
                                                <th>On Hand</th>
                                                <th>Reserved</th>
                                                <th>Available</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var location in Model.Locations)
                                            {
                                                <tr>
                                                    <td>@location.Warehouse?.Name</td>
                                                    <td>@location.BinLocation</td>
                                                    <td>@location.QuantityOnHand @Model.UnitOfMeasure</td>
                                                    <td>@location.QuantityReserved @Model.UnitOfMeasure</td>
                                                    <td>
                                                        <strong>@location.QuantityAvailable @Model.UnitOfMeasure</strong>
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                        <tfoot>
                                            <tr class="table-info">
                                                <th colspan="2">Total</th>
                                                <th>@Model.Locations.Sum(l => l.QuantityOnHand) @Model.UnitOfMeasure</th>
                                                <th>@Model.Locations.Sum(l => l.QuantityReserved) @Model.UnitOfMeasure</th>
                                                <th>@Model.Locations.Sum(l => l.QuantityAvailable) @Model.UnitOfMeasure</th>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </div>
                        </div>
                    }

                    <!-- Recent Transactions -->
                    @if (Model.Transactions?.Any() == true)
                    {
                        <div class="row">
                            <div class="col-12">
                                <h5 class="mb-3">Recent Transactions (Last 10)</h5>
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>Date</th>
                                                <th>Type</th>
                                                <th>Warehouse</th>
                                                <th>Quantity</th>
                                                <th>Unit Cost</th>
                                                <th>Reference</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var transaction in Model.Transactions.OrderByDescending(t => t.TransactionDate).Take(10))
                                            {
                                                <tr>
                                                    <td>@transaction.TransactionDate.ToString("dd/MM/yyyy")</td>
                                                    <td>
                                                        <span class="badge bg-primary">@transaction.Type</span>
                                                    </td>
                                                    <td>@transaction.Warehouse?.Name</td>
                                                    <td>@transaction.Quantity @Model.UnitOfMeasure</td>
                                                    <td>@transaction.UnitCost.ToString("C")</td>
                                                    <td>@transaction.Reference</td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Initialize Feather icons
        if (typeof feather !== 'undefined') {
            feather.replace();
        }
    </script>
}
