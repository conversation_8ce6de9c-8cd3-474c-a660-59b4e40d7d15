using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using ColorOasisSystemWeb.Authorization;

namespace ColorOasisSystemWeb.Controllers
{
    [Authorize]
    public class InvoicesController : Controller
    {
        [Authorize(Permissions.Invoices.View)]
        public IActionResult Index()
        {
            ViewBag.Title = "Invoices";
            ViewBag.Message = "Manage customer invoices, billing, and invoice tracking.";
            return View("~/Views/Shared/ComingSoon.cshtml");
        }

        [Authorize(Permissions.Invoices.Create)]
        public IActionResult Create()
        {
            ViewBag.Title = "Create Invoice";
            ViewBag.Message = "Create new invoice functionality will be implemented here.";
            return View("~/Views/Shared/ComingSoon.cshtml");
        }
    }
}
