using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using ColorOasisSystemWeb.Data;
using ColorOasisSystemWeb.Models;
using Microsoft.AspNetCore.Authorization;
using ColorOasisSystemWeb.Authorization;
using Microsoft.AspNetCore.Identity;
using ColorOasisSystemWeb.AdvancedNotification.Services;
using ColorOasisSystemWeb.Enums;
using ColorOasisSystemWeb.ViewModels.Service;
using ColorOasisSystemWeb.Models.Mappers;

namespace ColorOasisSystemWeb.Controllers
{
    public class ServiceTypesController : Controller
    {
        private readonly AppDbContext _context;
        private readonly UserManager<User> _userManager;
        private readonly NotificationService _notificationService;

        public ServiceTypesController(
            AppDbContext context,
            UserManager<User> userManager,
            NotificationService notificationService)
        {
            _context = context;
            _userManager = userManager;
            _notificationService = notificationService;
        }

        // GET: ServiceTypes
        [Authorize(Permissions.ServiceTypes_View)]
        public async Task<IActionResult> Index()
        {
            var serviceTypes = await _context.ServiceTypes.ToListAsync();
            var viewModels = ServiceTypeMapper.ToViewModelList(serviceTypes);
            return View(viewModels);
        }

        // GET: ServiceTypes/Details/5
        [Authorize(Permissions.ServiceTypes_View)]
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var serviceType = await _context.ServiceTypes
                .FirstOrDefaultAsync(m => m.Id == id);
            if (serviceType == null)
            {
                return NotFound();
            }

            var viewModel = ServiceTypeMapper.ToViewModel(serviceType);
            return View(viewModel);
        }

        // GET: ServiceTypes/Create
        [Authorize(Permissions.ServiceTypes_Create)]
        public IActionResult Create()
        {
            string datePart = DateTime.Now.ToString("ddMMyyyy");

            // Get the latest client code that matches today's date
            var lastClient = _context.ServiceTypes
                .Where(c => c.Code.Contains(datePart))
                .OrderByDescending(c => c.Code)
                .FirstOrDefaultAsync().Result;

            // Initialize the sequence number
            int sequenceNumber = 1;

            if (lastClient != null)
            {
                // Extract the numeric part and increment it
                string lastSequence = lastClient.Code.Substring(16);
                sequenceNumber = int.Parse(lastSequence) + 1;
            }

            // Generate the new client code
            string newClientCode = $"SRVCTYP-{datePart}{sequenceNumber.ToString("D4")}";
            ViewBag.Code = newClientCode;
            var viewModel = new ServiceTypeViewModel();
            return View(viewModel);
        }

        // POST: ServiceTypes/Create
        // To protect from overposting attacks, enable the specific properties you want to bind to.
        // For more details, see http://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        [Authorize(Permissions.ServiceTypes_Create)]
        public async Task<IActionResult> Create([Bind("Id,Code,Name,NameEn")] ServiceTypeViewModel viewModel)
        {
            ViewBag.Code = viewModel.Code;

            if (ModelState.IsValid)
            {
                var serviceType = ServiceTypeMapper.FromViewModel(viewModel);
                _context.Add(serviceType);
                await _context.SaveChangesAsync();
                
                // Send notification for new service type
                var currentUser = await _userManager.GetUserAsync(User);
                if (currentUser != null)
                {
                    await _notificationService.CreateAndSendNotification(
                        ModelType.Servicetype,
                        ProcessType.Add,
                        serviceType.Id,
                        currentUser);
                }
                
                return RedirectToAction(nameof(Index));
            }
            return View(viewModel);
        }

        // GET: ServiceTypes/Edit/5
        [Authorize(Permissions.ServiceTypes_Edit)]
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var serviceType = await _context.ServiceTypes.FindAsync(id);
            if (serviceType == null)
            {
                return NotFound();
            }
            var viewModel = ServiceTypeMapper.ToViewModel(serviceType);
            return View(viewModel);
        }

        // POST: ServiceTypes/Edit/5
        // To protect from overposting attacks, enable the specific properties you want to bind to.
        // For more details, see http://go.microsoft.com/fwlink/?LinkId=317598.
        [HttpPost]
        [ValidateAntiForgeryToken]
        [Authorize(Permissions.ServiceTypes_Edit)]
        public async Task<IActionResult> Edit(int id, [Bind("Id,Code,Name,NameEn")] ServiceTypeViewModel viewModel)
        {
            if (id != viewModel.Id)
            {
                return NotFound();
            }
            ViewBag.Code = viewModel.Code;

            if (ModelState.IsValid)
            {
                try
                {
                    var serviceType = ServiceTypeMapper.FromViewModel(viewModel);
                    _context.Update(serviceType);
                    await _context.SaveChangesAsync();
                    // Send notification for new service type
                    var currentUser = await _userManager.GetUserAsync(User);
                    if (currentUser != null)
                    {
                        await _notificationService.CreateAndSendNotification(
                            ModelType.Servicetype,
                            ProcessType.Update,
                            serviceType.Id,
                            currentUser);
                    }
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!ServiceTypeExists(viewModel.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
            }
            return View(viewModel);
        }

        // GET: ServiceTypes/Delete/5
        [Authorize(Permissions.ServiceTypes_Delete)]
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var serviceType = await _context.ServiceTypes
                .FirstOrDefaultAsync(m => m.Id == id);
            if (serviceType == null)
            {
                return NotFound();
            }

            var viewModel = ServiceTypeMapper.ToViewModel(serviceType);
            return View(viewModel);
        }

        // POST: ServiceTypes/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        [Authorize(Permissions.ServiceTypes_Delete)]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var serviceType = await _context.ServiceTypes.FindAsync(id);
            if (serviceType != null)
            {
                _context.ServiceTypes.Remove(serviceType);
            }

            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
        }

        private bool ServiceTypeExists(int id)
        {
            return _context.ServiceTypes.Any(e => e.Id == id);
        }
    }
}
