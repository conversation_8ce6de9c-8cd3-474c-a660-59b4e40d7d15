using System;
using System.Collections.Generic;
using System.Linq;

namespace ColorOasisSystemWeb.Authorization
{
    public static class Permissions
    {
        // User permissions
        public const string Users_View = "Permissions.Users.View";
        public const string Users_Create = "Permissions.Users.Create";
        public const string Users_Edit = "Permissions.Users.Edit";
        public const string Users_Delete = "Permissions.Users.Delete";
        
        // Role permissions
        public const string Roles_View = "Permissions.Roles.View";
        public const string Roles_Create = "Permissions.Roles.Create";
        public const string Roles_Edit = "Permissions.Roles.Edit";
        public const string Roles_Delete = "Permissions.Roles.Delete";
        
        // Notification permissions
        public const string Notifications_View = "Permissions.Notifications.View";
        public const string Notifications_Create = "Permissions.Notifications.Create";
        public const string Notifications_Edit = "Permissions.Notifications.Edit";
        public const string Notifications_Delete = "Permissions.Notifications.Delete";
        
        // Client permissions
        public const string Clients_View = "Permissions.Clients.View";
        public const string Clients_Create = "Permissions.Clients.Create";
        public const string Clients_Edit = "Permissions.Clients.Edit";
        public const string Clients_Delete = "Permissions.Clients.Delete";
        
        // Company permissions
        public const string Companies_View = "Permissions.Companies.View";
        public const string Companies_Create = "Permissions.Companies.Create";
        public const string Companies_Edit = "Permissions.Companies.Edit";
        public const string Companies_Delete = "Permissions.Companies.Delete";
        
        // CompanyInfo permissions
        public const string CompanyInfo_View = "Permissions.CompanyInfo.View";
        public const string CompanyInfo_Create = "Permissions.CompanyInfo.Create";
        public const string CompanyInfo_Edit = "Permissions.CompanyInfo.Edit";
        public const string CompanyInfo_Delete = "Permissions.CompanyInfo.Delete";
        
        // Inspection permissions
        public const string Inspections_View = "Permissions.Inspections.View";
        public const string Inspections_Create = "Permissions.Inspections.Create";
        public const string Inspections_Edit = "Permissions.Inspections.Edit";
        public const string Inspections_Delete = "Permissions.Inspections.Delete";
        
        // Quotation permissions
        public const string Quotations_View = "Permissions.Quotations.View";
        public const string Quotations_Create = "Permissions.Quotations.Create";
        public const string Quotations_Edit = "Permissions.Quotations.Edit";
        public const string Quotations_Delete = "Permissions.Quotations.Delete";
        
        // Service permissions
        public const string Services_View = "Permissions.Services.View";
        public const string Services_Create = "Permissions.Services.Create";
        public const string Services_Edit = "Permissions.Services.Edit";
        public const string Services_Delete = "Permissions.Services.Delete";
        
        // ServiceCategory permissions
        public const string ServiceCategories_View = "Permissions.ServiceCategories.View";
        public const string ServiceCategories_Create = "Permissions.ServiceCategories.Create";
        public const string ServiceCategories_Edit = "Permissions.ServiceCategories.Edit";
        public const string ServiceCategories_Delete = "Permissions.ServiceCategories.Delete";
        
        // ServiceType permissions
        public const string ServiceTypes_View = "Permissions.ServiceTypes.View";
        public const string ServiceTypes_Create = "Permissions.ServiceTypes.Create";
        public const string ServiceTypes_Edit = "Permissions.ServiceTypes.Edit";
        public const string ServiceTypes_Delete = "Permissions.ServiceTypes.Delete";
        
        // ConnectionGroup permissions
        public const string ConnectionGroups_View = "Permissions.ConnectionGroups.View";
        public const string ConnectionGroups_Create = "Permissions.ConnectionGroups.Create";
        public const string ConnectionGroups_Edit = "Permissions.ConnectionGroups.Edit";
        public const string ConnectionGroups_Delete = "Permissions.ConnectionGroups.Delete";
        
        // InspectionDetails permissions
        public const string InspectionDetails_View = "Permissions.InspectionDetails.View";
        public const string InspectionDetails_Create = "Permissions.InspectionDetails.Create";
        public const string InspectionDetails_Edit = "Permissions.InspectionDetails.Edit";
        public const string InspectionDetails_Delete = "Permissions.InspectionDetails.Delete";
        
        // QuotationDetails permissions
        public const string QuotationDetails_View = "Permissions.QuotationDetails.View";
        public const string QuotationDetails_Create = "Permissions.QuotationDetails.Create";
        public const string QuotationDetails_Edit = "Permissions.QuotationDetails.Edit";
        public const string QuotationDetails_Delete = "Permissions.QuotationDetails.Delete";
        
        // UserConnection permissions
        public const string UserConnections_View = "Permissions.UserConnections.View";
        public const string UserConnections_Create = "Permissions.UserConnections.Create";
        public const string UserConnections_Edit = "Permissions.UserConnections.Edit";
        public const string UserConnections_Delete = "Permissions.UserConnections.Delete";
        
        // SignalR permissions
        public const string SignalR_View = "Permissions.SignalR.View";
        public const string SignalR_Manage = "Permissions.SignalR.Manage";
        
        // Chat permissions
        public const string Chat_View = "Permissions.Chat.View";
        public const string Chat_Send = "Permissions.Chat.Send";
        public const string Chat_Manage = "Permissions.Chat.Manage";
        
        // Get all permissions
        public static List<string> GetAllPermissions()
        {
            return typeof(Permissions)
                .GetFields(System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Static | System.Reflection.BindingFlags.FlattenHierarchy)
                .Where(f => f.IsLiteral && !f.IsInitOnly && f.FieldType == typeof(string))
                .Select(f => (string)f.GetValue(null))
                .ToList();
        }
        
        // Get permissions by role
        public static List<string> GetPermissionsByRole(string roleName)
        {
            // Default permissions based on role
            switch (roleName)
            {
                case "Admin":
                    return GetAllPermissions();
                case "Manager":
                    return new List<string>
                    {
                        // User management
                        Users_View,
                        
                        // Role management
                        Roles_View,
                        
                        // Notification management
                        Notifications_View,
                        Notifications_Create,
                        Notifications_Edit,
                        
                        // Client management
                        Clients_View,
                        Clients_Create,
                        Clients_Edit,
                        
                        // Company management
                        Companies_View,
                        Companies_Create,
                        Companies_Edit,
                        
                        // CompanyInfo management
                        CompanyInfo_View,
                        CompanyInfo_Edit,
                        
                        // Inspection management
                        Inspections_View,
                        Inspections_Create,
                        Inspections_Edit,
                        InspectionDetails_View,
                        InspectionDetails_Create,
                        InspectionDetails_Edit,
                        
                        // Quotation management
                        Quotations_View,
                        Quotations_Create,
                        Quotations_Edit,
                        QuotationDetails_View,
                        QuotationDetails_Create,
                        QuotationDetails_Edit,
                        
                        // Service management
                        Services_View,
                        Services_Create,
                        Services_Edit,
                        ServiceCategories_View,
                        ServiceTypes_View,
                        
                        // Connection management
                        ConnectionGroups_View,
                        UserConnections_View,
                        
                        // SignalR management
                        SignalR_View
                    };
                case "User":
                    return new List<string>
                    {
                        // Notification permissions
                        Notifications_View,
                        
                        // View-only permissions
                        Clients_View,
                        Companies_View,
                        CompanyInfo_View,
                        Inspections_View,
                        InspectionDetails_View,
                        Quotations_View,
                        QuotationDetails_View,
                        Services_View,
                        ServiceCategories_View,
                        ServiceTypes_View,
                        ConnectionGroups_View,
                        UserConnections_View
                    };
                default:
                    // For custom roles, provide basic view permissions
                    // This ensures new roles have at least basic access
                    return new List<string>
                    {
                        // Basic view permissions for custom roles
                        Users_View,
                        Notifications_View,
                        Clients_View,
                        Companies_View,
                        CompanyInfo_View,
                        Inspections_View,
                        InspectionDetails_View,
                        Quotations_View,
                        QuotationDetails_View,
                        Services_View,
                        ServiceCategories_View,
                        ServiceTypes_View,
                        ConnectionGroups_View,
                        UserConnections_View
                    };
            }
        }
        
        // Get all permissions grouped by category
        public static Dictionary<string, List<PermissionInfo>> GetPermissionsByCategory()
        {
            var allPermissions = GetAllPermissions();
            var permissionGroups = new Dictionary<string, List<PermissionInfo>>();
            
            foreach (var permission in allPermissions)
            {
                var parts = permission.Split('.');
                if (parts.Length >= 3)
                {
                    var category = parts[1]; // e.g., "Users", "Roles", etc.
                    var action = parts[2]; // e.g., "View", "Create", etc.
                    
                    if (!permissionGroups.ContainsKey(category))
                    {
                        permissionGroups[category] = new List<PermissionInfo>();
                    }
                    
                    permissionGroups[category].Add(new PermissionInfo
                    {
                        Name = $"{category} {action}",
                        Value = permission,
                        Category = category,
                        Action = action
                    });
                }
            }
            
            return permissionGroups;
        }
    }
    
    // Class to hold permission information
    public class PermissionInfo
    {
        public string Name { get; set; }
        public string Value { get; set; }
        public string Category { get; set; }
        public string Action { get; set; }
    }
}