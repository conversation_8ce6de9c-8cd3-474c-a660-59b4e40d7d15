using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using ColorOasisSystemWeb.Authorization;

namespace ColorOasisSystemWeb.Controllers
{
    [Authorize]
    public class PurchaseOrdersController : Controller
    {
        [Authorize(Permissions.Purchasing.View)]
        public IActionResult Index()
        {
            ViewBag.Title = "Purchase Orders";
            ViewBag.Message = "Manage purchase orders, supplier orders, and procurement tracking.";
            return View("~/Views/Shared/ComingSoon.cshtml");
        }

        [Authorize(Permissions.Purchasing.Create)]
        public IActionResult Create()
        {
            ViewBag.Title = "Create Purchase Order";
            ViewBag.Message = "Create new purchase order functionality will be implemented here.";
            return View("~/Views/Shared/ComingSoon.cshtml");
        }
    }
}
