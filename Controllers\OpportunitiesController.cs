using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using ColorOasisSystemWeb.Authorization;

namespace ColorOasisSystemWeb.Controllers
{
    [Authorize]
    public class OpportunitiesController : Controller
    {
        [Authorize(Permissions.Opportunities.View)]
        public IActionResult Index()
        {
            ViewBag.Title = "Opportunities";
            ViewBag.Message = "Manage sales opportunities, pipeline tracking, and deal management.";
            return View("~/Views/Shared/ComingSoon.cshtml");
        }

        [Authorize(Permissions.Opportunities.Create)]
        public IActionResult Create()
        {
            ViewBag.Title = "Add Opportunity";
            ViewBag.Message = "Add new opportunity functionality will be implemented here.";
            return View("~/Views/Shared/ComingSoon.cshtml");
        }
    }
}
