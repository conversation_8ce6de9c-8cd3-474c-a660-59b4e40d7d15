@model IEnumerable<ColorOasisSystemWeb.Models.Inventory.InventoryItem>

@{
    ViewData["Title"] = "Inventory Items";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="card-title">@ViewData["Title"]</h4>
                    <div>
                        @if (User.HasClaim("Permission", "Inventory.Create"))
                        {
                            <a asp-action="Create" class="btn btn-primary">
                                <i data-feather="plus" class="feather-icon me-2"></i>
                                Add New Item
                            </a>
                        }
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="inventoryItemsTable" class="table table-striped table-bordered">
                            <thead>
                                <tr>
                                    <th>SKU</th>
                                    <th>Name</th>
                                    <th>Category</th>
                                    <th>Type</th>
                                    <th>Unit Price</th>
                                    <th>Reorder Point</th>
                                    <th>Stock Level</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in Model)
                                {
                                    <tr>
                                        <td>
                                            <strong>@Html.DisplayFor(modelItem => item.SKU)</strong>
                                        </td>
                                        <td>
                                            <div>
                                                <strong>@Html.DisplayFor(modelItem => item.Name)</strong>
                                                @if (!string.IsNullOrEmpty(item.NameEn))
                                                {
                                                    <br><small class="text-muted">@Html.DisplayFor(modelItem => item.NameEn)</small>
                                                }
                                            </div>
                                        </td>
                                        <td>@Html.DisplayFor(modelItem => item.ServiceCategory.Name)</td>
                                        <td>
                                            <span class="badge bg-info">@Html.DisplayFor(modelItem => item.Type)</span>
                                        </td>
                                        <td>
                                            <strong>@item.Price.ToString("C")</strong>
                                            @if (item.MinimumPrice != item.MaximumPrice)
                                            {
                                                <br><small class="text-muted">@item.MinimumPrice.ToString("C") - @item.MaximumPrice.ToString("C")</small>
                                            }
                                        </td>
                                        <td>
                                            <span class="badge bg-warning">@Html.DisplayFor(modelItem => item.ReorderPoint)</span>
                                        </td>
                                        <td>
                                            @{
                                                var totalStock = item.Locations?.Sum(l => l.QuantityOnHand) ?? 0;
                                                var stockClass = totalStock <= item.ReorderPoint ? "text-danger" : totalStock <= item.SafetyStock ? "text-warning" : "text-success";
                                            }
                                            <span class="@stockClass">
                                                <strong>@totalStock @item.UnitOfMeasure</strong>
                                            </span>
                                            @if (item.Locations?.Any() == true)
                                            {
                                                <br><small class="text-muted">@item.Locations.Count() location(s)</small>
                                            }
                                        </td>
                                        <td>
                                            @if (!item.IsDeleted)
                                            {
                                                <span class="badge bg-success">Active</span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-secondary">Inactive</span>
                                            }
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                @if (User.HasClaim("Permission", "Inventory.View"))
                                                {
                                                    <a asp-action="Details" asp-route-id="@item.Id" class="btn btn-sm btn-outline-info" title="View Details">
                                                        <i data-feather="eye" class="feather-icon"></i>
                                                    </a>
                                                }
                                                @if (User.HasClaim("Permission", "Inventory.Edit"))
                                                {
                                                    <a asp-action="Edit" asp-route-id="@item.Id" class="btn btn-sm btn-outline-primary" title="Edit">
                                                        <i data-feather="edit" class="feather-icon"></i>
                                                    </a>
                                                }
                                                @if (User.HasClaim("Permission", "Inventory.Delete"))
                                                {
                                                    <a asp-action="Delete" asp-route-id="@item.Id" class="btn btn-sm btn-outline-danger" title="Delete">
                                                        <i data-feather="trash-2" class="feather-icon"></i>
                                                    </a>
                                                }
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_DataTablesScriptsPartial");}
    <script>
        $(document).ready(function() {
            $('#inventoryItemsTable').DataTable({
                "responsive": true,
                "pageLength": 25,
                "order": [[0, "asc"]],
                "columnDefs": [
                    { "orderable": false, "targets": [8] }
                ],
                "language": {
                    "search": "Search items:",
                    "lengthMenu": "Show _MENU_ items per page",
                    "info": "Showing _START_ to _END_ of _TOTAL_ items",
                    "paginate": {
                        "first": "First",
                        "last": "Last",
                        "next": "Next",
                        "previous": "Previous"
                    }
                }
            });

            // Initialize Feather icons
            if (typeof feather !== 'undefined') {
                feather.replace();
            }
        });
    </script>
}
