!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t="undefined"!=typeof globalThis?globalThis:t||self).bootstrap=e()}(this,function(){"use strict";const d={find(t,e=document.documentElement){return[].concat(...Element.prototype.querySelectorAll.call(e,t))},findOne(t,e=document.documentElement){return Element.prototype.querySelector.call(e,t)},children(t,e){return[].concat(...t.children).filter(t=>t.matches(e))},parents(t,e){const i=[];let n=t.parentNode;for(;n&&n.nodeType===Node.ELEMENT_NODE&&3!==n.nodeType;)n.matches(e)&&i.push(n),n=n.parentNode;return i},prev(t,e){let i=t.previousElementSibling;for(;i;){if(i.matches(e))return[i];i=i.previousElementSibling}return[]},next(t,e){let i=t.nextElementSibling;for(;i;){if(i.matches(e))return[i];i=i.nextElementSibling}return[]}},P=1e3,H="transitionend",R=t=>{for(;t+=Math.floor(1e6*Math.random()),document.getElementById(t););return t},B=e=>{let i=e.getAttribute("data-bs-target");if(!i||"#"===i){let t=e.getAttribute("href");if(!t||!t.includes("#")&&!t.startsWith("."))return null;t.includes("#")&&!t.startsWith("#")&&(t="#"+t.split("#")[1]),i=t&&"#"!==t?t.trim():null}return i},W=t=>{t=B(t);return t&&document.querySelector(t)?t:null},s=t=>{t=B(t);return t?document.querySelector(t):null},q=t=>{t.dispatchEvent(new Event(H))},r=t=>!(!t||"object"!=typeof t)&&void 0!==(t=void 0!==t.jquery?t[0]:t).nodeType,$=t=>r(t)?t.jquery?t[0]:t:"string"==typeof t&&0<t.length?d.findOne(t):null,i=(n,s,o)=>{Object.keys(o).forEach(t=>{var e=o[t],i=s[t],i=i&&r(i)?"element":null==(i=i)?""+i:{}.toString.call(i).match(/\s([a-z]+)/i)[1].toLowerCase();if(!new RegExp(e).test(i))throw new TypeError(n.toUpperCase()+`: Option "${t}" provided type "${i}" but expected type "${e}".`)})},z=t=>!(!r(t)||0===t.getClientRects().length)&&"visible"===getComputedStyle(t).getPropertyValue("visibility"),F=t=>!t||t.nodeType!==Node.ELEMENT_NODE||(!!t.classList.contains("disabled")||(void 0!==t.disabled?t.disabled:t.hasAttribute("disabled")&&"false"!==t.getAttribute("disabled"))),U=t=>{return document.documentElement.attachShadow?"function"==typeof t.getRootNode?(e=t.getRootNode())instanceof ShadowRoot?e:null:t instanceof ShadowRoot?t:t.parentNode?U(t.parentNode):null:null;var e},V=()=>{},K=t=>t.offsetHeight,X=()=>{var t=window["jQuery"];return t&&!document.body.hasAttribute("data-bs-no-jquery")?t:null},Y=[],n=()=>"rtl"===document.documentElement.dir;var t=n=>{var t;t=()=>{const t=X();if(t){const e=n.NAME,i=t.fn[e];t.fn[e]=n.jQueryInterface,t.fn[e].Constructor=n,t.fn[e].noConflict=()=>(t.fn[e]=i,n.jQueryInterface)}},"loading"===document.readyState?(Y.length||document.addEventListener("DOMContentLoaded",()=>{Y.forEach(t=>t())}),Y.push(t)):t()};const o=t=>{"function"==typeof t&&t()},Q=(i,n,t=!0)=>{if(t){t=(t=>{if(!t)return 0;let{transitionDuration:e,transitionDelay:i}=window.getComputedStyle(t);var t=Number.parseFloat(e),n=Number.parseFloat(i);return t||n?(e=e.split(",")[0],i=i.split(",")[0],(Number.parseFloat(e)+Number.parseFloat(i))*P):0})(n)+5;let e=!1;const s=({target:t})=>{t===n&&(e=!0,n.removeEventListener(H,s),o(i))};n.addEventListener(H,s),setTimeout(()=>{e||q(n)},t)}else o(i)},G=(t,e,i,n)=>{let s=t.indexOf(e);if(-1===s)return t[!i&&n?t.length-1:0];e=t.length;return s+=i?1:-1,n&&(s=(s+e)%e),t[Math.max(0,Math.min(s,e-1))]},Z=/[^.]*(?=\..*)\.|.*/,J=/\..*/,tt=/::\d+$/,et={};let it=1;const nt={mouseenter:"mouseover",mouseleave:"mouseout"},st=/^(mouseenter|mouseleave)/i,ot=new Set(["click","dblclick","mouseup","mousedown","contextmenu","mousewheel","DOMMouseScroll","mouseover","mouseout","mousemove","selectstart","selectend","keydown","keypress","keyup","orientationchange","touchstart","touchmove","touchend","touchcancel","pointerdown","pointermove","pointerup","pointerleave","pointercancel","gesturestart","gesturechange","gestureend","focus","blur","change","reset","select","submit","focusin","focusout","load","unload","beforeunload","resize","move","DOMContentLoaded","readystatechange","error","abort","scroll"]);function rt(t,e){return e&&e+"::"+it++||t.uidEvent||it++}function at(t){var e=rt(t);return t.uidEvent=e,et[e]=et[e]||{},et[e]}function lt(i,n,s=null){var o=Object.keys(i);for(let t=0,e=o.length;t<e;t++){var r=i[o[t]];if(r.originalHandler===n&&r.delegationSelector===s)return r}return null}function ct(t,e,i){var n="string"==typeof e,i=n?i:e;let s=ut(t);e=ot.has(s);return[n,i,s=e?s:t]}function ht(t,e,i,n,s){if("string"==typeof e&&t){i||(i=n,n=null),st.test(e)&&(o=e=>function(t){if(!t.relatedTarget||t.relatedTarget!==t.delegateTarget&&!t.delegateTarget.contains(t.relatedTarget))return e.call(this,t)},n?n=o(n):i=o(i));var[o,r,a]=ct(e,i,n);const f=at(t),p=f[a]||(f[a]={}),m=lt(p,r,o?i:null);if(m)m.oneOff=m.oneOff&&s;else{var l,c,h,d,u,e=rt(r,e.replace(Z,""));const g=o?(h=t,d=i,u=n,function i(n){var s=h.querySelectorAll(d);for(let e=n["target"];e&&e!==this;e=e.parentNode)for(let t=s.length;t--;)if(s[t]===e)return n.delegateTarget=e,i.oneOff&&_.off(h,n.type,d,u),u.apply(e,[n]);return null}):(l=t,c=i,function t(e){return e.delegateTarget=l,t.oneOff&&_.off(l,e.type,c),c.apply(l,[e])});g.delegationSelector=o?i:null,g.originalHandler=r,g.oneOff=s,g.uidEvent=e,p[e]=g,t.addEventListener(a,g,o)}}}function dt(t,e,i,n,s){n=lt(e[i],n,s);n&&(t.removeEventListener(i,n,Boolean(s)),delete e[i][n.uidEvent])}function ut(t){return t=t.replace(J,""),nt[t]||t}const _={on(t,e,i,n){ht(t,e,i,n,!1)},one(t,e,i,n){ht(t,e,i,n,!0)},off(r,a,t,e){if("string"==typeof a&&r){const[i,n,s]=ct(a,t,e),o=s!==a,l=at(r);e=a.startsWith(".");if(void 0!==n)return l&&l[s]?void dt(r,l,s,n,i?t:null):void 0;e&&Object.keys(l).forEach(t=>{{var e=r,i=l,n=t,s=a.slice(1);const o=i[n]||{};return void Object.keys(o).forEach(t=>{t.includes(s)&&(t=o[t],dt(e,i,n,t.originalHandler,t.delegationSelector))})}});const c=l[s]||{};Object.keys(c).forEach(t=>{var e=t.replace(tt,"");o&&!a.includes(e)||(e=c[t],dt(r,l,s,e.originalHandler,e.delegationSelector))})}},trigger(t,e,i){if("string"!=typeof e||!t)return null;const n=X();var s=ut(e),o=e!==s,r=ot.has(s);let a,l=!0,c=!0,h=!1,d=null;return o&&n&&(a=n.Event(e,i),n(t).trigger(a),l=!a.isPropagationStopped(),c=!a.isImmediatePropagationStopped(),h=a.isDefaultPrevented()),r?(d=document.createEvent("HTMLEvents")).initEvent(s,l,!0):d=new CustomEvent(e,{bubbles:l,cancelable:!0}),void 0!==i&&Object.keys(i).forEach(t=>{Object.defineProperty(d,t,{get(){return i[t]}})}),h&&d.preventDefault(),c&&t.dispatchEvent(d),d.defaultPrevented&&void 0!==a&&a.preventDefault(),d}},a=new Map;var ft=function(t,e,i){a.has(t)||a.set(t,new Map);const n=a.get(t);n.has(e)||0===n.size?n.set(e,i):console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(n.keys())[0]}.`)},pt=function(t,e){return a.has(t)&&a.get(t).get(e)||null},mt=function(t,e){if(a.has(t)){const i=a.get(t);i.delete(e),0===i.size&&a.delete(t)}};class e{constructor(t){(t=$(t))&&(this._element=t,ft(this._element,this.constructor.DATA_KEY,this))}dispose(){mt(this._element,this.constructor.DATA_KEY),_.off(this._element,this.constructor.EVENT_KEY),Object.getOwnPropertyNames(this).forEach(t=>{this[t]=null})}_queueCallback(t,e,i=!0){Q(t,e,i)}static getInstance(t){return pt(t,this.DATA_KEY)}static getOrCreateInstance(t,e={}){return this.getInstance(t)||new this(t,"object"==typeof e?e:null)}static get VERSION(){return"5.0.2"}static get NAME(){throw new Error('You have to implement the static method "NAME", for each component!')}static get DATA_KEY(){return"bs."+this.NAME}static get EVENT_KEY(){return"."+this.DATA_KEY}}class gt extends e{static get NAME(){return"alert"}close(t){var t=t?this._getRootElement(t):this._element,e=this._triggerCloseEvent(t);null===e||e.defaultPrevented||this._removeElement(t)}_getRootElement(t){return s(t)||t.closest(".alert")}_triggerCloseEvent(t){return _.trigger(t,"close.bs.alert")}_removeElement(t){t.classList.remove("show");var e=t.classList.contains("fade");this._queueCallback(()=>this._destroyElement(t),t,e)}_destroyElement(t){t.remove(),_.trigger(t,"closed.bs.alert")}static jQueryInterface(e){return this.each(function(){const t=gt.getOrCreateInstance(this);"close"===e&&t[e](this)})}static handleDismiss(e){return function(t){t&&t.preventDefault(),e.close(this)}}}_.on(document,"click.bs.alert.data-api",'[data-bs-dismiss="alert"]',gt.handleDismiss(new gt)),t(gt);const _t='[data-bs-toggle="button"]';class vt extends e{static get NAME(){return"button"}toggle(){this._element.setAttribute("aria-pressed",this._element.classList.toggle("active"))}static jQueryInterface(e){return this.each(function(){const t=vt.getOrCreateInstance(this);"toggle"===e&&t[e]()})}}function bt(t){return"true"===t||"false"!==t&&(t===Number(t).toString()?Number(t):""===t||"null"===t?null:t)}function yt(t){return t.replace(/[A-Z]/g,t=>"-"+t.toLowerCase())}_.on(document,"click.bs.button.data-api",_t,t=>{t.preventDefault();t=t.target.closest(_t);const e=vt.getOrCreateInstance(t);e.toggle()}),t(vt);const l={setDataAttribute(t,e,i){t.setAttribute("data-bs-"+yt(e),i)},removeDataAttribute(t,e){t.removeAttribute("data-bs-"+yt(e))},getDataAttributes(i){if(!i)return{};const n={};return Object.keys(i.dataset).filter(t=>t.startsWith("bs")).forEach(t=>{let e=t.replace(/^bs/,"");e=e.charAt(0).toLowerCase()+e.slice(1,e.length),n[e]=bt(i.dataset[t])}),n},getDataAttribute(t,e){return bt(t.getAttribute("data-bs-"+yt(e)))},offset(t){t=t.getBoundingClientRect();return{top:t.top+document.body.scrollTop,left:t.left+document.body.scrollLeft}},position(t){return{top:t.offsetTop,left:t.offsetLeft}}},wt="carousel";var Et=".bs.carousel";const At={interval:5e3,keyboard:!0,slide:!1,pause:"hover",wrap:!0,touch:!0},Tt={interval:"(number|boolean)",keyboard:"boolean",slide:"(boolean|string)",pause:"(string|boolean)",wrap:"boolean",touch:"boolean"},u="next",c="prev",h="left",Ot="right",Ct={ArrowLeft:Ot,ArrowRight:h},Lt="slid"+Et;const f="active",kt=".active.carousel-item";class p extends e{constructor(t,e){super(t),this._items=null,this._interval=null,this._activeElement=null,this._isPaused=!1,this._isSliding=!1,this.touchTimeout=null,this.touchStartX=0,this.touchDeltaX=0,this._config=this._getConfig(e),this._indicatorsElement=d.findOne(".carousel-indicators",this._element),this._touchSupported="ontouchstart"in document.documentElement||0<navigator.maxTouchPoints,this._pointerEvent=Boolean(window.PointerEvent),this._addEventListeners()}static get Default(){return At}static get NAME(){return wt}next(){this._slide(u)}nextWhenVisible(){!document.hidden&&z(this._element)&&this.next()}prev(){this._slide(c)}pause(t){t||(this._isPaused=!0),d.findOne(".carousel-item-next, .carousel-item-prev",this._element)&&(q(this._element),this.cycle(!0)),clearInterval(this._interval),this._interval=null}cycle(t){t||(this._isPaused=!1),this._interval&&(clearInterval(this._interval),this._interval=null),this._config&&this._config.interval&&!this._isPaused&&(this._updateInterval(),this._interval=setInterval((document.visibilityState?this.nextWhenVisible:this.next).bind(this),this._config.interval))}to(t){this._activeElement=d.findOne(kt,this._element);var e=this._getItemIndex(this._activeElement);if(!(t>this._items.length-1||t<0))if(this._isSliding)_.one(this._element,Lt,()=>this.to(t));else{if(e===t)return this.pause(),void this.cycle();e=e<t?u:c;this._slide(e,this._items[t])}}_getConfig(t){return t={...At,...l.getDataAttributes(this._element),..."object"==typeof t?t:{}},i(wt,t,Tt),t}_handleSwipe(){var t=Math.abs(this.touchDeltaX);t<=40||(t=t/this.touchDeltaX,this.touchDeltaX=0,t&&this._slide(0<t?Ot:h))}_addEventListeners(){this._config.keyboard&&_.on(this._element,"keydown.bs.carousel",t=>this._keydown(t)),"hover"===this._config.pause&&(_.on(this._element,"mouseenter.bs.carousel",t=>this.pause(t)),_.on(this._element,"mouseleave.bs.carousel",t=>this.cycle(t))),this._config.touch&&this._touchSupported&&this._addTouchEventListeners()}_addTouchEventListeners(){const e=t=>{!this._pointerEvent||"pen"!==t.pointerType&&"touch"!==t.pointerType?this._pointerEvent||(this.touchStartX=t.touches[0].clientX):this.touchStartX=t.clientX},i=t=>{this.touchDeltaX=t.touches&&1<t.touches.length?0:t.touches[0].clientX-this.touchStartX},n=t=>{!this._pointerEvent||"pen"!==t.pointerType&&"touch"!==t.pointerType||(this.touchDeltaX=t.clientX-this.touchStartX),this._handleSwipe(),"hover"===this._config.pause&&(this.pause(),this.touchTimeout&&clearTimeout(this.touchTimeout),this.touchTimeout=setTimeout(t=>this.cycle(t),500+this._config.interval))};d.find(".carousel-item img",this._element).forEach(t=>{_.on(t,"dragstart.bs.carousel",t=>t.preventDefault())}),this._pointerEvent?(_.on(this._element,"pointerdown.bs.carousel",t=>e(t)),_.on(this._element,"pointerup.bs.carousel",t=>n(t)),this._element.classList.add("pointer-event")):(_.on(this._element,"touchstart.bs.carousel",t=>e(t)),_.on(this._element,"touchmove.bs.carousel",t=>i(t)),_.on(this._element,"touchend.bs.carousel",t=>n(t)))}_keydown(t){var e;/input|textarea/i.test(t.target.tagName)||(e=Ct[t.key])&&(t.preventDefault(),this._slide(e))}_getItemIndex(t){return this._items=t&&t.parentNode?d.find(".carousel-item",t.parentNode):[],this._items.indexOf(t)}_getItemByOrder(t,e){t=t===u;return G(this._items,e,t,this._config.wrap)}_triggerSlideEvent(t,e){var i=this._getItemIndex(t),n=this._getItemIndex(d.findOne(kt,this._element));return _.trigger(this._element,"slide.bs.carousel",{relatedTarget:t,direction:e,from:n,to:i})}_setActiveIndicatorElement(e){if(this._indicatorsElement){const t=d.findOne(".active",this._indicatorsElement),i=(t.classList.remove(f),t.removeAttribute("aria-current"),d.find("[data-bs-target]",this._indicatorsElement));for(let t=0;t<i.length;t++)if(Number.parseInt(i[t].getAttribute("data-bs-slide-to"),10)===this._getItemIndex(e)){i[t].classList.add(f),i[t].setAttribute("aria-current","true");break}}}_updateInterval(){const t=this._activeElement||d.findOne(kt,this._element);var e;t&&((e=Number.parseInt(t.getAttribute("data-bs-interval"),10))?(this._config.defaultInterval=this._config.defaultInterval||this._config.interval,this._config.interval=e):this._config.interval=this._config.defaultInterval||this._config.interval)}_slide(t,e){t=this._directionToOrder(t);const i=d.findOne(kt,this._element),n=this._getItemIndex(i),s=e||this._getItemByOrder(t,i),o=this._getItemIndex(s);var e=Boolean(this._interval),r=t===u;const a=r?"carousel-item-start":"carousel-item-end",l=r?"carousel-item-next":"carousel-item-prev",c=this._orderToDirection(t);if(s&&s.classList.contains(f))this._isSliding=!1;else if(!this._isSliding){r=this._triggerSlideEvent(s,c);if(!r.defaultPrevented&&i&&s){this._isSliding=!0,e&&this.pause(),this._setActiveIndicatorElement(s),this._activeElement=s;const h=()=>{_.trigger(this._element,Lt,{relatedTarget:s,direction:c,from:n,to:o})};this._element.classList.contains("slide")?(s.classList.add(l),K(s),i.classList.add(a),s.classList.add(a),this._queueCallback(()=>{s.classList.remove(a,l),s.classList.add(f),i.classList.remove(f,l,a),this._isSliding=!1,setTimeout(h,0)},i,!0)):(i.classList.remove(f),s.classList.add(f),this._isSliding=!1,h()),e&&this.cycle()}}}_directionToOrder(t){return[Ot,h].includes(t)?n()?t===h?c:u:t===h?u:c:t}_orderToDirection(t){return[u,c].includes(t)?n()?t===c?h:Ot:t===c?Ot:h:t}static carouselInterface(t,e){const i=p.getOrCreateInstance(t,e);let n=i["_config"];"object"==typeof e&&(n={...n,...e});t="string"==typeof e?e:n.slide;if("number"==typeof e)i.to(e);else if("string"==typeof t){if(void 0===i[t])throw new TypeError(`No method named "${t}"`);i[t]()}else n.interval&&n.ride&&(i.pause(),i.cycle())}static jQueryInterface(t){return this.each(function(){p.carouselInterface(this,t)})}static dataApiClickHandler(t){const e=s(this);if(e&&e.classList.contains("carousel")){const n={...l.getDataAttributes(e),...l.getDataAttributes(this)};var i=this.getAttribute("data-bs-slide-to");i&&(n.interval=!1),p.carouselInterface(e,n),i&&p.getInstance(e).to(i),t.preventDefault()}}}_.on(document,"click.bs.carousel.data-api","[data-bs-slide], [data-bs-slide-to]",p.dataApiClickHandler),_.on(window,"load.bs.carousel.data-api",()=>{var i=d.find('[data-bs-ride="carousel"]');for(let t=0,e=i.length;t<e;t++)p.carouselInterface(i[t],p.getInstance(i[t]))}),t(p);const xt="collapse",Dt="bs.collapse";Dt;const St={toggle:!0,parent:""},It={toggle:"boolean",parent:"(string|element)"};const m="show",Nt="collapse",jt="collapsing",Mt="collapsed",Pt='[data-bs-toggle="collapse"]';class g extends e{constructor(t,e){super(t),this._isTransitioning=!1,this._config=this._getConfig(e),this._triggerArray=d.find(Pt+`[href="#${this._element.id}"],`+Pt+`[data-bs-target="#${this._element.id}"]`);var i=d.find(Pt);for(let t=0,e=i.length;t<e;t++){var n=i[t],s=W(n),o=d.find(s).filter(t=>t===this._element);null!==s&&o.length&&(this._selector=s,this._triggerArray.push(n))}this._parent=this._config.parent?this._getParent():null,this._config.parent||this._addAriaAndCollapsedClass(this._element,this._triggerArray),this._config.toggle&&this.toggle()}static get Default(){return St}static get NAME(){return xt}toggle(){this._element.classList.contains(m)?this.hide():this.show()}show(){if(!this._isTransitioning&&!this._element.classList.contains(m)){let t,e;this._parent&&0===(t=d.find(".show, .collapsing",this._parent).filter(t=>"string"==typeof this._config.parent?t.getAttribute("data-bs-parent")===this._config.parent:t.classList.contains(Nt))).length&&(t=null);const n=d.findOne(this._selector);if(t){var i=t.find(t=>n!==t);if((e=i?g.getInstance(i):null)&&e._isTransitioning)return}i=_.trigger(this._element,"show.bs.collapse");if(!i.defaultPrevented){t&&t.forEach(t=>{n!==t&&g.collapseInterface(t,"hide"),e||ft(t,Dt,null)});const s=this._getDimension();this._element.classList.remove(Nt),this._element.classList.add(jt),this._element.style[s]=0,this._triggerArray.length&&this._triggerArray.forEach(t=>{t.classList.remove(Mt),t.setAttribute("aria-expanded",!0)}),this.setTransitioning(!0);i="scroll"+(s[0].toUpperCase()+s.slice(1));this._queueCallback(()=>{this._element.classList.remove(jt),this._element.classList.add(Nt,m),this._element.style[s]="",this.setTransitioning(!1),_.trigger(this._element,"shown.bs.collapse")},this._element,!0),this._element.style[s]=this._element[i]+"px"}}}hide(){if(!this._isTransitioning&&this._element.classList.contains(m)){var t=_.trigger(this._element,"hide.bs.collapse");if(!t.defaultPrevented){var t=this._getDimension(),e=(this._element.style[t]=this._element.getBoundingClientRect()[t]+"px",K(this._element),this._element.classList.add(jt),this._element.classList.remove(Nt,m),this._triggerArray.length);if(0<e)for(let t=0;t<e;t++){const i=this._triggerArray[t],n=s(i);n&&!n.classList.contains(m)&&(i.classList.add(Mt),i.setAttribute("aria-expanded",!1))}this.setTransitioning(!0);this._element.style[t]="",this._queueCallback(()=>{this.setTransitioning(!1),this._element.classList.remove(jt),this._element.classList.add(Nt),_.trigger(this._element,"hidden.bs.collapse")},this._element,!0)}}}setTransitioning(t){this._isTransitioning=t}_getConfig(t){return(t={...St,...t}).toggle=Boolean(t.toggle),i(xt,t,It),t}_getDimension(){return this._element.classList.contains("width")?"width":"height"}_getParent(){var t=this._config["parent"],t=$(t),e=Pt+`[data-bs-parent="${t}"]`;return d.find(e,t).forEach(t=>{var e=s(t);this._addAriaAndCollapsedClass(e,[t])}),t}_addAriaAndCollapsedClass(t,e){if(t&&e.length){const i=t.classList.contains(m);e.forEach(t=>{i?t.classList.remove(Mt):t.classList.add(Mt),t.setAttribute("aria-expanded",i)})}}static collapseInterface(t,e){let i=g.getInstance(t);const n={...St,...l.getDataAttributes(t),..."object"==typeof e&&e?e:{}};if(!i&&n.toggle&&"string"==typeof e&&/show|hide/.test(e)&&(n.toggle=!1),i=i||new g(t,n),"string"==typeof e){if(void 0===i[e])throw new TypeError(`No method named "${e}"`);i[e]()}}static jQueryInterface(t){return this.each(function(){g.collapseInterface(this,t)})}}_.on(document,"click.bs.collapse.data-api",Pt,function(t){("A"===t.target.tagName||t.delegateTarget&&"A"===t.delegateTarget.tagName)&&t.preventDefault();const n=l.getDataAttributes(this);t=W(this);const e=d.find(t);e.forEach(t=>{const e=g.getInstance(t);let i;i=e?(null===e._parent&&"string"==typeof n.parent&&(e._config.parent=n.parent,e._parent=e._getParent()),"toggle"):n,g.collapseInterface(t,i)})}),t(g);var L="top",k="bottom",x="right",D="left",Ht="auto",Rt=[L,k,x,D],S="start",Bt="end",Wt="clippingParents",qt="viewport",$t="popper",zt="reference",Ft=Rt.reduce(function(t,e){return t.concat([e+"-"+S,e+"-"+Bt])},[]),Ut=[].concat(Rt,[Ht]).reduce(function(t,e){return t.concat([e,e+"-"+S,e+"-"+Bt])},[]),Et="beforeRead",Vt="afterRead",Kt="beforeMain",Xt="afterMain",Yt="beforeWrite",Qt="afterWrite",Gt=[Et,"read",Vt,Kt,"main",Xt,Yt,"write",Qt];function v(t){return t?(t.nodeName||"").toLowerCase():null}function b(t){return null==t?window:"[object Window]"!==t.toString()?(e=t.ownerDocument)&&e.defaultView||window:t;var e}function Zt(t){return t instanceof b(t).Element||t instanceof Element}function y(t){return t instanceof b(t).HTMLElement||t instanceof HTMLElement}function Jt(t){if("undefined"!=typeof ShadowRoot)return t instanceof b(t).ShadowRoot||t instanceof ShadowRoot}var w={name:"applyStyles",enabled:!0,phase:"write",fn:function(t){var s=t.state;Object.keys(s.elements).forEach(function(t){var e=s.styles[t]||{},i=s.attributes[t]||{},n=s.elements[t];y(n)&&v(n)&&(Object.assign(n.style,e),Object.keys(i).forEach(function(t){var e=i[t];!1===e?n.removeAttribute(t):n.setAttribute(t,!0===e?"":e)}))})},effect:function(t){var n=t.state,s={popper:{position:n.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(n.elements.popper.style,s.popper),n.styles=s,n.elements.arrow&&Object.assign(n.elements.arrow.style,s.arrow),function(){Object.keys(n.elements).forEach(function(t){var e=n.elements[t],i=n.attributes[t]||{},t=Object.keys((n.styles.hasOwnProperty(t)?n.styles:s)[t]).reduce(function(t,e){return t[e]="",t},{});y(e)&&v(e)&&(Object.assign(e.style,t),Object.keys(i).forEach(function(t){e.removeAttribute(t)}))})}},requires:["computeStyles"]};function I(t){return t.split("-")[0]}function te(t){t=t.getBoundingClientRect();return{width:t.width,height:t.height,top:t.top,right:t.right,bottom:t.bottom,left:t.left,x:t.left,y:t.top}}function ee(t){var e=te(t),i=t.offsetWidth,n=t.offsetHeight;return Math.abs(e.width-i)<=1&&(i=e.width),Math.abs(e.height-n)<=1&&(n=e.height),{x:t.offsetLeft,y:t.offsetTop,width:i,height:n}}function ie(t,e){var i=e.getRootNode&&e.getRootNode();if(t.contains(e))return!0;if(i&&Jt(i)){var n=e;do{if(n&&t.isSameNode(n))return!0}while(n=n.parentNode||n.host)}return!1}function E(t){return b(t).getComputedStyle(t)}function A(t){return((Zt(t)?t.ownerDocument:t.document)||window.document).documentElement}function ne(t){return"html"===v(t)?t:t.assignedSlot||t.parentNode||(Jt(t)?t.host:null)||A(t)}function se(t){return y(t)&&"fixed"!==E(t).position?t.offsetParent:null}function oe(t){for(var e,i=b(t),n=se(t);n&&(e=n,0<=["table","td","th"].indexOf(v(e)))&&"static"===E(n).position;)n=se(n);return(!n||"html"!==v(n)&&("body"!==v(n)||"static"!==E(n).position))&&(n||function(t){var e=-1!==navigator.userAgent.toLowerCase().indexOf("firefox"),i=-1!==navigator.userAgent.indexOf("Trident");if(i&&y(t)&&"fixed"===E(t).position)return null;for(var n=ne(t);y(n)&&["html","body"].indexOf(v(n))<0;){var s=E(n);if("none"!==s.transform||"none"!==s.perspective||"paint"===s.contain||-1!==["transform","perspective"].indexOf(s.willChange)||e&&"filter"===s.willChange||e&&s.filter&&"none"!==s.filter)return n;n=n.parentNode}return null}(t))||i}function re(t){return 0<=["top","bottom"].indexOf(t)?"x":"y"}var T=Math.max,ae=Math.min,le=Math.round;function ce(t,e,i){return T(t,ae(e,i))}function he(){return{top:0,right:0,bottom:0,left:0}}function de(t){return Object.assign({},he(),t)}function ue(i,t){return t.reduce(function(t,e){return t[e]=i,t},{})}var O={name:"arrow",enabled:!0,phase:"main",fn:function(t){var e,i,n,s,o=t.state,r=t.name,t=t.options,a=o.elements.arrow,l=o.modifiersData.popperOffsets,c=re(h=I(o.placement)),h=0<=[D,x].indexOf(h)?"height":"width";a&&l&&(t=t.padding,i=o,i=de("number"!=typeof(t="function"==typeof t?t(Object.assign({},i.rects,{placement:i.placement})):t)?t:ue(t,Rt)),t=ee(a),s="y"===c?L:D,n="y"===c?k:x,e=o.rects.reference[h]+o.rects.reference[c]-l[c]-o.rects.popper[h],l=l[c]-o.rects.reference[c],a=(a=oe(a))?"y"===c?a.clientHeight||0:a.clientWidth||0:0,s=i[s],i=a-t[h]-i[n],s=ce(s,n=a/2-t[h]/2+(e/2-l/2),i),o.modifiersData[r]=((a={})[c]=s,a.centerOffset=s-n,a))},effect:function(t){var e=t.state;null!=(t=void 0===(t=t.options.element)?"[data-popper-arrow]":t)&&("string"!=typeof t||(t=e.elements.popper.querySelector(t)))&&ie(e.elements.popper,t)&&(e.elements.arrow=t)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]},fe={top:"auto",right:"auto",bottom:"auto",left:"auto"};function pe(t){var e,i,n,s=t.popper,o=t.popperRect,r=t.placement,a=t.offsets,l=t.position,c=t.gpuAcceleration,h=t.adaptive,t=t.roundOffsets,d=!0===t?(d=(u=a).x,u=a.y,f=window.devicePixelRatio||1,{x:le(le(d*f)/f)||0,y:le(le(u*f)/f)||0}):"function"==typeof t?t(a):a,u=d.x,f=void 0===u?0:u,t=d.y,t=void 0===t?0:t,p=a.hasOwnProperty("x"),a=a.hasOwnProperty("y"),m=D,g=L,_=window,s=(h&&(n="clientHeight",i="clientWidth",(e=oe(s))===b(s)&&"static"!==E(e=A(s)).position&&(n="scrollHeight",i="scrollWidth"),r===L&&(g=k,t=(t-(e[n]-o.height))*(c?1:-1)),r===D&&(m=x,f=(f-(e[i]-o.width))*(c?1:-1))),Object.assign({position:l},h&&fe));return c?Object.assign({},s,((n={})[g]=a?"0":"",n[m]=p?"0":"",n.transform=(_.devicePixelRatio||1)<2?"translate("+f+"px, "+t+"px)":"translate3d("+f+"px, "+t+"px, 0)",n)):Object.assign({},s,((r={})[g]=a?t+"px":"",r[m]=p?f+"px":"",r.transform="",r))}var me={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(t){var e=t.state,t=t.options,i=void 0===(i=t.gpuAcceleration)||i,n=void 0===(n=t.adaptive)||n,t=void 0===(t=t.roundOffsets)||t,i={placement:I(e.placement),popper:e.elements.popper,popperRect:e.rects.popper,gpuAcceleration:i};null!=e.modifiersData.popperOffsets&&(e.styles.popper=Object.assign({},e.styles.popper,pe(Object.assign({},i,{offsets:e.modifiersData.popperOffsets,position:e.options.strategy,adaptive:n,roundOffsets:t})))),null!=e.modifiersData.arrow&&(e.styles.arrow=Object.assign({},e.styles.arrow,pe(Object.assign({},i,{offsets:e.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:t})))),e.attributes.popper=Object.assign({},e.attributes.popper,{"data-popper-placement":e.placement})},data:{}},ge={passive:!0};var _e={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(t){var e=t.state,i=t.instance,n=(t=t.options).scroll,s=void 0===n||n,o=void 0===(n=t.resize)||n,r=b(e.elements.popper),a=[].concat(e.scrollParents.reference,e.scrollParents.popper);return s&&a.forEach(function(t){t.addEventListener("scroll",i.update,ge)}),o&&r.addEventListener("resize",i.update,ge),function(){s&&a.forEach(function(t){t.removeEventListener("scroll",i.update,ge)}),o&&r.removeEventListener("resize",i.update,ge)}},data:{}},ve={left:"right",right:"left",bottom:"top",top:"bottom"};function be(t){return t.replace(/left|right|bottom|top/g,function(t){return ve[t]})}var ye={start:"end",end:"start"};function we(t){return t.replace(/start|end/g,function(t){return ye[t]})}function Ee(t){t=b(t);return{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function Ae(t){return te(A(t)).left+Ee(t).scrollLeft}function Te(t){var t=E(t),e=t.overflow,i=t.overflowX,t=t.overflowY;return/auto|scroll|overlay|hidden/.test(e+t+i)}function Oe(t,e){void 0===e&&(e=[]);var i=function t(e){return 0<=["html","body","#document"].indexOf(v(e))?e.ownerDocument.body:y(e)&&Te(e)?e:t(ne(e))}(t),t=i===(null==(t=t.ownerDocument)?void 0:t.body),n=b(i),n=t?[n].concat(n.visualViewport||[],Te(i)?i:[]):i,i=e.concat(n);return t?i:i.concat(Oe(ne(n)))}function Ce(t){return Object.assign({},t,{left:t.x,top:t.y,right:t.x+t.width,bottom:t.y+t.height})}function Le(t,e){return e===qt?Ce((n=b(i=t),s=A(i),n=n.visualViewport,o=s.clientWidth,s=s.clientHeight,a=r=0,n&&(o=n.width,s=n.height,/^((?!chrome|android).)*safari/i.test(navigator.userAgent)||(r=n.offsetLeft,a=n.offsetTop)),{width:o,height:s,x:r+Ae(i),y:a})):y(e)?((o=te(n=e)).top=o.top+n.clientTop,o.left=o.left+n.clientLeft,o.bottom=o.top+n.clientHeight,o.right=o.left+n.clientWidth,o.width=n.clientWidth,o.height=n.clientHeight,o.x=o.left,o.y=o.top,o):Ce((s=A(t),r=A(s),i=Ee(s),a=null==(a=s.ownerDocument)?void 0:a.body,e=T(r.scrollWidth,r.clientWidth,a?a.scrollWidth:0,a?a.clientWidth:0),t=T(r.scrollHeight,r.clientHeight,a?a.scrollHeight:0,a?a.clientHeight:0),s=-i.scrollLeft+Ae(s),i=-i.scrollTop,"rtl"===E(a||r).direction&&(s+=T(r.clientWidth,a?a.clientWidth:0)-e),{width:e,height:t,x:s,y:i}));var i,n,s,o,r,a}function ke(i,t,e){var n,s="clippingParents"===t?(o=Oe(ne(s=i)),Zt(n=0<=["absolute","fixed"].indexOf(E(s).position)&&y(s)?oe(s):s)?o.filter(function(t){return Zt(t)&&ie(t,n)&&"body"!==v(t)}):[]):[].concat(t),o=[].concat(s,[e]),t=o[0],e=o.reduce(function(t,e){e=Le(i,e);return t.top=T(e.top,t.top),t.right=ae(e.right,t.right),t.bottom=ae(e.bottom,t.bottom),t.left=T(e.left,t.left),t},Le(i,t));return e.width=e.right-e.left,e.height=e.bottom-e.top,e.x=e.left,e.y=e.top,e}function xe(t){return t.split("-")[1]}function De(t){var e,i=t.reference,n=t.element,t=t.placement,s=t?I(t):null,t=t?xe(t):null,o=i.x+i.width/2-n.width/2,r=i.y+i.height/2-n.height/2;switch(s){case L:e={x:o,y:i.y-n.height};break;case k:e={x:o,y:i.y+i.height};break;case x:e={x:i.x+i.width,y:r};break;case D:e={x:i.x-n.width,y:r};break;default:e={x:i.x,y:i.y}}var a=s?re(s):null;if(null!=a){var l="y"===a?"height":"width";switch(t){case S:e[a]=e[a]-(i[l]/2-n[l]/2);break;case Bt:e[a]=e[a]+(i[l]/2-n[l]/2)}}return e}function Se(t,e){var n,e=e=void 0===e?{}:e,i=e.placement,i=void 0===i?t.placement:i,s=e.boundary,s=void 0===s?Wt:s,o=e.rootBoundary,o=void 0===o?qt:o,r=e.elementContext,r=void 0===r?$t:r,a=e.altBoundary,a=void 0!==a&&a,e=e.padding,e=void 0===e?0:e,e=de("number"!=typeof e?e:ue(e,Rt)),l=t.elements.reference,c=t.rects.popper,a=t.elements[a?r===$t?zt:$t:r],a=ke(Zt(a)?a:a.contextElement||A(t.elements.popper),s,o),s=te(l),o=De({reference:s,element:c,strategy:"absolute",placement:i}),l=Ce(Object.assign({},c,o)),c=r===$t?l:s,h={top:a.top-c.top+e.top,bottom:c.bottom-a.bottom+e.bottom,left:a.left-c.left+e.left,right:c.right-a.right+e.right},o=t.modifiersData.offset;return r===$t&&o&&(n=o[i],Object.keys(h).forEach(function(t){var e=0<=[x,k].indexOf(t)?1:-1,i=0<=[L,k].indexOf(t)?"y":"x";h[t]+=n[i]*e})),h}var Ie={name:"flip",enabled:!0,phase:"main",fn:function(t){var d=t.state,e=t.options,t=t.name;if(!d.modifiersData[t]._skip){for(var i=e.mainAxis,n=void 0===i||i,i=e.altAxis,s=void 0===i||i,i=e.fallbackPlacements,u=e.padding,f=e.boundary,p=e.rootBoundary,o=e.altBoundary,r=e.flipVariations,m=void 0===r||r,g=e.allowedAutoPlacements,r=d.options.placement,e=I(r),i=i||(e===r||!m?[be(r)]:function(t){if(I(t)===Ht)return[];var e=be(t);return[we(t),e,we(e)]}(r)),a=[r].concat(i).reduce(function(t,e){return t.concat(I(e)===Ht?(i=d,n=(t=t=void 0===(t={placement:e,boundary:f,rootBoundary:p,padding:u,flipVariations:m,allowedAutoPlacements:g})?{}:t).placement,s=t.boundary,o=t.rootBoundary,r=t.padding,a=t.flipVariations,l=void 0===(t=t.allowedAutoPlacements)?Ut:t,c=xe(n),t=c?a?Ft:Ft.filter(function(t){return xe(t)===c}):Rt,h=(n=0===(n=t.filter(function(t){return 0<=l.indexOf(t)})).length?t:n).reduce(function(t,e){return t[e]=Se(i,{placement:e,boundary:s,rootBoundary:o,padding:r})[I(e)],t},{}),Object.keys(h).sort(function(t,e){return h[t]-h[e]})):e);var i,n,s,o,r,a,l,c,h},[]),l=d.rects.reference,c=d.rects.popper,h=new Map,_=!0,v=a[0],b=0;b<a.length;b++){var y=a[b],w=I(y),E=xe(y)===S,A=0<=[L,k].indexOf(w),T=A?"width":"height",O=Se(d,{placement:y,boundary:f,rootBoundary:p,altBoundary:o,padding:u}),A=A?E?x:D:E?k:L,E=(l[T]>c[T]&&(A=be(A)),be(A)),T=[];if(n&&T.push(O[w]<=0),s&&T.push(O[A]<=0,O[E]<=0),T.every(function(t){return t})){v=y,_=!1;break}h.set(y,T)}if(_)for(var C=m?3:1;0<C;C--)if("break"===function(e){var t=a.find(function(t){t=h.get(t);if(t)return t.slice(0,e).every(function(t){return t})});if(t)return v=t,"break"}(C))break;d.placement!==v&&(d.modifiersData[t]._skip=!0,d.placement=v,d.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}};function Ne(t,e,i){return{top:t.top-e.height-(i=void 0===i?{x:0,y:0}:i).y,right:t.right-e.width+i.x,bottom:t.bottom-e.height+i.y,left:t.left-e.width-i.x}}function je(e){return[L,x,k,D].some(function(t){return 0<=e[t]})}var Me={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(t){var e=t.state,t=t.name,i=e.rects.reference,n=e.rects.popper,s=e.modifiersData.preventOverflow,o=Se(e,{elementContext:"reference"}),r=Se(e,{altBoundary:!0}),o=Ne(o,i),i=Ne(r,n,s),r=je(o),n=je(i);e.modifiersData[t]={referenceClippingOffsets:o,popperEscapeOffsets:i,isReferenceHidden:r,hasPopperEscaped:n},e.attributes.popper=Object.assign({},e.attributes.popper,{"data-popper-reference-hidden":r,"data-popper-escaped":n})}};var Pe={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(t){var r=t.state,e=t.options,t=t.name,a=void 0===(e=e.offset)?[0,0]:e,e=Ut.reduce(function(t,e){var i,n,s,o;return t[e]=(e=e,i=r.rects,n=a,s=I(e),o=0<=[D,L].indexOf(s)?-1:1,e=(i="function"==typeof n?n(Object.assign({},i,{placement:e})):n)[0]||0,n=(i[1]||0)*o,0<=[D,x].indexOf(s)?{x:n,y:e}:{x:e,y:n}),t},{}),i=(n=e[r.placement]).x,n=n.y;null!=r.modifiersData.popperOffsets&&(r.modifiersData.popperOffsets.x+=i,r.modifiersData.popperOffsets.y+=n),r.modifiersData[t]=e}};var He={name:"popperOffsets",enabled:!0,phase:"read",fn:function(t){var e=t.state,t=t.name;e.modifiersData[t]=De({reference:e.rects.reference,element:e.rects.popper,strategy:"absolute",placement:e.placement})},data:{}};var Re={name:"preventOverflow",enabled:!0,phase:"main",fn:function(t){var e,i,n,s,o,r,a,l,c,h=t.state,d=t.options,t=t.name,u=void 0===(u=d.mainAxis)||u,f=void 0!==(f=d.altAxis)&&f,p=d.boundary,m=d.rootBoundary,g=d.altBoundary,_=d.padding,v=void 0===(v=d.tether)||v,d=void 0===(d=d.tetherOffset)?0:d,p=Se(h,{boundary:p,rootBoundary:m,padding:_,altBoundary:g}),m=I(h.placement),g=!(_=xe(h.placement)),b="x"===(m=re(m))?"y":"x",y=h.modifiersData.popperOffsets,w=h.rects.reference,E=h.rects.popper,d="function"==typeof d?d(Object.assign({},h.rects,{placement:h.placement})):d,A={x:0,y:0};y&&((u||f)&&(o="y"===m?"height":"width",e=y[m],i=y[m]+p[c="y"===m?L:D],n=y[m]-p[a="y"===m?k:x],r=v?-E[o]/2:0,s=(_===S?w:E)[o],_=_===S?-E[o]:-w[o],E=h.elements.arrow,E=v&&E?ee(E):{width:0,height:0},c=(l=h.modifiersData["arrow#persistent"]?h.modifiersData["arrow#persistent"].padding:he())[c],l=l[a],a=ce(0,w[o],E[o]),E=g?w[o]/2-r-a-c-d:s-a-c-d,s=g?-w[o]/2+r+a+l+d:_+a+l+d,g=(c=h.elements.arrow&&oe(h.elements.arrow))?"y"===m?c.clientTop||0:c.clientLeft||0:0,w=h.modifiersData.offset?h.modifiersData.offset[h.placement][m]:0,o=y[m]+E-w-g,r=y[m]+s-w,u&&(_=ce(v?ae(i,o):i,e,v?T(n,r):n),y[m]=_,A[m]=_-e),f&&(l=(a=y[b])+p["x"===m?L:D],d=a-p["x"===m?k:x],c=ce(v?ae(l,o):l,a,v?T(d,r):d),y[b]=c,A[b]=c-a)),h.modifiersData[t]=A)},requiresIfExists:["offset"]};function Be(t,e,i){void 0===i&&(i=!1);var n=A(e),t=te(t),s=y(e),o={scrollLeft:0,scrollTop:0},r={x:0,y:0};return!s&&i||("body"===v(e)&&!Te(n)||(o=(s=e)!==b(s)&&y(s)?{scrollLeft:s.scrollLeft,scrollTop:s.scrollTop}:Ee(s)),y(e)?((r=te(e)).x+=e.clientLeft,r.y+=e.clientTop):n&&(r.x=Ae(n))),{x:t.left+o.scrollLeft-r.x,y:t.top+o.scrollTop-r.y,width:t.width,height:t.height}}function We(t){var i=new Map,n=new Set,s=[];return t.forEach(function(t){i.set(t.name,t)}),t.forEach(function(t){n.has(t.name)||!function e(t){n.add(t.name),[].concat(t.requires||[],t.requiresIfExists||[]).forEach(function(t){n.has(t)||(t=i.get(t))&&e(t)}),s.push(t)}(t)}),s}var qe={placement:"bottom",modifiers:[],strategy:"absolute"};function $e(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];return!e.some(function(t){return!(t&&"function"==typeof t.getBoundingClientRect)})}function ze(t){var t=t=void 0===t?{}:t,e=t.defaultModifiers,d=void 0===e?[]:e,e=t.defaultOptions,u=void 0===e?qe:e;return function(n,s,e){void 0===e&&(e=u);var i,o,r={placement:"bottom",orderedModifiers:[],options:Object.assign({},qe,u),modifiersData:{},elements:{reference:n,popper:s},attributes:{},styles:{}},a=[],l=!1,c={state:r,setOptions:function(t){h(),r.options=Object.assign({},u,r.options,t),r.scrollParents={reference:Zt(n)?Oe(n):n.contextElement?Oe(n.contextElement):[],popper:Oe(s)};t=[].concat(d,r.options.modifiers),e=t.reduce(function(t,e){var i=t[e.name];return t[e.name]=i?Object.assign({},i,e,{options:Object.assign({},i.options,e.options),data:Object.assign({},i.data,e.data)}):e,t},{}),t=Object.keys(e).map(function(t){return e[t]}),i=We(t);var i,e,t=Gt.reduce(function(t,e){return t.concat(i.filter(function(t){return t.phase===e}))},[]);return r.orderedModifiers=t.filter(function(t){return t.enabled}),r.orderedModifiers.forEach(function(t){var e=t.name,i=t.options,t=t.effect;"function"==typeof t&&(t=t({state:r,name:e,instance:c,options:void 0===i?{}:i}),a.push(t||function(){}))}),c.update()},forceUpdate:function(){if(!l){var t=r.elements,e=t.reference,t=t.popper;if($e(e,t)){r.rects={reference:Be(e,oe(t),"fixed"===r.options.strategy),popper:ee(t)},r.reset=!1,r.placement=r.options.placement,r.orderedModifiers.forEach(function(t){return r.modifiersData[t.name]=Object.assign({},t.data)});for(var i,n,s,o=0;o<r.orderedModifiers.length;o++)!0===r.reset?(r.reset=!1,o=-1):(i=(s=r.orderedModifiers[o]).fn,n=s.options,s=s.name,"function"==typeof i&&(r=i({state:r,options:void 0===n?{}:n,name:s,instance:c})||r))}}},update:(i=function(){return new Promise(function(t){c.forceUpdate(),t(r)})},function(){return o=o||new Promise(function(t){Promise.resolve().then(function(){o=void 0,t(i())})})}),destroy:function(){h(),l=!0}};return $e(n,s)&&c.setOptions(e).then(function(t){!l&&e.onFirstUpdate&&e.onFirstUpdate(t)}),c;function h(){a.forEach(function(t){return t()}),a=[]}}}var Fe=ze({defaultModifiers:[_e,He,me,w,Pe,Ie,Re,O,Me]}),Ue=Object.freeze({__proto__:null,popperGenerator:ze,detectOverflow:Se,createPopperBase:ze(),createPopper:Fe,createPopperLite:ze({defaultModifiers:[_e,He,me,w]}),top:L,bottom:k,right:x,left:D,auto:Ht,basePlacements:Rt,start:S,end:Bt,clippingParents:Wt,viewport:qt,popper:$t,reference:zt,variationPlacements:Ft,placements:Ut,beforeRead:Et,read:"read",afterRead:Vt,beforeMain:Kt,main:"main",afterMain:Xt,beforeWrite:Yt,write:"write",afterWrite:Qt,modifierPhases:Gt,applyStyles:w,arrow:O,computeStyles:me,eventListeners:_e,flip:Ie,hide:Me,offset:Pe,popperOffsets:He,preventOverflow:Re});const Ve="dropdown";Et=".bs.dropdown",Vt=".data-api";const Ke="Escape",Xe="ArrowUp",Ye="ArrowDown",Qe=new RegExp(Xe+`|${Ye}|`+Ke);Kt="click"+Et+Vt,Xt="keydown"+Et+Vt;const C="show",Ge='[data-bs-toggle="dropdown"]',Ze=".dropdown-menu",Je=n()?"top-end":"top-start",ti=n()?"top-start":"top-end",ei=n()?"bottom-end":"bottom-start",ii=n()?"bottom-start":"bottom-end",ni=n()?"left-start":"right-start",si=n()?"right-start":"left-start",oi={offset:[0,2],boundary:"clippingParents",reference:"toggle",display:"dynamic",popperConfig:null,autoClose:!0},ri={offset:"(array|string|function)",boundary:"(string|element)",reference:"(string|element|object)",display:"string",popperConfig:"(null|object|function)",autoClose:"(boolean|string)"};class N extends e{constructor(t,e){super(t),this._popper=null,this._config=this._getConfig(e),this._menu=this._getMenuElement(),this._inNavbar=this._detectNavbar(),this._addEventListeners()}static get Default(){return oi}static get DefaultType(){return ri}static get NAME(){return Ve}toggle(){F(this._element)||(this._element.classList.contains(C)?this.hide():this.show())}show(){if(!F(this._element)&&!this._menu.classList.contains(C)){const i=N.getParentFromElement(this._element);var t={relatedTarget:this._element},e=_.trigger(this._element,"show.bs.dropdown",t);if(!e.defaultPrevented){if(this._inNavbar)l.setDataAttribute(this._menu,"popper","none");else{if(void 0===Ue)throw new TypeError("Bootstrap's dropdowns require Popper (https://popper.js.org)");let t=this._element;"parent"===this._config.reference?t=i:r(this._config.reference)?t=$(this._config.reference):"object"==typeof this._config.reference&&(t=this._config.reference);const n=this._getPopperConfig();e=n.modifiers.find(t=>"applyStyles"===t.name&&!1===t.enabled);this._popper=Fe(t,this._menu,n),e&&l.setDataAttribute(this._menu,"popper","static")}"ontouchstart"in document.documentElement&&!i.closest(".navbar-nav")&&[].concat(...document.body.children).forEach(t=>_.on(t,"mouseover",V)),this._element.focus(),this._element.setAttribute("aria-expanded",!0),this._menu.classList.toggle(C),this._element.classList.toggle(C),_.trigger(this._element,"shown.bs.dropdown",t)}}}hide(){var t;!F(this._element)&&this._menu.classList.contains(C)&&(t={relatedTarget:this._element},this._completeHide(t))}dispose(){this._popper&&this._popper.destroy(),super.dispose()}update(){this._inNavbar=this._detectNavbar(),this._popper&&this._popper.update()}_addEventListeners(){_.on(this._element,"click.bs.dropdown",t=>{t.preventDefault(),this.toggle()})}_completeHide(t){_.trigger(this._element,"hide.bs.dropdown",t).defaultPrevented||("ontouchstart"in document.documentElement&&[].concat(...document.body.children).forEach(t=>_.off(t,"mouseover",V)),this._popper&&this._popper.destroy(),this._menu.classList.remove(C),this._element.classList.remove(C),this._element.setAttribute("aria-expanded","false"),l.removeDataAttribute(this._menu,"popper"),_.trigger(this._element,"hidden.bs.dropdown",t))}_getConfig(t){if(t={...this.constructor.Default,...l.getDataAttributes(this._element),...t},i(Ve,t,this.constructor.DefaultType),"object"!=typeof t.reference||r(t.reference)||"function"==typeof t.reference.getBoundingClientRect)return t;throw new TypeError(Ve.toUpperCase()+': Option "reference" provided type "object" without a required "getBoundingClientRect" method.')}_getMenuElement(){return d.next(this._element,Ze)[0]}_getPlacement(){const t=this._element.parentNode;if(t.classList.contains("dropend"))return ni;if(t.classList.contains("dropstart"))return si;var e="end"===getComputedStyle(this._menu).getPropertyValue("--bs-position").trim();return t.classList.contains("dropup")?e?ti:Je:e?ii:ei}_detectNavbar(){return null!==this._element.closest(".navbar")}_getOffset(){const e=this._config["offset"];return"string"==typeof e?e.split(",").map(t=>Number.parseInt(t,10)):"function"==typeof e?t=>e(t,this._element):e}_getPopperConfig(){const t={placement:this._getPlacement(),modifiers:[{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"offset",options:{offset:this._getOffset()}}]};return"static"===this._config.display&&(t.modifiers=[{name:"applyStyles",enabled:!1}]),{...t,..."function"==typeof this._config.popperConfig?this._config.popperConfig(t):this._config.popperConfig}}_selectMenuItem({key:t,target:e}){const i=d.find(".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)",this._menu).filter(z);i.length&&G(i,e,t===Ye,!i.includes(e)).focus()}static dropdownInterface(t,e){const i=N.getOrCreateInstance(t,e);if("string"==typeof e){if(void 0===i[e])throw new TypeError(`No method named "${e}"`);i[e]()}}static jQueryInterface(t){return this.each(function(){N.dropdownInterface(this,t)})}static clearMenus(i){if(!i||2!==i.button&&("keyup"!==i.type||"Tab"===i.key)){var n=d.find(Ge);for(let t=0,e=n.length;t<e;t++){const o=N.getInstance(n[t]);if(o&&!1!==o._config.autoClose&&o._element.classList.contains(C)){const r={relatedTarget:o._element};if(i){const a=i.composedPath();var s=a.includes(o._menu);if(a.includes(o._element)||"inside"===o._config.autoClose&&!s||"outside"===o._config.autoClose&&s)continue;if(o._menu.contains(i.target)&&("keyup"===i.type&&"Tab"===i.key||/input|select|option|textarea|form/i.test(i.target.tagName)))continue;"click"===i.type&&(r.clickEvent=i)}o._completeHide(r)}}}}static getParentFromElement(t){return s(t)||t.parentNode}static dataApiKeydownHandler(t){if(/input|textarea/i.test(t.target.tagName)?!("Space"===t.key||t.key!==Ke&&(t.key!==Ye&&t.key!==Xe||t.target.closest(Ze))):Qe.test(t.key)){var e,i=this.classList.contains(C);if(i||t.key!==Ke)if(t.preventDefault(),t.stopPropagation(),!F(this))return e=()=>this.matches(Ge)?this:d.prev(this,Ge)[0],t.key===Ke?(e().focus(),void N.clearMenus()):t.key===Xe||t.key===Ye?(i||e().click(),void N.getInstance(e())._selectMenuItem(t)):void(i&&"Space"!==t.key||N.clearMenus())}}}_.on(document,Xt,Ge,N.dataApiKeydownHandler),_.on(document,Xt,Ze,N.dataApiKeydownHandler),_.on(document,Kt,N.clearMenus),_.on(document,"keyup.bs.dropdown.data-api",N.clearMenus),_.on(document,Kt,Ge,function(t){t.preventDefault(),N.dropdownInterface(this)}),t(N);const ai=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",li=".sticky-top";class ci{constructor(){this._element=document.body}getWidth(){var t=document.documentElement.clientWidth;return Math.abs(window.innerWidth-t)}hide(){const e=this.getWidth();this._disableOverFlow(),this._setElementAttributes(this._element,"paddingRight",t=>t+e),this._setElementAttributes(ai,"paddingRight",t=>t+e),this._setElementAttributes(li,"marginRight",t=>t-e)}_disableOverFlow(){this._saveInitialAttribute(this._element,"overflow"),this._element.style.overflow="hidden"}_setElementAttributes(t,i,n){const s=this.getWidth();this._applyManipulationCallback(t,t=>{var e;t!==this._element&&window.innerWidth>t.clientWidth+s||(this._saveInitialAttribute(t,i),e=window.getComputedStyle(t)[i],t.style[i]=n(Number.parseFloat(e))+"px")})}reset(){this._resetElementAttributes(this._element,"overflow"),this._resetElementAttributes(this._element,"paddingRight"),this._resetElementAttributes(ai,"paddingRight"),this._resetElementAttributes(li,"marginRight")}_saveInitialAttribute(t,e){var i=t.style[e];i&&l.setDataAttribute(t,e,i)}_resetElementAttributes(t,i){this._applyManipulationCallback(t,t=>{var e=l.getDataAttribute(t,i);void 0===e?t.style.removeProperty(i):(l.removeDataAttribute(t,i),t.style[i]=e)})}_applyManipulationCallback(t,e){r(t)?e(t):d.find(t,this._element).forEach(e)}isOverflowing(){return 0<this.getWidth()}}const hi={isVisible:!0,isAnimated:!1,rootElement:"body",clickCallback:null},di={isVisible:"boolean",isAnimated:"boolean",rootElement:"(element|string)",clickCallback:"(function|null)"},ui="backdrop",fi="mousedown.bs."+ui;class pi{constructor(t){this._config=this._getConfig(t),this._isAppended=!1,this._element=null}show(t){this._config.isVisible?(this._append(),this._config.isAnimated&&K(this._getElement()),this._getElement().classList.add("show"),this._emulateAnimation(()=>{o(t)})):o(t)}hide(t){this._config.isVisible?(this._getElement().classList.remove("show"),this._emulateAnimation(()=>{this.dispose(),o(t)})):o(t)}_getElement(){if(!this._element){const t=document.createElement("div");t.className="modal-backdrop",this._config.isAnimated&&t.classList.add("fade"),this._element=t}return this._element}_getConfig(t){return(t={...hi,..."object"==typeof t?t:{}}).rootElement=$(t.rootElement),i(ui,t,di),t}_append(){this._isAppended||(this._config.rootElement.appendChild(this._getElement()),_.on(this._getElement(),fi,()=>{o(this._config.clickCallback)}),this._isAppended=!0)}dispose(){this._isAppended&&(_.off(this._element,fi),this._element.remove(),this._isAppended=!1)}_emulateAnimation(t){Q(t,this._getElement(),this._config.isAnimated)}}const j=".bs.modal";const mi={backdrop:!0,keyboard:!0,focus:!0},gi={backdrop:"(boolean|string)",keyboard:"boolean",focus:"boolean"},_i=(j,j,"hidden"+j),vi="show"+j,bi=(j,"focusin"+j),yi="resize"+j,wi="click.dismiss"+j,Ei="keydown.dismiss"+j,Ai=(j,"mousedown.dismiss"+j);j;const Ti="modal-open",Oi="modal-static";class Ci extends e{constructor(t,e){super(t),this._config=this._getConfig(e),this._dialog=d.findOne(".modal-dialog",this._element),this._backdrop=this._initializeBackDrop(),this._isShown=!1,this._ignoreBackdropClick=!1,this._isTransitioning=!1,this._scrollBar=new ci}static get Default(){return mi}static get NAME(){return"modal"}toggle(t){return this._isShown?this.hide():this.show(t)}show(t){this._isShown||this._isTransitioning||_.trigger(this._element,vi,{relatedTarget:t}).defaultPrevented||(this._isShown=!0,this._isAnimated()&&(this._isTransitioning=!0),this._scrollBar.hide(),document.body.classList.add(Ti),this._adjustDialog(),this._setEscapeEvent(),this._setResizeEvent(),_.on(this._element,wi,'[data-bs-dismiss="modal"]',t=>this.hide(t)),_.on(this._dialog,Ai,()=>{_.one(this._element,"mouseup.dismiss.bs.modal",t=>{t.target===this._element&&(this._ignoreBackdropClick=!0)})}),this._showBackdrop(()=>this._showElement(t)))}hide(t){t&&["A","AREA"].includes(t.target.tagName)&&t.preventDefault(),!this._isShown||this._isTransitioning||_.trigger(this._element,"hide.bs.modal").defaultPrevented||(this._isShown=!1,(t=this._isAnimated())&&(this._isTransitioning=!0),this._setEscapeEvent(),this._setResizeEvent(),_.off(document,bi),this._element.classList.remove("show"),_.off(this._element,wi),_.off(this._dialog,Ai),this._queueCallback(()=>this._hideModal(),this._element,t))}dispose(){[window,this._dialog].forEach(t=>_.off(t,j)),this._backdrop.dispose(),super.dispose(),_.off(document,bi)}handleUpdate(){this._adjustDialog()}_initializeBackDrop(){return new pi({isVisible:Boolean(this._config.backdrop),isAnimated:this._isAnimated()})}_getConfig(t){return t={...mi,...l.getDataAttributes(this._element),..."object"==typeof t?t:{}},i("modal",t,gi),t}_showElement(t){var e=this._isAnimated();const i=d.findOne(".modal-body",this._dialog);this._element.parentNode&&this._element.parentNode.nodeType===Node.ELEMENT_NODE||document.body.appendChild(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.scrollTop=0,i&&(i.scrollTop=0),e&&K(this._element),this._element.classList.add("show"),this._config.focus&&this._enforceFocus();this._queueCallback(()=>{this._config.focus&&this._element.focus(),this._isTransitioning=!1,_.trigger(this._element,"shown.bs.modal",{relatedTarget:t})},this._dialog,e)}_enforceFocus(){_.off(document,bi),_.on(document,bi,t=>{document===t.target||this._element===t.target||this._element.contains(t.target)||this._element.focus()})}_setEscapeEvent(){this._isShown?_.on(this._element,Ei,t=>{this._config.keyboard&&"Escape"===t.key?(t.preventDefault(),this.hide()):this._config.keyboard||"Escape"!==t.key||this._triggerBackdropTransition()}):_.off(this._element,Ei)}_setResizeEvent(){this._isShown?_.on(window,yi,()=>this._adjustDialog()):_.off(window,yi)}_hideModal(){this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._isTransitioning=!1,this._backdrop.hide(()=>{document.body.classList.remove(Ti),this._resetAdjustments(),this._scrollBar.reset(),_.trigger(this._element,_i)})}_showBackdrop(t){_.on(this._element,wi,t=>{this._ignoreBackdropClick?this._ignoreBackdropClick=!1:t.target===t.currentTarget&&(!0===this._config.backdrop?this.hide():"static"===this._config.backdrop&&this._triggerBackdropTransition())}),this._backdrop.show(t)}_isAnimated(){return this._element.classList.contains("fade")}_triggerBackdropTransition(){var t=_.trigger(this._element,"hidePrevented.bs.modal");if(!t.defaultPrevented){const{classList:e,scrollHeight:i,style:n}=this._element,s=i>document.documentElement.clientHeight;!s&&"hidden"===n.overflowY||e.contains(Oi)||(s||(n.overflowY="hidden"),e.add(Oi),this._queueCallback(()=>{e.remove(Oi),s||this._queueCallback(()=>{n.overflowY=""},this._dialog)},this._dialog),this._element.focus())}}_adjustDialog(){var t=this._element.scrollHeight>document.documentElement.clientHeight,e=this._scrollBar.getWidth(),i=0<e;(!i&&t&&!n()||i&&!t&&n())&&(this._element.style.paddingLeft=e+"px"),(i&&!t&&!n()||!i&&t&&n())&&(this._element.style.paddingRight=e+"px")}_resetAdjustments(){this._element.style.paddingLeft="",this._element.style.paddingRight=""}static jQueryInterface(e,i){return this.each(function(){const t=Ci.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e])throw new TypeError(`No method named "${e}"`);t[e](i)}})}}_.on(document,"click.bs.modal.data-api",'[data-bs-toggle="modal"]',function(t){const e=s(this),i=(["A","AREA"].includes(this.tagName)&&t.preventDefault(),_.one(e,vi,t=>{t.defaultPrevented||_.one(e,_i,()=>{z(this)&&this.focus()})}),Ci.getOrCreateInstance(e));i.toggle(this)}),t(Ci);const Li="offcanvas";Yt=".bs.offcanvas";const ki={backdrop:!0,keyboard:!0,scroll:!1},xi={backdrop:"boolean",keyboard:"boolean",scroll:"boolean"},Di=".offcanvas.show",Si="hidden"+Yt,Ii="focusin"+Yt;class Ni extends e{constructor(t,e){super(t),this._config=this._getConfig(e),this._isShown=!1,this._backdrop=this._initializeBackDrop(),this._addEventListeners()}static get NAME(){return Li}static get Default(){return ki}toggle(t){return this._isShown?this.hide():this.show(t)}show(t){this._isShown||_.trigger(this._element,"show.bs.offcanvas",{relatedTarget:t}).defaultPrevented||(this._isShown=!0,this._element.style.visibility="visible",this._backdrop.show(),this._config.scroll||((new ci).hide(),this._enforceFocusOnElement(this._element)),this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.classList.add("show"),this._queueCallback(()=>{_.trigger(this._element,"shown.bs.offcanvas",{relatedTarget:t})},this._element,!0))}hide(){this._isShown&&!_.trigger(this._element,"hide.bs.offcanvas").defaultPrevented&&(_.off(document,Ii),this._element.blur(),this._isShown=!1,this._element.classList.remove("show"),this._backdrop.hide(),this._queueCallback(()=>{this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._element.style.visibility="hidden",this._config.scroll||(new ci).reset(),_.trigger(this._element,Si)},this._element,!0))}dispose(){this._backdrop.dispose(),super.dispose(),_.off(document,Ii)}_getConfig(t){return t={...ki,...l.getDataAttributes(this._element),..."object"==typeof t?t:{}},i(Li,t,xi),t}_initializeBackDrop(){return new pi({isVisible:this._config.backdrop,isAnimated:!0,rootElement:this._element.parentNode,clickCallback:()=>this.hide()})}_enforceFocusOnElement(e){_.off(document,Ii),_.on(document,Ii,t=>{document===t.target||e===t.target||e.contains(t.target)||e.focus()}),e.focus()}_addEventListeners(){_.on(this._element,"click.dismiss.bs.offcanvas",'[data-bs-dismiss="offcanvas"]',()=>this.hide()),_.on(this._element,"keydown.dismiss.bs.offcanvas",t=>{this._config.keyboard&&"Escape"===t.key&&this.hide()})}static jQueryInterface(e){return this.each(function(){const t=Ni.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e]||e.startsWith("_")||"constructor"===e)throw new TypeError(`No method named "${e}"`);t[e](this)}})}}_.on(document,"click.bs.offcanvas.data-api",'[data-bs-toggle="offcanvas"]',function(t){var e=s(this);if(["A","AREA"].includes(this.tagName)&&t.preventDefault(),!F(this)){_.one(e,Si,()=>{z(this)&&this.focus()});t=d.findOne(Di);t&&t!==e&&Ni.getInstance(t).hide();const i=Ni.getOrCreateInstance(e);i.toggle(this)}}),_.on(window,"load.bs.offcanvas.data-api",()=>d.find(Di).forEach(t=>Ni.getOrCreateInstance(t).show())),t(Ni);const ji=new Set(["background","cite","href","itemtype","longdesc","poster","src","xlink:href"]);const Mi=/^(?:(?:https?|mailto|ftp|tel|file):|[^#&/:?]*(?:[#/?]|$))/i,Pi=/^data:(?:image\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\/(?:mpeg|mp4|ogg|webm)|audio\/(?:mp3|oga|ogg|opus));base64,[\d+/a-z]+=*$/i;Qt={"*":["class","dir","id","lang","role",/^aria-[\w-]*$/i],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],div:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]};function Hi(t,i,e){if(!t.length)return t;if(e&&"function"==typeof e)return e(t);const n=new window.DOMParser,s=n.parseFromString(t,"text/html"),o=Object.keys(i);var r=[].concat(...s.body.querySelectorAll("*"));for(let t=0,e=r.length;t<e;t++){const l=r[t];var a=l.nodeName.toLowerCase();if(o.includes(a)){const c=[].concat(...l.attributes),h=[].concat(i["*"]||[],i[a]||[]);c.forEach(t=>{((t,e)=>{var i=t.nodeName.toLowerCase();if(e.includes(i))return!ji.has(i)||Boolean(Mi.test(t.nodeValue)||Pi.test(t.nodeValue));const n=e.filter(t=>t instanceof RegExp);for(let t=0,e=n.length;t<e;t++)if(n[t].test(i))return!0;return!1})(t,h)||l.removeAttribute(t.nodeName)})}else l.remove()}return s.body.innerHTML}const Ri="tooltip";w=".bs.tooltip";const Bi="bs-tooltip",Wi=new RegExp(`(^|\\s)${Bi}\\S+`,"g"),qi=new Set(["sanitize","allowList","sanitizeFn"]),$i={animation:"boolean",template:"string",title:"(string|element|function)",trigger:"string",delay:"(number|object)",html:"boolean",selector:"(string|boolean)",placement:"(string|function)",offset:"(array|string|function)",container:"(string|element|boolean)",fallbackPlacements:"array",boundary:"(string|element)",customClass:"(string|function)",sanitize:"boolean",sanitizeFn:"(null|function)",allowList:"object",popperConfig:"(null|object|function)"},zi={AUTO:"auto",TOP:"top",RIGHT:n()?"left":"right",BOTTOM:"bottom",LEFT:n()?"right":"left"},Fi={animation:!0,template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',trigger:"hover focus",title:"",delay:0,html:!1,selector:!1,placement:"top",offset:[0,0],container:!1,fallbackPlacements:["top","right","bottom","left"],boundary:"clippingParents",customClass:"",sanitize:!0,sanitizeFn:null,allowList:Qt,popperConfig:null},Ui={HIDE:"hide"+w,HIDDEN:"hidden"+w,SHOW:"show"+w,SHOWN:"shown"+w,INSERTED:"inserted"+w,CLICK:"click"+w,FOCUSIN:"focusin"+w,FOCUSOUT:"focusout"+w,MOUSEENTER:"mouseenter"+w,MOUSELEAVE:"mouseleave"+w},Vi="fade",Ki="show",Xi="show",Yi="hover",Qi="focus";class Gi extends e{constructor(t,e){if(void 0===Ue)throw new TypeError("Bootstrap's tooltips require Popper (https://popper.js.org)");super(t),this._isEnabled=!0,this._timeout=0,this._hoverState="",this._activeTrigger={},this._popper=null,this._config=this._getConfig(e),this.tip=null,this._setListeners()}static get Default(){return Fi}static get NAME(){return Ri}static get Event(){return Ui}static get DefaultType(){return $i}enable(){this._isEnabled=!0}disable(){this._isEnabled=!1}toggleEnabled(){this._isEnabled=!this._isEnabled}toggle(t){if(this._isEnabled)if(t){const e=this._initializeOnDelegatedTarget(t);e._activeTrigger.click=!e._activeTrigger.click,e._isWithActiveTrigger()?e._enter(null,e):e._leave(null,e)}else this.getTipElement().classList.contains(Ki)?this._leave(null,this):this._enter(null,this)}dispose(){clearTimeout(this._timeout),_.off(this._element.closest(".modal"),"hide.bs.modal",this._hideModalHandler),this.tip&&this.tip.remove(),this._popper&&this._popper.destroy(),super.dispose()}show(){if("none"===this._element.style.display)throw new Error("Please use show on visible elements");if(this.isWithContent()&&this._isEnabled){var t=_.trigger(this._element,this.constructor.Event.SHOW);const i=U(this._element);var e=(null===i?this._element.ownerDocument.documentElement:i).contains(this._element);if(!t.defaultPrevented&&e){const n=this.getTipElement();t=R(this.constructor.NAME),e=(n.setAttribute("id",t),this._element.setAttribute("aria-describedby",t),this.setContent(),this._config.animation&&n.classList.add(Vi),"function"==typeof this._config.placement?this._config.placement.call(this,n,this._element):this._config.placement),t=this._getAttachment(e);this._addAttachmentClass(t);const s=this._config["container"],o=(ft(n,this.constructor.DATA_KEY,this),this._element.ownerDocument.documentElement.contains(this.tip)||(s.appendChild(n),_.trigger(this._element,this.constructor.Event.INSERTED)),this._popper?this._popper.update():this._popper=Fe(this._element,n,this._getPopperConfig(t)),n.classList.add(Ki),"function"==typeof this._config.customClass?this._config.customClass():this._config.customClass);o&&n.classList.add(...o.split(" ")),"ontouchstart"in document.documentElement&&[].concat(...document.body.children).forEach(t=>{_.on(t,"mouseover",V)});e=this.tip.classList.contains(Vi);this._queueCallback(()=>{var t=this._hoverState;this._hoverState=null,_.trigger(this._element,this.constructor.Event.SHOWN),"out"===t&&this._leave(null,this)},this.tip,e)}}}hide(){if(this._popper){const e=this.getTipElement();var t;_.trigger(this._element,this.constructor.Event.HIDE).defaultPrevented||(e.classList.remove(Ki),"ontouchstart"in document.documentElement&&[].concat(...document.body.children).forEach(t=>_.off(t,"mouseover",V)),this._activeTrigger.click=!1,this._activeTrigger[Qi]=!1,this._activeTrigger[Yi]=!1,t=this.tip.classList.contains(Vi),this._queueCallback(()=>{this._isWithActiveTrigger()||(this._hoverState!==Xi&&e.remove(),this._cleanTipClass(),this._element.removeAttribute("aria-describedby"),_.trigger(this._element,this.constructor.Event.HIDDEN),this._popper&&(this._popper.destroy(),this._popper=null))},this.tip,t),this._hoverState="")}}update(){null!==this._popper&&this._popper.update()}isWithContent(){return Boolean(this.getTitle())}getTipElement(){if(this.tip)return this.tip;const t=document.createElement("div");return t.innerHTML=this._config.template,this.tip=t.children[0],this.tip}setContent(){const t=this.getTipElement();this.setElementContent(d.findOne(".tooltip-inner",t),this.getTitle()),t.classList.remove(Vi,Ki)}setElementContent(t,e){if(null!==t)return r(e)?(e=$(e),void(this._config.html?e.parentNode!==t&&(t.innerHTML="",t.appendChild(e)):t.textContent=e.textContent)):void(this._config.html?(this._config.sanitize&&(e=Hi(e,this._config.allowList,this._config.sanitizeFn)),t.innerHTML=e):t.textContent=e)}getTitle(){let t=this._element.getAttribute("data-bs-original-title");return t=t||("function"==typeof this._config.title?this._config.title.call(this._element):this._config.title)}updateAttachment(t){return"right"===t?"end":"left"===t?"start":t}_initializeOnDelegatedTarget(t,e){var i=this.constructor.DATA_KEY;return(e=e||pt(t.delegateTarget,i))||(e=new this.constructor(t.delegateTarget,this._getDelegateConfig()),ft(t.delegateTarget,i,e)),e}_getOffset(){const e=this._config["offset"];return"string"==typeof e?e.split(",").map(t=>Number.parseInt(t,10)):"function"==typeof e?t=>e(t,this._element):e}_getPopperConfig(t){t={placement:t,modifiers:[{name:"flip",options:{fallbackPlacements:this._config.fallbackPlacements}},{name:"offset",options:{offset:this._getOffset()}},{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"arrow",options:{element:`.${this.constructor.NAME}-arrow`}},{name:"onChange",enabled:!0,phase:"afterWrite",fn:t=>this._handlePopperPlacementChange(t)}],onFirstUpdate:t=>{t.options.placement!==t.placement&&this._handlePopperPlacementChange(t)}};return{...t,..."function"==typeof this._config.popperConfig?this._config.popperConfig(t):this._config.popperConfig}}_addAttachmentClass(t){this.getTipElement().classList.add(Bi+"-"+this.updateAttachment(t))}_getAttachment(t){return zi[t.toUpperCase()]}_setListeners(){const t=this._config.trigger.split(" ");t.forEach(t=>{var e;"click"===t?_.on(this._element,this.constructor.Event.CLICK,this._config.selector,t=>this.toggle(t)):"manual"!==t&&(e=t===Yi?this.constructor.Event.MOUSEENTER:this.constructor.Event.FOCUSIN,t=t===Yi?this.constructor.Event.MOUSELEAVE:this.constructor.Event.FOCUSOUT,_.on(this._element,e,this._config.selector,t=>this._enter(t)),_.on(this._element,t,this._config.selector,t=>this._leave(t)))}),this._hideModalHandler=()=>{this._element&&this.hide()},_.on(this._element.closest(".modal"),"hide.bs.modal",this._hideModalHandler),this._config.selector?this._config={...this._config,trigger:"manual",selector:""}:this._fixTitle()}_fixTitle(){var t=this._element.getAttribute("title"),e=typeof this._element.getAttribute("data-bs-original-title");!t&&"string"==e||(this._element.setAttribute("data-bs-original-title",t||""),!t||this._element.getAttribute("aria-label")||this._element.textContent||this._element.setAttribute("aria-label",t),this._element.setAttribute("title",""))}_enter(t,e){e=this._initializeOnDelegatedTarget(t,e),t&&(e._activeTrigger["focusin"===t.type?Qi:Yi]=!0),e.getTipElement().classList.contains(Ki)||e._hoverState===Xi?e._hoverState=Xi:(clearTimeout(e._timeout),e._hoverState=Xi,e._config.delay&&e._config.delay.show?e._timeout=setTimeout(()=>{e._hoverState===Xi&&e.show()},e._config.delay.show):e.show())}_leave(t,e){e=this._initializeOnDelegatedTarget(t,e),t&&(e._activeTrigger["focusout"===t.type?Qi:Yi]=e._element.contains(t.relatedTarget)),e._isWithActiveTrigger()||(clearTimeout(e._timeout),e._hoverState="out",e._config.delay&&e._config.delay.hide?e._timeout=setTimeout(()=>{"out"===e._hoverState&&e.hide()},e._config.delay.hide):e.hide())}_isWithActiveTrigger(){for(const t in this._activeTrigger)if(this._activeTrigger[t])return!0;return!1}_getConfig(t){const e=l.getDataAttributes(this._element);return Object.keys(e).forEach(t=>{qi.has(t)&&delete e[t]}),(t={...this.constructor.Default,...e,..."object"==typeof t&&t?t:{}}).container=!1===t.container?document.body:$(t.container),"number"==typeof t.delay&&(t.delay={show:t.delay,hide:t.delay}),"number"==typeof t.title&&(t.title=t.title.toString()),"number"==typeof t.content&&(t.content=t.content.toString()),i(Ri,t,this.constructor.DefaultType),t.sanitize&&(t.template=Hi(t.template,t.allowList,t.sanitizeFn)),t}_getDelegateConfig(){const t={};if(this._config)for(const e in this._config)this.constructor.Default[e]!==this._config[e]&&(t[e]=this._config[e]);return t}_cleanTipClass(){const e=this.getTipElement(),t=e.getAttribute("class").match(Wi);null!==t&&0<t.length&&t.map(t=>t.trim()).forEach(t=>e.classList.remove(t))}_handlePopperPlacementChange(t){t=t.state;t&&(this.tip=t.elements.popper,this._cleanTipClass(),this._addAttachmentClass(this._getAttachment(t.placement)))}static jQueryInterface(e){return this.each(function(){const t=Gi.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e])throw new TypeError(`No method named "${e}"`);t[e]()}})}}t(Gi);O=".bs.popover";const Zi="bs-popover",Ji=new RegExp(`(^|\\s)${Zi}\\S+`,"g"),tn={...Gi.Default,placement:"right",offset:[0,8],trigger:"click",content:"",template:'<div class="popover" role="tooltip"><div class="popover-arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>'},en={...Gi.DefaultType,content:"(string|element|function)"},nn={HIDE:"hide"+O,HIDDEN:"hidden"+O,SHOW:"show"+O,SHOWN:"shown"+O,INSERTED:"inserted"+O,CLICK:"click"+O,FOCUSIN:"focusin"+O,FOCUSOUT:"focusout"+O,MOUSEENTER:"mouseenter"+O,MOUSELEAVE:"mouseleave"+O},sn=".popover-header",on=".popover-body";class rn extends Gi{static get Default(){return tn}static get NAME(){return"popover"}static get Event(){return nn}static get DefaultType(){return en}isWithContent(){return this.getTitle()||this._getContent()}getTipElement(){return this.tip||(this.tip=super.getTipElement(),this.getTitle()||d.findOne(sn,this.tip).remove(),this._getContent()||d.findOne(on,this.tip).remove(),this.tip)}setContent(){const t=this.getTipElement();this.setElementContent(d.findOne(sn,t),this.getTitle());let e=this._getContent();"function"==typeof e&&(e=e.call(this._element)),this.setElementContent(d.findOne(on,t),e),t.classList.remove("fade","show")}_addAttachmentClass(t){this.getTipElement().classList.add(Zi+"-"+this.updateAttachment(t))}_getContent(){return this._element.getAttribute("data-bs-content")||this._config.content}_cleanTipClass(){const e=this.getTipElement(),t=e.getAttribute("class").match(Ji);null!==t&&0<t.length&&t.map(t=>t.trim()).forEach(t=>e.classList.remove(t))}static jQueryInterface(e){return this.each(function(){const t=rn.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e])throw new TypeError(`No method named "${e}"`);t[e]()}})}}t(rn);const an="scrollspy";const ln=".bs.scrollspy";const cn={offset:10,method:"auto",target:""},hn={offset:"number",method:"string",target:"(string|element)"};ln,ln;ln;const dn="dropdown-item",M="active",un=".nav-link",fn=".list-group-item",pn="position";class mn extends e{constructor(t,e){super(t),this._scrollElement="BODY"===this._element.tagName?window:this._element,this._config=this._getConfig(e),this._selector=`${this._config.target} ${un}, ${this._config.target} ${fn}, ${this._config.target} .`+dn,this._offsets=[],this._targets=[],this._activeTarget=null,this._scrollHeight=0,_.on(this._scrollElement,"scroll.bs.scrollspy",()=>this._process()),this.refresh(),this._process()}static get Default(){return cn}static get NAME(){return an}refresh(){var t=this._scrollElement===this._scrollElement.window?"offset":pn;const n="auto"===this._config.method?t:this._config.method,s=n===pn?this._getScrollTop():0,e=(this._offsets=[],this._targets=[],this._scrollHeight=this._getScrollHeight(),d.find(this._selector));e.map(t=>{t=W(t);const e=t?d.findOne(t):null;if(e){var i=e.getBoundingClientRect();if(i.width||i.height)return[l[n](e).top+s,t]}return null}).filter(t=>t).sort((t,e)=>t[0]-e[0]).forEach(t=>{this._offsets.push(t[0]),this._targets.push(t[1])})}dispose(){_.off(this._scrollElement,ln),super.dispose()}_getConfig(e){if("string"!=typeof(e={...cn,...l.getDataAttributes(this._element),..."object"==typeof e&&e?e:{}}).target&&r(e.target)){let t=e.target["id"];t||(t=R(an),e.target.id=t),e.target="#"+t}return i(an,e,hn),e}_getScrollTop(){return this._scrollElement===window?this._scrollElement.pageYOffset:this._scrollElement.scrollTop}_getScrollHeight(){return this._scrollElement.scrollHeight||Math.max(document.body.scrollHeight,document.documentElement.scrollHeight)}_getOffsetHeight(){return this._scrollElement===window?window.innerHeight:this._scrollElement.getBoundingClientRect().height}_process(){var e=this._getScrollTop()+this._config.offset,t=this._getScrollHeight(),i=this._config.offset+t-this._getOffsetHeight();if(this._scrollHeight!==t&&this.refresh(),i<=e)return t=this._targets[this._targets.length-1],void(this._activeTarget!==t&&this._activate(t));if(this._activeTarget&&e<this._offsets[0]&&0<this._offsets[0])return this._activeTarget=null,void this._clear();for(let t=this._offsets.length;t--;)this._activeTarget!==this._targets[t]&&e>=this._offsets[t]&&(void 0===this._offsets[t+1]||e<this._offsets[t+1])&&this._activate(this._targets[t])}_activate(e){this._activeTarget=e,this._clear();const t=this._selector.split(",").map(t=>t+`[data-bs-target="${e}"],${t}[href="${e}"]`),i=d.findOne(t.join(","));i.classList.contains(dn)?(d.findOne(".dropdown-toggle",i.closest(".dropdown")).classList.add(M),i.classList.add(M)):(i.classList.add(M),d.parents(i,".nav, .list-group").forEach(t=>{d.prev(t,un+", "+fn).forEach(t=>t.classList.add(M)),d.prev(t,".nav-item").forEach(t=>{d.children(t,un).forEach(t=>t.classList.add(M))})})),_.trigger(this._scrollElement,"activate.bs.scrollspy",{relatedTarget:e})}_clear(){d.find(this._selector).filter(t=>t.classList.contains(M)).forEach(t=>t.classList.remove(M))}static jQueryInterface(e){return this.each(function(){const t=mn.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e])throw new TypeError(`No method named "${e}"`);t[e]()}})}}_.on(window,"load.bs.scrollspy.data-api",()=>{d.find('[data-bs-spy="scroll"]').forEach(t=>new mn(t))}),t(mn);const gn="active",_n=".active",vn=":scope > li > .active";class bn extends e{static get NAME(){return"tab"}show(){if(!this._element.parentNode||this._element.parentNode.nodeType!==Node.ELEMENT_NODE||!this._element.classList.contains(gn)){let t;var e=s(this._element),i=this._element.closest(".nav, .list-group"),n=(i&&(n="UL"===i.nodeName||"OL"===i.nodeName?vn:_n,t=(t=d.find(n,i))[t.length-1]),t?_.trigger(t,"hide.bs.tab",{relatedTarget:this._element}):null);_.trigger(this._element,"show.bs.tab",{relatedTarget:t}).defaultPrevented||null!==n&&n.defaultPrevented||(this._activate(this._element,i),n=()=>{_.trigger(t,"hidden.bs.tab",{relatedTarget:this._element}),_.trigger(this._element,"shown.bs.tab",{relatedTarget:t})},e?this._activate(e,e.parentNode,n):n())}}_activate(t,e,i){const n=(!e||"UL"!==e.nodeName&&"OL"!==e.nodeName?d.children(e,_n):d.find(vn,e))[0];var e=i&&n&&n.classList.contains("fade"),s=()=>this._transitionComplete(t,n,i);n&&e?(n.classList.remove("show"),this._queueCallback(s,t,!0)):s()}_transitionComplete(t,e,i){if(e){e.classList.remove(gn);const s=d.findOne(":scope > .dropdown-menu .active",e.parentNode);s&&s.classList.remove(gn),"tab"===e.getAttribute("role")&&e.setAttribute("aria-selected",!1)}t.classList.add(gn),"tab"===t.getAttribute("role")&&t.setAttribute("aria-selected",!0),K(t),t.classList.contains("fade")&&t.classList.add("show");let n=t.parentNode;(n=n&&"LI"===n.nodeName?n.parentNode:n)&&n.classList.contains("dropdown-menu")&&((e=t.closest(".dropdown"))&&d.find(".dropdown-toggle",e).forEach(t=>t.classList.add(gn)),t.setAttribute("aria-expanded",!0)),i&&i()}static jQueryInterface(e){return this.each(function(){const t=bn.getOrCreateInstance(this);if("string"==typeof e){if(void 0===t[e])throw new TypeError(`No method named "${e}"`);t[e]()}})}}_.on(document,"click.bs.tab.data-api",'[data-bs-toggle="tab"], [data-bs-toggle="pill"], [data-bs-toggle="list"]',function(t){if(["A","AREA"].includes(this.tagName)&&t.preventDefault(),!F(this)){const e=bn.getOrCreateInstance(this);e.show()}}),t(bn);const yn="show",wn="showing",En={animation:"boolean",autohide:"boolean",delay:"number"},An={animation:!0,autohide:!0,delay:5e3};class Tn extends e{constructor(t,e){super(t),this._config=this._getConfig(e),this._timeout=null,this._hasMouseInteraction=!1,this._hasKeyboardInteraction=!1,this._setListeners()}static get DefaultType(){return En}static get Default(){return An}static get NAME(){return"toast"}show(){_.trigger(this._element,"show.bs.toast").defaultPrevented||(this._clearTimeout(),this._config.animation&&this._element.classList.add("fade"),this._element.classList.remove("hide"),K(this._element),this._element.classList.add(wn),this._queueCallback(()=>{this._element.classList.remove(wn),this._element.classList.add(yn),_.trigger(this._element,"shown.bs.toast"),this._maybeScheduleHide()},this._element,this._config.animation))}hide(){this._element.classList.contains(yn)&&!_.trigger(this._element,"hide.bs.toast").defaultPrevented&&(this._element.classList.remove(yn),this._queueCallback(()=>{this._element.classList.add("hide"),_.trigger(this._element,"hidden.bs.toast")},this._element,this._config.animation))}dispose(){this._clearTimeout(),this._element.classList.contains(yn)&&this._element.classList.remove(yn),super.dispose()}_getConfig(t){return t={...An,...l.getDataAttributes(this._element),..."object"==typeof t&&t?t:{}},i("toast",t,this.constructor.DefaultType),t}_maybeScheduleHide(){!this._config.autohide||this._hasMouseInteraction||this._hasKeyboardInteraction||(this._timeout=setTimeout(()=>{this.hide()},this._config.delay))}_onInteraction(t,e){switch(t.type){case"mouseover":case"mouseout":this._hasMouseInteraction=e;break;case"focusin":case"focusout":this._hasKeyboardInteraction=e}e?this._clearTimeout():(t=t.relatedTarget,this._element===t||this._element.contains(t)||this._maybeScheduleHide())}_setListeners(){_.on(this._element,"click.dismiss.bs.toast",'[data-bs-dismiss="toast"]',()=>this.hide()),_.on(this._element,"mouseover.bs.toast",t=>this._onInteraction(t,!0)),_.on(this._element,"mouseout.bs.toast",t=>this._onInteraction(t,!1)),_.on(this._element,"focusin.bs.toast",t=>this._onInteraction(t,!0)),_.on(this._element,"focusout.bs.toast",t=>this._onInteraction(t,!1))}_clearTimeout(){clearTimeout(this._timeout),this._timeout=null}static jQueryInterface(e){return this.each(function(){const t=Tn.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e])throw new TypeError(`No method named "${e}"`);t[e](this)}})}}return t(Tn),{Alert:gt,Button:vt,Carousel:p,Collapse:g,Dropdown:N,Modal:Ci,Offcanvas:Ni,Popover:rn,ScrollSpy:mn,Tab:bn,Toast:Tn,Tooltip:Gi}});
//# sourceMappingURL=bootstrap.bundle.min.js.map
