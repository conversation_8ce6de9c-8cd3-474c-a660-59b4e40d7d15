@page
@model LoginModel

@{
    ViewData["Title"] = "Log in - Color Oasis";
    Layout = "_CleanLayout"; // This page won't use the layout
}
<main class="auth-minimal-wrapper">
    <div class="auth-minimal-inner">
        <div class="minimal-card-wrapper">
            <div class="card mb-4 mt-5 mx-4 mx-sm-0 position-relative">
                <div class="wd-50 bg-white p-2 rounded-circle shadow-lg position-absolute translate-middle top-0 start-50">
                    <img src="~/images/logo-abbr.png" alt="" class="img-fluid">
                </div>
                <div class="card-body p-sm-5">
                    <h2 class="fs-20 fw-bolder mb-4">@SharedLocalizer["Identity.Account.Login"]</h2>
                    <h4 class="fs-13 fw-bold mb-2">@SharedLocalizer["Identity.Account.LoginToAccount"]</h4>
                    <p class="fs-12 fw-medium text-muted">@SharedLocalizer["Identity.Account.WelcomeBack"]</p>
                    <form id="account" method="post" class="w-100 mt-4 pt-2">
                        <div asp-validation-summary="ModelOnly" class="text-danger" role="alert"></div>

                        <div class="mb-4">
                            <input asp-for="Input.Email" autocomplete="username" aria-required="true" class="form-control" placeholder="@SharedLocalizer["Identity.Account.EmailOrUsername"]" required>
                        </div>
                        <div class="mb-3">
                            <input asp-for="Input.Password" class="form-control" autocomplete="current-password" aria-required="true" placeholder="@SharedLocalizer["Identity.Manage.Password"]" required>
                        </div>
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <div class="custom-control custom-checkbox">
                                    <input asp-for="Input.RememberMe" type="checkbox" class="custom-control-input" id="rememberMe">
                                    <label class="custom-control-label c-pointer" for="rememberMe">@SharedLocalizer["Identity.Account.RememberMe"]</label>
                                </div>
                            </div>
                            <div>
                                <a id="forgot-password" asp-page="./ForgotPassword" class="fs-11 text-primary">@SharedLocalizer["Identity.Account.ForgotPassword"]</a>
                            </div>
                        </div>
                        <div class="mt-5">
                            <button type="submit" class="btn btn-lg btn-primary w-100">@SharedLocalizer["Identity.Account.Login"]</button>
                        </div>
                    </form>
                    <div class="w-100 mt-5 text-center mx-auto">
                        <div class="mb-4 border-bottom position-relative"><span class="small py-1 px-3 text-uppercase text-muted bg-white position-absolute translate-middle">@SharedLocalizer["Common.UI.Or"]</span></div>
                        <div class="d-flex align-items-center justify-content-center gap-2">
                            <a href="javascript:void(0);" class="btn btn-light-brand flex-fill" data-bs-toggle="tooltip" data-bs-trigger="hover" title="Login with Facebook">
                                <i class="feather-facebook"></i>
                            </a>
                            <a href="javascript:void(0);" class="btn btn-light-brand flex-fill" data-bs-toggle="tooltip" data-bs-trigger="hover" title="Login with Twitter">
                                <i class="feather-twitter"></i>
                            </a>
                            <a href="javascript:void(0);" class="btn btn-light-brand flex-fill" data-bs-toggle="tooltip" data-bs-trigger="hover" title="Login with Github">
                                <i class="feather-github text"></i>
                            </a>
                        </div>
                    </div>
                    <div class="mt-5 text-muted">
                        <span> @SharedLocalizer["Identity.Account.DontHaveAccount"]</span>
                        <a asp-page="./Register" asp-route-returnUrl="@Model.ReturnUrl" class="fw-bold">@SharedLocalizer["Identity.Account.CreateAnAccount"]</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

@* <div class="row">
    <div class="col-md-4">
        <section>
            <form id="account" method="post">
                <h2>Use a local account to log in.</h2>
                <hr />
                <div asp-validation-summary="ModelOnly" class="text-danger" role="alert"></div>
                <div class="form-floating mb-3">
                    <input asp-for="Input.Email"  autocomplete="username" aria-required="true" placeholder="<EMAIL>"class="form-control" />
                    <label asp-for="Input.Email" class="form-label">Email</label>
                    <span asp-validation-for="Input.Email" class="text-danger"></span>
                </div>
                <div class="form-floating mb-3">
                    <input asp-for="Input.Password" class="form-control" autocomplete="current-password" aria-required="true" placeholder="password" />
                    <label asp-for="Input.Password" class="form-label">Password</label>
                    <span asp-validation-for="Input.Password" class="text-danger"></span>
                </div>
                <div class="checkbox mb-3">
                    <label asp-for="Input.RememberMe" class="form-label">
                        <input class="form-check-input" asp-for="Input.RememberMe" />
                        @Html.DisplayNameFor(m => m.Input.RememberMe)
                    </label>
                </div>
                <div>
                    <button id="login-submit" type="submit" class="w-100 btn btn-lg btn-primary">Log in</button>
                </div>
                <div>
                    <p>
                        <a id="forgot-password" asp-page="./ForgotPassword">Forgot your password?</a>
                    </p>
                    <p>
                        <a asp-page="./Register" asp-route-returnUrl="@Model.ReturnUrl">Register as a new user</a>
                    </p>
                    <p>
                        <a id="resend-confirmation" asp-page="./ResendEmailConfirmation">Resend email confirmation</a>
                    </p>
                </div>
            </form>
        </section>
    </div>
    <div class="col-md-6 col-md-offset-2">
        <section>
            <h3>Use another service to log in.</h3>
            <hr />
            @{
                if ((Model.ExternalLogins?.Count ?? 0) == 0)
                {
                    <div>
                        <p>
                            There are no external authentication services configured. See this <a href="https://go.microsoft.com/fwlink/?LinkID=532715">article
                            about setting up this ASP.NET application to support logging in via external services</a>.
                        </p>
                    </div>
                }
                else
                {
                    <form id="external-account" asp-page="./ExternalLogin" asp-route-returnUrl="@Model.ReturnUrl" method="post" class="form-horizontal">
                        <div>
                            <p>
                                @foreach (var provider in Model.ExternalLogins!)
                                {
                                    <button type="submit" class="btn btn-primary" name="provider" value="@provider.Name" title="Log in using your @provider.DisplayName account">@provider.DisplayName</button>
                                }
                            </p>
                        </div>
                    </form>
                }
            }
        </section>
    </div>
</div>
 *@
@* @section Scripts {
    <partial name="_ValidationScriptsPartial" />

} *@

