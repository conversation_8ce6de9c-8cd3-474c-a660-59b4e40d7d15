@model IEnumerable<ColorOasisSystemWeb.Models.Sales.Payment>

@{
    ViewData["Title"] = "Payments";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="nxl-content">
    <!-- [ page-header ] start -->
    <div class="page-header">
        <div class="page-header-left d-flex align-items-center">
            <div class="page-header-title">
                <h5 class="m-b-10">Payments</h5>
            </div>
            <ul class="breadcrumb">
                <li class="breadcrumb-item"><a href="@Url.Action("Index", "Home")">Home</a></li>
                <li class="breadcrumb-item">ERP</li>
                <li class="breadcrumb-item">Accounting</li>
                <li class="breadcrumb-item">Payments</li>
            </ul>
        </div>
        <div class="page-header-right ms-auto">
            <div class="page-header-right-items">
                <div class="d-flex d-md-none">
                    <a href="javascript:void(0)" class="page-header-right-close-toggle">
                        <i class="feather-arrow-left me-2"></i>
                        <span>Back</span>
                    </a>
                </div>
                <div class="d-flex align-items-center gap-2 page-header-right-items-wrapper">
                    <a href="@Url.Action("Create")" class="btn btn-primary">
                        <i class="feather-plus me-2"></i>
                        <span>Record Payment</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
    <!-- [ page-header ] end -->

    <!-- [ Main Content ] start -->
    <div class="main-content">
        <div class="row">
            <div class="col-lg-12">
                <div class="card stretch stretch-full">
                    <div class="card-header">
                        <h5 class="card-title">Payments</h5>
                        <div class="card-header-action">
                            <div class="card-header-btn">
                                <div data-bs-toggle="tooltip" title="Refresh">
                                    <a href="javascript:void(0);" class="avatar-text avatar-md" onclick="location.reload();">
                                        <i class="feather-refresh-cw"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body custom-card-action p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0" id="paymentsTable">
                                <thead>
                                    <tr>
                                        <th>Payment Number</th>
                                        <th>Invoice</th>
                                        <th>Customer</th>
                                        <th>Payment Date</th>
                                        <th>Amount</th>
                                        <th>Method</th>
                                        <th>Status</th>
                                        <th class="text-end">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var payment in Model)
                                    {
                                        <tr>
                                            <td>
                                                <div class="fw-bold text-dark">@payment.PaymentNumber</div>
                                                @if (!string.IsNullOrEmpty(payment.Reference))
                                                {
                                                    <div class="fs-12 text-muted">Ref: @payment.Reference</div>
                                                }
                                            </td>
                                            <td>
                                                @if (payment.Invoice != null)
                                                {
                                                    <div class="fw-bold text-dark">@payment.Invoice.InvoiceNumber</div>
                                                    <div class="fs-12 text-muted">@payment.Invoice.InvoiceDate.ToString("MMM dd, yyyy")</div>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">No Invoice</span>
                                                }
                                            </td>
                                            <td>
                                                @if (payment.Invoice?.Client != null)
                                                {
                                                    <div class="fw-bold text-dark">@payment.Invoice.Client.Name</div>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">-</span>
                                                }
                                            </td>
                                            <td>
                                                <div class="fw-bold">@payment.PaymentDate.ToString("MMM dd, yyyy")</div>
                                                <div class="fs-12 text-muted">@payment.PaymentDate.ToString("HH:mm")</div>
                                            </td>
                                            <td>
                                                <div class="fw-bold text-success">@payment.Amount.ToString("C")</div>
                                                @if (payment.RefundAmount > 0)
                                                {
                                                    <div class="fs-12 text-danger">-@payment.RefundAmount.ToString("C") refunded</div>
                                                }
                                            </td>
                                            <td>
                                                @{
                                                    var methodClass = "";
                                                    var methodIcon = "";
                                                    switch (payment.PaymentMethod)
                                                    {
                                                        case ColorOasisSystemWeb.Models.Sales.PaymentMethod.Cash:
                                                            methodClass = "bg-soft-success text-success";
                                                            methodIcon = "feather-dollar-sign";
                                                            break;
                                                        case ColorOasisSystemWeb.Models.Sales.PaymentMethod.CreditCard:
                                                            methodClass = "bg-soft-primary text-primary";
                                                            methodIcon = "feather-credit-card";
                                                            break;
                                                        case ColorOasisSystemWeb.Models.Sales.PaymentMethod.BankTransfer:
                                                            methodClass = "bg-soft-info text-info";
                                                            methodIcon = "feather-send";
                                                            break;
                                                        case ColorOasisSystemWeb.Models.Sales.PaymentMethod.Check:
                                                            methodClass = "bg-soft-warning text-warning";
                                                            methodIcon = "feather-file-text";
                                                            break;
                                                        case ColorOasisSystemWeb.Models.Sales.PaymentMethod.Other:
                                                            methodClass = "bg-soft-secondary text-secondary";
                                                            methodIcon = "feather-more-horizontal";
                                                            break;
                                                    }
                                                }
                                                <span class="badge @methodClass">
                                                    <i class="@methodIcon me-1"></i>@payment.PaymentMethod
                                                </span>
                                            </td>
                                            <td>
                                                @switch (payment.Status)
                                                {
                                                    case ColorOasisSystemWeb.Models.Sales.PaymentStatus.Pending:
                                                        <span class="badge bg-soft-warning text-warning">Pending</span>
                                                        break;
                                                    case ColorOasisSystemWeb.Models.Sales.PaymentStatus.Completed:
                                                        <span class="badge bg-soft-success text-success">Completed</span>
                                                        break;
                                                    case ColorOasisSystemWeb.Models.Sales.PaymentStatus.Failed:
                                                        <span class="badge bg-soft-danger text-danger">Failed</span>
                                                        break;
                                                    case ColorOasisSystemWeb.Models.Sales.PaymentStatus.Cancelled:
                                                        <span class="badge bg-soft-secondary text-secondary">Cancelled</span>
                                                        break;
                                                    case ColorOasisSystemWeb.Models.Sales.PaymentStatus.Refunded:
                                                        <span class="badge bg-soft-info text-info">Refunded</span>
                                                        break;
                                                }
                                            </td>
                                            <td>
                                                <div class="hstack gap-2 justify-content-end">
                                                    <a href="@Url.Action("Details", new { id = payment.Id })" class="avatar-text avatar-md" data-bs-toggle="tooltip" title="View Details">
                                                        <i class="feather-eye"></i>
                                                    </a>
                                                    @if (payment.Status == ColorOasisSystemWeb.Models.Sales.PaymentStatus.Completed && payment.RefundAmount == 0)
                                                    {
                                                        <a href="@Url.Action("Refund", new { id = payment.Id })" class="avatar-text avatar-md" data-bs-toggle="tooltip" title="Process Refund">
                                                            <i class="feather-corner-up-left"></i>
                                                        </a>
                                                    }
                                                    <a href="@Url.Action("Receipt", new { id = payment.Id })" class="avatar-text avatar-md" data-bs-toggle="tooltip" title="Print Receipt" target="_blank">
                                                        <i class="feather-printer"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- [ Main Content ] end -->
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            $('#paymentsTable').DataTable({
                responsive: true,
                pageLength: 25,
                order: [[0, 'desc']],
                columnDefs: [
                    { orderable: false, targets: [7] }
                ]
            });
        });
    </script>
}
