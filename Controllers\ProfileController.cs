using ColorOasisSystemWeb.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;
using ColorOasisSystemWeb.Data;
using ColorOasisSystemWeb.Helpers;

namespace ColorOasisSystemWeb.Controllers
{
    public class ProfileController : Controller
    {
        private readonly UserManager<User> _userManager;
        private readonly AppDbContext _context;

        public ProfileController(UserManager<User> userManager, AppDbContext context)
        {
            _userManager = userManager;
            _context = context;
        }

        // GET: /Profile
        [Authorize]
        public async Task<IActionResult> Index()
        {
            // Redirect to the current user's profile
            var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            return RedirectToAction(nameof(Details), new { id = userId });
        }

        // GET: /Profile/Details/5
        [Route("Profile/Details/{id}")]
        public async Task<IActionResult> Details(string id)
        {
            if (string.IsNullOrEmpty(id))
            {
                return NotFound();
            }

            // Try to find user by ID first
            var user = await _userManager.Users
                .Include(u => u.ProfileVisibility)
                .Include(u => u.ProfileDetails)
                .Include(u => u.SocialMedia)
                .FirstOrDefaultAsync(u => u.Id == id);

            // If not found by ID, try to find by username
            if (user == null)
            {
                user = await _userManager.Users
                    .Include(u => u.ProfileVisibility)
                    .Include(u => u.ProfileDetails)
                    .Include(u => u.SocialMedia)
                    .FirstOrDefaultAsync(u => u.UserName == id);
                
                if (user == null)
                {
                    return NotFound();
                }
            }

            // Check if the profile is public or if the current user is the profile owner or an admin
            var currentUserId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            var isAdmin = User.IsInRole("Admin");
            var isOwner = currentUserId == id;
            var isPublic = user.ProfileVisibility?.IsPublic ?? false;

            if (!isPublic && !isOwner && !isAdmin)
            {
                return View("PrivateProfile");
            }

            // Determine which view to show based on the user's role
            if (isOwner || isAdmin)
            {
                // Show the private/full profile view to the owner and admins
                return View("PrivateDetails", user);
            }
            else
            {
                // Show the public profile view to others
                return View("PublicDetails", user);
            }
        }

        // GET: /Profile/Edit
        [Authorize]
        [Route("ProfileEdit/{id}")]
        public async Task<IActionResult> Edit(string id = null)
        {
            // If no ID is provided, default to the current user
            var userId = id ?? User.FindFirstValue(ClaimTypes.NameIdentifier);
            
            // Only allow editing if the user is the profile owner or an admin
            var currentUserId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            var isAdmin = User.IsInRole("Admin");
            
            if (userId != currentUserId && !isAdmin)
            {
                return Forbid();
            }
            
            var user = await _userManager.Users
                .Include(u => u.ProfileVisibility)
                .Include(u => u.ProfileDetails)
                .Include(u => u.SocialMedia)
                .FirstOrDefaultAsync(u => u.Id == userId);

            if (user == null)
            {
                return NotFound();
            }

            // Create profile visibility if it doesn't exist
            if (user.ProfileVisibility == null)
            {
                user.ProfileVisibility = new UserProfileVisibility
                {
                    UserId = userId,
                    IsPublic = false,
                    LastModifiedBy = userId
                };
                _context.Add(user.ProfileVisibility);
                await _context.SaveChangesAsync();
            }
            
            // Create profile details if it doesn't exist
            if (user.ProfileDetails == null)
            {
                user.ProfileDetails = new UserProfileDetails
                {
                    UserId = userId,
                    LastUpdated = DateTime.UtcNow
                };
                _context.Add(user.ProfileDetails);
                await _context.SaveChangesAsync();
            }
            
            // Create social media links if they don't exist
            if (user.SocialMedia == null)
            {
                user.SocialMedia = new UserSocialMedia
                {
                    UserId = userId,
                    LastModifiedBy = userId,
                    LastModifiedDate = DateTime.UtcNow
                };
                _context.Add(user.SocialMedia);
                await _context.SaveChangesAsync();
            }

            return View(user);
        }

        // POST: /Profile/Edit
        [HttpPost]
        [ValidateAntiForgeryToken]
        [Authorize]
        [Route("ProfileEdit/{id}")]
        public async Task<IActionResult> Edit(string id, User model, IFormFile ProfilePicture, [Bind(Prefix = "visibility")] UserProfileVisibility visibility, [Bind(Prefix = "details")] UserProfileDetails details, [Bind(Prefix = "social")] UserSocialMedia social)
        {
            // If no ID is provided, default to the current user
            var userId = id ?? User.FindFirstValue(ClaimTypes.NameIdentifier);
            
            // Only allow editing if the user is the profile owner or an admin
            var currentUserId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            var isAdmin = User.IsInRole("Admin");
            
            if (userId != currentUserId && !isAdmin)
            {
                return Forbid();
            }
            
            var user = await _userManager.Users
                .Include(u => u.ProfileVisibility)
                .Include(u => u.ProfileDetails)
                .Include(u => u.SocialMedia)
                .FirstOrDefaultAsync(u => u.Id == userId);

            if (user == null)
            {
                return NotFound();
            }
            
            // Skip ModelState validation completely since we're only updating specific fields
            // and don't want validation errors for fields that weren't changed
            
            // Instead, manually validate only the essential fields
            bool isValid = true;
            
            if (string.IsNullOrWhiteSpace(model.FirstName))
            {
                ModelState.AddModelError(nameof(model.FirstName), "First Name is required");
                isValid = false;
            }
            
            if (string.IsNullOrWhiteSpace(model.LastName))
            {
                ModelState.AddModelError(nameof(model.LastName), "Last Name is required");
                isValid = false;
            }
            
            // We don't need to validate Gender and DateOfBirth here as they will be preserved from the existing user
            // if not explicitly changed in the form
            
            if (isValid)
            {
                try
                {
                    // Update user profile fields
                    user.FirstName = model.FirstName;
                    user.LastName = model.LastName;
                    user.PhoneNumber = model.PhoneNumber;
                    user.Gender = model.Gender;
                    user.DateOfBirth = model.DateOfBirth;
                    
                    // Update profile picture if provided
                    if (ProfilePicture != null && ProfilePicture.Length > 0)
                    {
                        using (var memoryStream = new MemoryStream())
                        {
                            await ProfilePicture.CopyToAsync(memoryStream);
                            user.ProfilePicture = memoryStream.ToArray();
                        }
                    }

                    // Update or create profile visibility settings
                    if (user.ProfileVisibility == null)
                    {
                        user.ProfileVisibility = new UserProfileVisibility
                        {
                            UserId = userId,
                            IsPublic = visibility.IsPublic,
                            ShowEmail = visibility.ShowEmail,
                            ShowPhoneNumber = visibility.ShowPhoneNumber,
                            ShowDateOfBirth = visibility.ShowDateOfBirth,
                            ShowGender = visibility.ShowGender,
                            ShowBio = visibility.ShowBio,
                            ShowJobTitle = visibility.ShowJobTitle,
                            ShowCompany = visibility.ShowCompany,
                            ShowWebsite = visibility.ShowWebsite,
                            // Social media visibility settings
                            ShowFacebook = visibility.ShowFacebook,
                            ShowTwitter = visibility.ShowTwitter,
                            ShowInstagram = visibility.ShowInstagram,
                            ShowLinkedIn = visibility.ShowLinkedIn,
                            ShowGitHub = visibility.ShowGitHub,
                            ShowYouTube = visibility.ShowYouTube,
                            ShowSnapchat = visibility.ShowSnapchat,
                            ShowTikTok = visibility.ShowTikTok,
                            LastModifiedBy = userId,
                            LastModifiedDate = DateTime.UtcNow
                        };
                        _context.Add(user.ProfileVisibility);
                    }
                    else
                    {
                        user.ProfileVisibility.IsPublic = visibility.IsPublic;
                        user.ProfileVisibility.ShowEmail = visibility.ShowEmail;
                        user.ProfileVisibility.ShowPhoneNumber = visibility.ShowPhoneNumber;
                        user.ProfileVisibility.ShowDateOfBirth = visibility.ShowDateOfBirth;
                        user.ProfileVisibility.ShowGender = visibility.ShowGender;
                        user.ProfileVisibility.ShowBio = visibility.ShowBio;
                        user.ProfileVisibility.ShowJobTitle = visibility.ShowJobTitle;
                        user.ProfileVisibility.ShowCompany = visibility.ShowCompany;
                        user.ProfileVisibility.ShowWebsite = visibility.ShowWebsite;
                        // Update social media visibility settings
                        user.ProfileVisibility.ShowFacebook = visibility.ShowFacebook;
                        user.ProfileVisibility.ShowTwitter = visibility.ShowTwitter;
                        user.ProfileVisibility.ShowInstagram = visibility.ShowInstagram;
                        user.ProfileVisibility.ShowLinkedIn = visibility.ShowLinkedIn;
                        user.ProfileVisibility.ShowGitHub = visibility.ShowGitHub;
                        user.ProfileVisibility.ShowYouTube = visibility.ShowYouTube;
                        user.ProfileVisibility.ShowSnapchat = visibility.ShowSnapchat;
                        user.ProfileVisibility.ShowTikTok = visibility.ShowTikTok;
                        user.ProfileVisibility.LastModifiedBy = userId;
                        user.ProfileVisibility.LastModifiedDate = DateTime.UtcNow;
                    }
                    
                    // Update or create profile details
                    if (user.ProfileDetails == null)
                    {
                        user.ProfileDetails = new UserProfileDetails
                        {
                            UserId = userId,
                            Bio = details.Bio,
                            JobTitle = details.JobTitle,
                            Company = details.Company,
                            Website = details.Website,
                            Address = details.Address,
                            City = details.City,
                            Country = details.Country,
                            PostalCode = details.PostalCode,
                            EmergencyContact = details.EmergencyContact,
                            EmergencyContactPhone = details.EmergencyContactPhone,
                            LastUpdated = DateTime.UtcNow
                        };
                        _context.Add(user.ProfileDetails);
                    }
                    else
                    {
                        user.ProfileDetails.Bio = details.Bio;
                        user.ProfileDetails.JobTitle = details.JobTitle;
                        user.ProfileDetails.Company = details.Company;
                        user.ProfileDetails.Website = details.Website;
                        user.ProfileDetails.Address = details.Address;
                        user.ProfileDetails.City = details.City;
                        user.ProfileDetails.Country = details.Country;
                        user.ProfileDetails.PostalCode = details.PostalCode;
                        user.ProfileDetails.EmergencyContact = details.EmergencyContact;
                        user.ProfileDetails.EmergencyContactPhone = details.EmergencyContactPhone;
                        user.ProfileDetails.LastUpdated = DateTime.UtcNow;
                    }

                    // First update the user entity
                    await _userManager.UpdateAsync(user);
                    
                    // Then explicitly update the related entities
                    if (user.ProfileVisibility != null)
                    {
                        // Ensure the visibility entity is being tracked and marked as modified
                        var visibilityEntry = _context.Entry(user.ProfileVisibility);
                        
                        // Force the state to be modified to ensure all properties are updated
                        visibilityEntry.State = EntityState.Modified;
                        
                        // Explicitly mark each property as modified to ensure changes are tracked
                        visibilityEntry.Property(v => v.IsPublic).IsModified = true;
                        visibilityEntry.Property(v => v.ShowEmail).IsModified = true;
                        visibilityEntry.Property(v => v.ShowPhoneNumber).IsModified = true;
                        visibilityEntry.Property(v => v.ShowDateOfBirth).IsModified = true;
                        visibilityEntry.Property(v => v.ShowGender).IsModified = true;
                        visibilityEntry.Property(v => v.ShowBio).IsModified = true;
                        visibilityEntry.Property(v => v.ShowJobTitle).IsModified = true;
                        visibilityEntry.Property(v => v.ShowCompany).IsModified = true;
                        visibilityEntry.Property(v => v.ShowWebsite).IsModified = true;
                        visibilityEntry.Property(v => v.LastModifiedDate).IsModified = true;
                        visibilityEntry.Property(v => v.LastModifiedBy).IsModified = true;
                    }
                    
                    if (user.ProfileDetails != null)
                    {
                        // Ensure the details entity is being tracked and marked as modified
                        var detailsEntry = _context.Entry(user.ProfileDetails);
                        detailsEntry.State = EntityState.Modified;
                    }
                    
                    // Update or create social media links
                    if (user.SocialMedia == null)
                    {
                        user.SocialMedia = new UserSocialMedia
                        {
                            UserId = userId,
                            FacebookUrl = social.FacebookUrl,
                            TwitterUrl = social.TwitterUrl,
                            InstagramUrl = social.InstagramUrl,
                            LinkedInUrl = social.LinkedInUrl,
                            GitHubUrl = social.GitHubUrl,
                            YouTubeUrl = social.YouTubeUrl,
                            SnapchatUrl = social.SnapchatUrl,
                            TikTokUrl = social.TikTokUrl,
                            LastModifiedBy = userId,
                            LastModifiedDate = DateTime.UtcNow
                        };
                        _context.Add(user.SocialMedia);
                    }
                    else
                    {
                        user.SocialMedia.FacebookUrl = social.FacebookUrl;
                        user.SocialMedia.TwitterUrl = social.TwitterUrl;
                        user.SocialMedia.InstagramUrl = social.InstagramUrl;
                        user.SocialMedia.LinkedInUrl = social.LinkedInUrl;
                        user.SocialMedia.GitHubUrl = social.GitHubUrl;
                        user.SocialMedia.YouTubeUrl = social.YouTubeUrl;
                        user.SocialMedia.SnapchatUrl = social.SnapchatUrl;
                        user.SocialMedia.TikTokUrl = social.TikTokUrl;
                        user.SocialMedia.LastModifiedBy = userId;
                        user.SocialMedia.LastModifiedDate = DateTime.UtcNow;
                        
                        // Ensure the social media entity is being tracked and marked as modified
                        var socialMediaEntry = _context.Entry(user.SocialMedia);
                        socialMediaEntry.State = EntityState.Modified;
                    }
                    
                    // Save all changes
                    await _context.SaveChangesAsync();
                    
                    TempData["StatusMessage"] = "Your profile has been updated successfully.";
                    return RedirectToAction(nameof(Details), new { id = userId });
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!await UserExists(user.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
            }
            
            return View(user);
        }

        private async Task<bool> UserExists(string id)
        {
            return await _userManager.Users.AnyAsync(e => e.Id == id || e.UserName == id);
        }
        
        // GET: /Profile/Username/{username}
        [Route("Profile/{username}")]
        public async Task<IActionResult> ByUsername(string username)
        {
            if (string.IsNullOrEmpty(username))
            {
                return NotFound();
            }

            var user = await _userManager.Users
                .Include(u => u.ProfileVisibility)
                .Include(u => u.ProfileDetails)
                .Include(u => u.SocialMedia)
                .FirstOrDefaultAsync(u => u.UserName == username);

            if (user == null)
            {
                return NotFound();
            }

            // Check if the profile is public or if the current user is the profile owner or an admin
            var currentUserId = User.FindFirstValue(ClaimTypes.NameIdentifier);
            var isAdmin = User.IsInRole("Admin");
            var isOwner = currentUserId == user.Id;
            var isPublic = user.ProfileVisibility?.IsPublic ?? false;

            if (!isPublic && !isOwner && !isAdmin)
            {
                return View("PrivateProfile");
            }

            // Determine which view to show based on the user's role
            if (isOwner || isAdmin)
            {
                // Show the private/full profile view to the owner and admins
                return View("PrivateDetails", user);
            }
            else
            {
                // Show the public profile view to others
                return View("PublicDetails", user);
            }
        }
    }
}