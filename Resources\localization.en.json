{"Navigation": {"Main": "Navigation", "Dashboards": "Dashboards", "CRM": "CRM", "Analytics": "Analytics", "Home": "Home", "InfoCenter": "Info Center", "Search": "Search...", "AdminSection": "Administration"}, "Common": {"Messages": {"Welcome": "Welcome", "Error": "Error", "ErrorOccurred": "An error has occurred", "AreYouSureDelete": "Are you sure you want to delete this?", "ConfirmDeletion": "Confirm Deletion", "DeleteConfirmation": "Are you sure you want to delete this row?"}, "UI": {"ThemeSettings": "Theme Settings", "Light": "Light", "Dark": "Dark", "Header": "Header", "Skins": "Skins", "Typography": "Typography"}, "Actions": {"Create": "Create", "CreateNew": "Create New", "Edit": "Edit", "Delete": "Delete", "Save": "Save", "SaveChanges": "Save Changes", "Back": "Back", "Cancel": "Cancel", "Details": "Details", "Index": "Index", "AddRow": "Add Row", "AddNewRow": "Add New Row", "AddItems": "Add Items", "Action": "Action", "Convert": "Convert"}, "Fields": {"Name": "Name", "NameEn": "English Name", "NameInEnglish": "Name In English", "Email": "Email", "Phone": "Phone", "Address": "Address", "TRN": "TRN", "POBox": "POBox", "Price": "Price", "MinimumPrice": "Minimum Price", "MaximumPrice": "Maximum Price", "Discount": "Discount", "DiscountValue": "Discount (value)", "TotalDiscount": "Total Discount", "TotalPrice": "Total Price", "GrandTotal": "Grand Total", "SubTotal": "Sub Total", "AfterDiscount": "After Discount", "Tax": "Tax", "TaxAmount": "Tax Amount", "Qty": "Qty", "TotalQuantity": "Total Quantity", "GrandTotalQuantity": "Grand total Quantity", "Barcode": "Barcode", "Product": "Product", "ValidTo": "<PERSON><PERSON>"}, "Auth": {"Login": "<PERSON><PERSON>", "Logout": "Logout", "Register": "Register"}}, "Company": {"Title": "Company", "Companies": "Companies", "CompanyCreate": "Create Company", "CompanyProduct": "Color Oasis System - Your Complete Business Solution", "MailUs": "Mail Us", "CompanyName": "Company Name", "CompanyCode": "Company Code", "CompanyCodeExample": "Company Code Example", "CompanyNameExample": "Your company name [Ex: واحه الالوان]", "CompanyNameEnExample": "Your company name in English [Ex: Color Oasis]", "CompanyPhoneExample": "Phone [Ex: +************ 654]", "CompanyAddress": "Company Address", "CompanyAddressExample": "Your company address [Ex: <PERSON> st]", "CompanyTRN": "Company TRN", "CompanyTRNExample": "TRN [Ex: 1234567890]", "Dealer": "Dealer"}, "Services": {"Title": "Services", "ServiceCreate": "Create Service", "ServiceCategories": "Service Categories", "ServiceCategoryCreate": "Create Service Category", "ServiceTypes": "Service Types", "ServiceTypeCreate": "Create Service Type", "ServiceType": "Service Type", "ServiceCode": "Service Code", "ServiceCategory": "Service Category", "SelectService": "Select a service"}, "Clients": {"Title": "Clients", "ClientCreate": "Create Client", "Client": "Client", "ClientType": "Client Type", "SelectClient": "Select a client", "Customers": "Customers", "CreateCustomer": "Create Customer", "EditCustomer": "Edit Customer", "CustomerCode": "Customer Code", "BackToList": "Back to List"}, "Inspection": {"Title": "Inspection", "Inspections": "Inspections", "InspectionCreate": "Create Inspection", "InspectionEdit": "Inspection Edit", "InspectionCode": "Inspection Code", "InspectionTo": "Inspection To", "PrepareInspection": "Prepare the inspection for the client|Company", "SaveInspection": "Save Inspection", "TypeOfUnit": "Type of Unit", "UnitTypes": {"Villa": "Villa", "Flat": "Flat", "Palace": "Palace", "Other": "Other"}, "UnitCode": "Unit Code", "RoomNumber": "Room Number"}, "Quotation": {"Title": "Quotation", "Quotations": "Quotations", "QuotationCreate": "Quotation Create", "QuotationEdit": "Quotation Edit", "SaveQuotation": "Save Quotation", "InvoiceProduct": "Invoice Product", "InvoiceNote": "Invoice Note", "QuotePrefix": "Quote-", "Status": "Status", "User": "User", "Paid": "Paid", "Remain": "<PERSON><PERSON><PERSON>", "IsPaid": "<PERSON>", "IsConverted": "Is Converted"}, "Rooms": {"Room": "Room", "SelectRoom": "Select a room", "MasterBedroom": "Master Bedroom", "MasterBedroomBathroom": "Master Bedroom Bathroom", "SecondBedroom": "Second Bedroom", "SecondBedroomBathroom": "Second Bedroom Bathroom", "ThirdBedroom": "Third Bedroom", "ThirdBedroomBathroom": "Third Bedroom Bathroom", "FourthBedroom": "Fourth Bedroom", "FourthBedroomBathroom": "Fourth Bedroom Bathroom", "FifthBedroom": "Fifth Bedroom", "FifthBedroomBathroom": "Fifth Bedroom Bathroom", "SixthBedroom": "Sixth Bedroom", "SixthBedroomBathroom": "Sixth Bedroom Bathroom", "SeventhBedroom": "Seventh Bedroom", "SeventhBedroomBathroom": "Seventh Bedroom Bathroom", "EighthBedroom": "Eighth Bedroom", "EighthBedroomBathroom": "Eighth Bedroom Bathroom", "NinethBedroom": "Nineth Bedroom", "NinethBedroomBathroom": "Nineth Bedroom Bathroom", "TenthBedroom": "Tenth Bedroom", "TenthBedroomBathroom": "Tenth Bedroom Bathroom"}, "Settings": {"Title": "Settings", "General": "General", "SEO": "SEO", "Tags": "Tags", "Email": "Email", "Tasks": "Tasks", "Leads": "Leads", "Support": "Support", "Finance": "Finance", "Gateways": "Gateways", "Customers": "Customers", "Localization": "Localization", "Recaptcha": "reCAPTCHA", "Miscellaneous": "Miscellaneous"}, "Auth": {"Title": "Authentication", "Login": "<PERSON><PERSON>", "Register": "Register", "Logout": "Logout", "Error404": "Error-404", "ResetPass": "Reset Pass", "VerifyOTP": "Verify OTP", "Maintenance": "Maintenance", "Cover": "Cover", "Minimal": "Minimal", "Creative": "Creative"}, "HelpCenter": {"Title": "Help Center", "Support": "Support", "KnowledgeBase": "KnowledgeBase", "Documentation": "Documentation"}, "UI": {"Back": "Back", "Help": "Help", "Terms": "Terms", "Privacy": "Privacy", "MegaMenu": "Mega Menu", "AddNewItems": "Add New Items"}, "Menu": {"Proposal": "Proposal", "ProposalView": "Proposal View", "ProposalEdit": "Proposal Edit", "ProposalCreate": "Proposal Create", "Payment": "Payment", "InvoiceView": "Invoice View", "InvoiceCreate": "Invoice Create", "Leads": "Leads", "LeadsView": "Leads View", "LeadsCreate": "Leads Create", "Projects": "Projects", "ProjectsView": "Projects View", "ProjectsCreate": "Projects Create", "Applications": "Applications", "Chat": "Cha<PERSON>", "Email": "Email", "Tasks": "Tasks", "Notes": "Notes", "Storage": "Storage", "Calendar": "Calendar", "Reports": "Reports", "SalesReport": "Sales Report", "LeadsReport": "Leads Report", "ProjectReport": "Project Report", "TimesheetsReport": "Timesheets Report"}, "Validation": {"NumericOnly": "Please enter a valid number or fraction.", "Validation": {"InspectionServices": {"Required": "At least one service must be included!"}, "Inspection": {"CodeRequired": "You must add a code for the inspection!", "RoomsNoRequired": "You must enter room number for the inspection!", "UnitCodeRequired": "You must enter unit code for the inspection!", "POBoxRequired": "You must enter POBox for the inspection!", "ClientRequired": "You must select the client/company for the inspection!"}}}, "Language": {"SelectLanguage": "Select Language", "LanguagesAvailable": "languages available!", "English": "English", "Arabic": "Arabic"}, "User": {"Gender": "Gender Type"}, "SignalR": {"Management": {"Title": "SignalR Connection Management", "Dashboard": "SignalR Connection Management Dashboard", "Description": "This dashboard allows you to monitor and manage real-time SignalR connections, send targeted notifications, and configure notification preferences for users.", "Tabs": {"ActiveConnections": "Active Connections", "TargetedNotifications": "Targeted Notifications", "Broadcast": "Broadcast", "ConnectionAnalytics": "Connection Analytics", "ConnectionGroups": "Connection Groups"}, "ConnectionManagement": {"Title": "Connection Management", "TotalUsers": "Total Users", "ConnectedUsers": "Connected Users", "DisconnectedUsers": "Disconnected Users", "ConnectionDetails": "Connection Details", "UserInformation": "User Information", "Name": "Name", "ConnectionCount": "Connection Count", "LastActive": "Last Active", "ConnectionHistory": "Connection History", "ActiveSessions": "Active Sessions", "LoadingSessionData": "Loading session data...", "Close": "Close"}, "TargetedNotifications": {"Title": "Send Targeted Notifications", "SelectRecipients": "Select Recipients", "ModelType": "Model Type", "ProcessType": "Process Type", "ProcessID": "Process ID", "NotificationOptions": "Notification Options", "StoreForOfflineUsers": "Store notifications for offline users", "NotificationMessage": "Notification Message", "Link": "Link (Optional)", "SendNotification": "Send Notification"}, "Broadcast": {"Title": "Broadcast Message to All Connected Users", "Message": "Message", "Link": "Link (Optional)", "BroadcastButton": "Broadcast to All Connected Users"}, "Analytics": {"Title": "Connection Analytics", "ConnectionRate": "Connection Rate", "ActiveSessions": "Active Sessions", "AvgSessionsPerUser": "Avg. Sessions Per User", "MostActiveUsers": "Most Active Users", "User": "User", "ConnectionCount": "Connection Count", "LastActive": "Last Active"}, "ConnectionGroups": {"Title": "Connection Groups", "CreateGroup": "Create Connection Group", "GroupName": "Group Name", "Description": "Description", "SelectGroupMembers": "Select Group Members", "Cancel": "Cancel", "Create": "Create Group", "EditGroup": "Edit Connection Group", "Update": "Update Group", "SendMessageToGroup": "Send Message to Group", "Group": "Group", "Message": "Message", "Link": "Link (Optional)", "Send": "Send"}}, "DirectMessage": {"Title": "Send Direct Message", "Recipient": "Recipient", "Message": "Message", "Link": "Link (Optional)", "Cancel": "Cancel", "Send": "Send Message"}}, "Identity": {"Account": {"Login": "<PERSON><PERSON>", "LoginToAccount": "Login to your account", "Register": "Register", "CreateAccount": "Create a new account", "ManageAccount": "Manage your account", "ChangeSettings": "Change your account settings", "RememberMe": "Remember Me", "ForgotPassword": "Forgot password?", "ForgotPasswordConfirmation": "Forgot password confirmation", "CheckEmailReset": "Please check your email to reset your password.", "DontHaveAccount": "Don't have an account?", "AlreadyHaveAccount": "Already have an account?", "CreateAnAccount": "Create an Account", "UseAnotherService": "Use another service to log in", "UseAnotherServiceRegister": "Use another service to register", "AssociateAccount": "Associate your account", "EnterEmail": "Enter your email", "ResetPassword": "Reset Password", "ResetYourPassword": "Reset your password", "ConfirmEmail": "Confirm Email", "EmailConfirmation": "Email Confirmation", "AccessDenied": "Access Denied", "NoAccessResource": "You do not have access to this resource.", "Lockout": "Locked out", "AccountLockedOut": "This account has been locked out, please try again later."}, "Manage": {"Profile": "Profile", "Email": "Email", "Password": "Password", "ExternalLogins": "External logins", "TwoFactorAuth": "Two-factor authentication", "PersonalData": "Personal data", "RegisteredLogins": "Registered Logins", "AddService": "Add another service to log in", "SetPassword": "Set your password", "NoLocalAccount": "You do not have a local username/password for this site", "AddLocalAccount": "Add a local account so you can log in without an external login", "AuthenticatorApp": "Authenticator app", "UpdatePassword": "Update password", "ChangePassword": "Change password", "DownloadYourData": "Download Your Data", "Download": "Download", "Delete": "Delete", "DeletePersonalData": "Delete Personal Data", "DeleteDataWarning": "Deleting this data will permanently remove your account, and this cannot be recovered.", "DeleteDataAndAccount": "Delete data and close my account", "PersonalDataDescription": "Your account contains personal data that you have given us. This page allows you to download or delete that data.", "ConfigureAuthenticatorApp": "Configure authenticator app", "ResetAuthenticatorKey": "Reset authenticator key", "RecoveryCodes": "Recovery codes", "PutCodesInSafePlace": "Put these codes in a safe place.", "LoseDeviceWarning": "If you lose your device and don't have the recovery codes you will lose access to your account.", "Disable2FA": "Disable two-factor authentication (2FA)", "Disable2FAWarning": "This action only disables 2FA.", "Disable2FAInfo": "Disabling 2FA does not change the keys used in authenticator apps. If you wish to change the key used in an authenticator app you should reset your authenticator keys.", "ManageEmail": "Manage Email", "SendVerificationEmail": "Send verification email", "VerificationCode": "Verification Code", "Verify": "Verify", "ResetPasswordConfirmation": "Reset password confirmation", "PasswordReset": "Your password has been reset.", "ClickToLogin": "Click here to log in", "LogOut": "Log out", "ClickToLogout": "Click here to Logout", "LoggedOutSuccess": "You have successfully logged out of the application.", "EnterAuthenticatorCode": "Enter the authenticator code", "RememberMachine": "Remember this machine", "NoAuthenticatorDevice": "Don't have access to your authenticator device?", "LoginWithRecoveryCode": "Login with a recovery code", "RecoveryCodeVerification": "Recovery code verification", "RecoveryCodeLoginInfo": "You have requested to log in with a recovery code. This login will not be remembered until you provide an authenticator app code at log in or disable 2FA and log in again.", "AuthenticatorAppSteps": "To use an authenticator app, follow these steps:", "DownloadAuthenticatorApp": "Download a two-factor authenticator app like Microsoft Authenticator for", "Android": "Android", "iOS": "iOS", "GoogleAuthenticatorFor": "Google Authenticator for", "ScanQRCode": "Scan the QR Code or enter this key", "IntoAuthenticatorApp": "into your two factor authenticator app. Spaces and casing do not matter.", "LearnQRCode": "Learn how to", "EnableQRGeneration": "enable QR code generation", "QRCodeScanned": "Once you have scanned the QR code or input the key above, your two factor authentication app will provide you with a unique code.", "EnterCodeBelow": "Enter the code in the confirmation box below.", "PleaseEnterCode": "Please enter the code."}, "Error": {"Error": "Error", "ErrorOccurred": "An error occurred while processing your request", "DevelopmentMode": "Development Mode", "DetailedInfo": "Swapping to Development environment will display more detailed information about the error that occurred", "NotForProduction": "Development environment should not be enabled in deployed applications"}}, "ERP": {"Title": "ERP Modules", "Inventory": {"Title": "Inventory Management", "Items": "Inventory Items", "AddItem": "Add Inventory Item", "Warehouses": "Warehouses", "AddWarehouse": "Add Warehouse", "StockAdjustments": "Stock Adjustments", "Transactions": "Inventory Transactions", "BinLocations": "Bin Locations", "StockLevels": "Stock Levels"}, "Purchasing": {"Title": "Purchasing", "Suppliers": "Suppliers", "AddSupplier": "Add Supplier", "Requisitions": "Purchase Requisitions", "CreateRequisition": "Create Requisition", "PurchaseOrders": "Purchase Orders", "CreatePurchaseOrder": "Create Purchase Order", "GoodsReceipts": "Goods Receipts", "SupplierEvaluation": "Supplier Evaluation"}, "Sales": {"Title": "Sales Management", "Leads": "Leads", "AddLead": "Add Lead", "Opportunities": "Opportunities", "AddOpportunity": "Add Opportunity", "SalesOrders": "Sales Orders", "CreateSalesOrder": "Create Sales Order", "Shipments": "Shipments", "LeadConversion": "Lead Conversion"}, "Accounting": {"Title": "Accounting", "Invoices": "Invoices", "CreateInvoice": "Create Invoice", "Payments": "Payments", "RecordPayment": "Record Payment", "Dashboard": "Accounting Dashboard", "Reports": "Financial Reports"}}, "Admin": {"UserAdministration": "User Administration", "UserManagement": "User Management", "RoleManagement": "Role Management", "PermissionsManagement": "Permissions Management", "ClaimsManagement": "Claims Management", "ProfileManagement": "Profile Management", "NotificationManagement": "Notification Management", "Notifications": "Notifications", "SystemManagement": "System Management", "Dashboard": "Admin Dashboard", "SignalRManagement": "SignalR Management"}, "Profile": {"Title": "Profile", "MyProfile": "My Profile", "EditProfile": "Edit Profile", "PrivateDetails": "Private Details"}, "CompanyInfo": {"Title": "Company Information", "ViewInfo": "View Company Info", "EditInfo": "Edit Company Info"}, "Chat": {"Title": "Cha<PERSON>", "Messages": "Messages", "NewMessage": "New Message", "ManageChats": "Manage Chats"}, "Notification": {"Title": "Notifications", "MyNotifications": "My Notifications", "SendNotification": "Send Notification"}, "Connection": {"Title": "Connection Management", "Groups": "Connection Groups", "CreateGroup": "Create Group", "UserConnections": "User Connections", "CreateConnection": "Create Connection"}, "UserSessions": {"Title": "User Sessions", "ActiveSessions": "Active Sessions", "ManageSessions": "Manage Sessions"}}