@model ColorOasisSystemWeb.Models.Inventory.InventoryItem

@{
    ViewData["Title"] = "Delete Inventory Item";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="card-title text-danger">
                        <i data-feather="alert-triangle" class="feather-icon me-2"></i>
                        @ViewData["Title"]
                    </h4>
                    <a asp-action="Index" class="btn btn-secondary">
                        <i data-feather="arrow-left" class="feather-icon me-2"></i>
                        Back to List
                    </a>
                </div>
                <div class="card-body">
                    <div class="alert alert-danger" role="alert">
                        <h5 class="alert-heading">
                            <i data-feather="alert-triangle" class="feather-icon me-2"></i>
                            Confirm Deletion
                        </h5>
                        <p>Are you sure you want to delete this inventory item? This action cannot be undone.</p>
                        <hr>
                        <p class="mb-0">
                            <strong>Warning:</strong> Deleting this item will also remove all associated inventory locations, transactions, and historical data.
                        </p>
                    </div>

                    <div class="row">
                        <!-- Item Information -->
                        <div class="col-md-6">
                            <h5 class="mb-3">Item Information</h5>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>SKU:</strong></td>
                                    <td>@Html.DisplayFor(model => model.SKU)</td>
                                </tr>
                                <tr>
                                    <td><strong>Code:</strong></td>
                                    <td>@Html.DisplayFor(model => model.Code)</td>
                                </tr>
                                <tr>
                                    <td><strong>Name:</strong></td>
                                    <td>@Html.DisplayFor(model => model.Name)</td>
                                </tr>
                                @if (!string.IsNullOrEmpty(Model.NameEn))
                                {
                                    <tr>
                                        <td><strong>Name (English):</strong></td>
                                        <td>@Html.DisplayFor(model => model.NameEn)</td>
                                    </tr>
                                }
                                <tr>
                                    <td><strong>Category:</strong></td>
                                    <td>@Html.DisplayFor(model => model.ServiceCategory.Name)</td>
                                </tr>
                                <tr>
                                    <td><strong>Type:</strong></td>
                                    <td>
                                        <span class="badge bg-info">@Html.DisplayFor(model => model.Type)</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Status:</strong></td>
                                    <td>
                                        @if (!Model.IsDeleted)
                                        {
                                            <span class="badge bg-success">Active</span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-secondary">Inactive</span>
                                        }
                                    </td>
                                </tr>
                            </table>
                        </div>

                        <!-- Current Stock Information -->
                        <div class="col-md-6">
                            <h5 class="mb-3">Current Stock Information</h5>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Unit of Measure:</strong></td>
                                    <td>@Html.DisplayFor(model => model.UnitOfMeasure)</td>
                                </tr>
                                <tr>
                                    <td><strong>Current Stock:</strong></td>
                                    <td>
                                        @{
                                            var totalStock = Model.Locations?.Sum(l => l.QuantityOnHand) ?? 0;
                                            var stockClass = totalStock > 0 ? "text-warning" : "text-success";
                                        }
                                        <span class="@stockClass">
                                            <strong>@totalStock @Model.UnitOfMeasure</strong>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Reserved Stock:</strong></td>
                                    <td>
                                        @{
                                            var reservedStock = Model.Locations?.Sum(l => l.QuantityReserved) ?? 0;
                                        }
                                        @reservedStock @Model.UnitOfMeasure
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Locations:</strong></td>
                                    <td>@(Model.Locations?.Count() ?? 0) location(s)</td>
                                </tr>
                                <tr>
                                    <td><strong>Standard Price:</strong></td>
                                    <td><strong>@Model.Price.ToString("C")</strong></td>
                                </tr>
                                <tr>
                                    <td><strong>Standard Cost:</strong></td>
                                    <td>@Model.StandardCost.ToString("C")</td>
                                </tr>
                                <tr>
                                    <td><strong>Item ID:</strong></td>
                                    <td>@Model.Id</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <!-- Stock Locations Warning -->
                    @if (Model.Locations?.Any() == true)
                    {
                        <div class="row">
                            <div class="col-12">
                                <div class="alert alert-warning" role="alert">
                                    <h6 class="alert-heading">
                                        <i data-feather="map-pin" class="feather-icon me-2"></i>
                                        Stock Locations to be Deleted
                                    </h6>
                                    <div class="table-responsive">
                                        <table class="table table-sm table-striped mb-0">
                                            <thead>
                                                <tr>
                                                    <th>Warehouse</th>
                                                    <th>Bin Location</th>
                                                    <th>On Hand</th>
                                                    <th>Reserved</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach (var location in Model.Locations)
                                                {
                                                    <tr>
                                                        <td>@location.Warehouse?.Name</td>
                                                        <td>@location.BinLocation</td>
                                                        <td>@location.QuantityOnHand @Model.UnitOfMeasure</td>
                                                        <td>@location.QuantityReserved @Model.UnitOfMeasure</td>
                                                    </tr>
                                                }
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }

                    <!-- Transaction History Warning -->
                    @if (Model.Transactions?.Any() == true)
                    {
                        <div class="row">
                            <div class="col-12">
                                <div class="alert alert-info" role="alert">
                                    <h6 class="alert-heading">
                                        <i data-feather="activity" class="feather-icon me-2"></i>
                                        Transaction History
                                    </h6>
                                    <p class="mb-0">
                                        This item has <strong>@Model.Transactions.Count()</strong> transaction record(s) that will also be deleted.
                                        The most recent transaction was on @Model.Transactions.OrderByDescending(t => t.TransactionDate).FirstOrDefault()?.TransactionDate.ToString("dd/MM/yyyy").
                                    </p>
                                </div>
                            </div>
                        </div>
                    }

                    <!-- Action Buttons -->
                    <div class="row">
                        <div class="col-12">
                            <form asp-action="Delete" method="post">
                                <input type="hidden" asp-for="Id" />
                                <div class="d-flex justify-content-end gap-2">
                                    <a asp-action="Index" class="btn btn-secondary">
                                        <i data-feather="x" class="feather-icon me-2"></i>
                                        Cancel
                                    </a>
                                    <a asp-action="Details" asp-route-id="@Model.Id" class="btn btn-info">
                                        <i data-feather="eye" class="feather-icon me-2"></i>
                                        View Details
                                    </a>
                                    <button type="submit" class="btn btn-danger">
                                        <i data-feather="trash-2" class="feather-icon me-2"></i>
                                        Delete Item
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Initialize Feather icons
        if (typeof feather !== 'undefined') {
            feather.replace();
        }

        // Add confirmation dialog
        $('form').on('submit', function(e) {
            if (!confirm('Are you absolutely sure you want to delete this inventory item? This action cannot be undone.')) {
                e.preventDefault();
                return false;
            }
        });
    </script>
}
