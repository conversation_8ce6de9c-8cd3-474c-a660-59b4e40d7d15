@model ColorOasisSystemWeb.Models.Purchasing.Supplier

@{
    ViewData["Title"] = "Delete Supplier";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="nxl-content">
    <!-- [ page-header ] start -->
    <div class="page-header">
        <div class="page-header-left d-flex align-items-center">
            <div class="page-header-title">
                <h5 class="m-b-10">Delete Supplier</h5>
            </div>
            <ul class="breadcrumb">
                <li class="breadcrumb-item"><a href="@Url.Action("Index", "Home")">Home</a></li>
                <li class="breadcrumb-item">ERP</li>
                <li class="breadcrumb-item">Purchasing</li>
                <li class="breadcrumb-item"><a href="@Url.Action("Index")">Suppliers</a></li>
                <li class="breadcrumb-item">Delete</li>
            </ul>
        </div>
        <div class="page-header-right ms-auto">
            <div class="page-header-right-items">
                <div class="d-flex align-items-center gap-2 page-header-right-items-wrapper">
                    <a href="@Url.Action("Index")" class="btn btn-outline-primary">
                        <i class="feather-arrow-left me-2"></i>
                        <span>Back to List</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
    <!-- [ page-header ] end -->

    <!-- [ Main Content ] start -->
    <div class="main-content">
        <div class="row">
            <div class="col-12">
                <div class="card stretch stretch-full">
                    <div class="card-header">
                        <h5 class="card-title text-danger">
                            <i class="feather-alert-triangle me-2"></i>
                            Confirm Deletion
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-danger">
                            <h6 class="alert-heading">Warning!</h6>
                            <p class="mb-0">
                                Are you sure you want to delete this supplier? This action cannot be undone.
                                All related data including purchase orders, contracts, and evaluations will be affected.
                            </p>
                        </div>

                        <!-- Supplier Information -->
                        <div class="row">
                            <div class="col-lg-6">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Supplier Code</label>
                                    <div class="fw-bold">@Model.Code</div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Supplier Name</label>
                                    <div class="fw-bold">@Model.Name</div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Contact Person</label>
                                    <div>@Model.ContactPerson</div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Email</label>
                                    <div>@Model.Email</div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Phone</label>
                                    <div>@Model.Phone</div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Status</label>
                                    <div>
                                        @switch (Model.Status)
                                        {
                                            case ColorOasisSystemWeb.Enums.SupplierStatus.Active:
                                                <span class="badge bg-soft-success text-success">Active</span>
                                                break;
                                            case ColorOasisSystemWeb.Enums.SupplierStatus.Inactive:
                                                <span class="badge bg-soft-secondary text-secondary">Inactive</span>
                                                break;
                                            case ColorOasisSystemWeb.Enums.SupplierStatus.Blacklisted:
                                                <span class="badge bg-soft-danger text-danger">Blacklisted</span>
                                                break;
                                            case ColorOasisSystemWeb.Enums.SupplierStatus.Pending:
                                                <span class="badge bg-soft-warning text-warning">Pending</span>
                                                break;
                                        }
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Supplier Type</label>
                                    <div><span class="badge bg-soft-info text-info">@Model.Type</span></div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Created Date</label>
                                    <div>@Model.CreatedDate.ToString("MMM dd, yyyy")</div>
                                </div>
                            </div>
                        </div>

                        <!-- Performance Summary -->
                        <div class="row">
                            <div class="col-12">
                                <h6 class="mb-3">Performance Summary</h6>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label text-muted">Quality Rating</label>
                                            <div class="d-flex align-items-center">
                                                @for (int i = 1; i <= 5; i++)
                                                {
                                                    if (i <= Model.QualityRating)
                                                    {
                                                        <i class="feather-star text-warning" style="fill: currentColor;"></i>
                                                    }
                                                    else
                                                    {
                                                        <i class="feather-star text-muted"></i>
                                                    }
                                                }
                                                <span class="ms-2">(@Model.QualityRating/5)</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label text-muted">Delivery Rating</label>
                                            <div class="d-flex align-items-center">
                                                @for (int i = 1; i <= 5; i++)
                                                {
                                                    if (i <= Model.DeliveryRating)
                                                    {
                                                        <i class="feather-star text-warning" style="fill: currentColor;"></i>
                                                    }
                                                    else
                                                    {
                                                        <i class="feather-star text-muted"></i>
                                                    }
                                                }
                                                <span class="ms-2">(@Model.DeliveryRating/5)</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label text-muted">Service Rating</label>
                                            <div class="d-flex align-items-center">
                                                @for (int i = 1; i <= 5; i++)
                                                {
                                                    if (i <= Model.ServiceRating)
                                                    {
                                                        <i class="feather-star text-warning" style="fill: currentColor;"></i>
                                                    }
                                                    else
                                                    {
                                                        <i class="feather-star text-muted"></i>
                                                    }
                                                }
                                                <span class="ms-2">(@Model.ServiceRating/5)</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Related Data Warning -->
                        <div class="alert alert-warning">
                            <h6 class="alert-heading">
                                <i class="feather-info me-2"></i>
                                Related Data Impact
                            </h6>
                            <p class="mb-2">Deleting this supplier may affect:</p>
                            <ul class="mb-0">
                                <li>Purchase Orders associated with this supplier</li>
                                <li>Supplier contracts and agreements</li>
                                <li>Performance evaluations and ratings</li>
                                <li>Financial records and payment history</li>
                            </ul>
                        </div>

                        <!-- Confirmation Form -->
                        <form asp-action="Delete" method="post" class="mt-4">
                            <input type="hidden" asp-for="Id" />
                            <div class="d-flex justify-content-end gap-2">
                                <a href="@Url.Action("Index")" class="btn btn-outline-secondary">
                                    <i class="feather-x me-2"></i>Cancel
                                </a>
                                <a href="@Url.Action("Details", new { id = Model.Id })" class="btn btn-outline-info">
                                    <i class="feather-eye me-2"></i>View Details
                                </a>
                                <button type="submit" class="btn btn-danger" onclick="return confirm('Are you absolutely sure you want to delete this supplier? This action cannot be undone.')">
                                    <i class="feather-trash-2 me-2"></i>Delete Supplier
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- [ Main Content ] end -->
</div>
