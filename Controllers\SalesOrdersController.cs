using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using ColorOasisSystemWeb.Authorization;

namespace ColorOasisSystemWeb.Controllers
{
    [Authorize]
    public class SalesOrdersController : Controller
    {
        [Authorize(Permissions.Sales.View)]
        public IActionResult Index()
        {
            ViewBag.Title = "Sales Orders";
            ViewBag.Message = "Manage sales orders, customer orders, and order fulfillment.";
            return View("~/Views/Shared/ComingSoon.cshtml");
        }

        [Authorize(Permissions.Sales.Create)]
        public IActionResult Create()
        {
            ViewBag.Title = "Create Sales Order";
            ViewBag.Message = "Create new sales order functionality will be implemented here.";
            return View("~/Views/Shared/ComingSoon.cshtml");
        }
    }
}
